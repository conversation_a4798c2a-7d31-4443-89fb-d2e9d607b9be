﻿using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Events.Create;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.EscalationMatrixLevel.Events.Create;

public class EscalationMatrixLevelCreatedEventHandlerTests
{
    private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
    private readonly Mock<ILoggedInUserService> _loggedInUserServiceMock;
    private readonly Mock<ILogger<EscalationMatrixLevelCreatedEventHandler>> _loggerMock;

    private readonly EscalationMatrixLevelCreatedEventHandler _handler;

    private readonly List<Domain.Entities.UserActivity> _userActivities;

    public EscalationMatrixLevelCreatedEventHandlerTests()
    {
        _userActivities = new List<Domain.Entities.UserActivity>();

        _userActivityRepoMock = EscalationMatrixLevelRepositoryMocks.CreateEscalationMatrixLevelEventRepository(_userActivities);
        _loggedInUserServiceMock = new Mock<ILoggedInUserService>();
        _loggerMock = new Mock<ILogger<EscalationMatrixLevelCreatedEventHandler>>();

        // Mock LoggedInUserService values
        _loggedInUserServiceMock.Setup(x => x.UserId).Returns("user-123");
        _loggedInUserServiceMock.Setup(x => x.LoginName).Returns("arif.dev");
        _loggedInUserServiceMock.Setup(x => x.CompanyId).Returns("company-789");
        _loggedInUserServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");
        _loggedInUserServiceMock.Setup(x => x.RequestedUrl).Returns("/api/escalation/create");

        _handler = new EscalationMatrixLevelCreatedEventHandler(
            _loggedInUserServiceMock.Object,
            _userActivityRepoMock.Object,
            _loggerMock.Object);
    }

    [Fact(DisplayName = "Handle_Should_Log_And_Save_UserActivity_When_EscalationMatrixLevelCreated")]
    public async Task Handle_Should_Log_And_Save_UserActivity_When_EscalationMatrixLevelCreated()
    {
        // Arrange
        var notification = new EscalationMatrixLevelCreatedEvent
        {
            EscLevName = "Level 1"
        };

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        Assert.Single(_userActivities); // userActivity added

        var savedActivity = _userActivities[0];
        Assert.Equal("user-123", savedActivity.UserId);
        Assert.Contains("Level 1", savedActivity.ActivityDetails);
        Assert.Equal(ActivityType.Create.ToString(), savedActivity.ActivityType);
        Assert.Equal(Modules.EscalationMatrixLevel.ToString(), savedActivity.Entity);

        // Optional: Verify logging
        _loggerMock.Verify(log => log.Log(
            LogLevel.Information,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) =>
                v.ToString()!.Contains("Escalation Matrix Level'Level 1' created successfully.")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Fact(DisplayName = "Handle_Should_Assign_NewGuid_When_UserId_Is_Null")]
    public async Task Handle_Should_Assign_NewGuid_When_UserId_Is_Null()
    {
        // Arrange
        var userActivities = new List<Domain.Entities.UserActivity>();
        var userActivityRepoMock = new Mock<IUserActivityRepository>();

        userActivityRepoMock.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync((Domain.Entities.UserActivity ua) =>
            {
                userActivities.Add(ua);
                return ua;
            });

        var loggedInUserServiceMock = new Mock<ILoggedInUserService>();
        loggedInUserServiceMock.Setup(x => x.UserId).Returns<string?>(null!);
        loggedInUserServiceMock.Setup(x => x.LoginName).Returns("arif.test");
        loggedInUserServiceMock.Setup(x => x.CompanyId).Returns("company-999");
        loggedInUserServiceMock.Setup(x => x.IpAddress).Returns("***********");
        loggedInUserServiceMock.Setup(x => x.RequestedUrl).Returns("/api/escalation/create");

        var loggerMock = new Mock<ILogger<EscalationMatrixLevelCreatedEventHandler>>();

        var handler = new EscalationMatrixLevelCreatedEventHandler(
            loggedInUserServiceMock.Object,
            userActivityRepoMock.Object,
            loggerMock.Object);

        var notification = new EscalationMatrixLevelCreatedEvent { EscLevName = "Level X" };

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        var activity = userActivities.FirstOrDefault();
        Assert.NotNull(activity);
        Assert.False(string.IsNullOrEmpty(activity.CreatedBy));
        Assert.False(string.IsNullOrEmpty(activity.LastModifiedBy));
        Assert.True(Guid.TryParse(activity.CreatedBy, out _)); // should be a valid GUID
        Assert.True(Guid.TryParse(activity.LastModifiedBy, out _));
        Assert.Contains("Level X", activity.ActivityDetails);
    }
}
