using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.InfraObjectSchedulerLogPagination;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class DrReadyFixture
{
    public PaginatedResult<InfraObjectSchedulerListVm> InfraObjectSchedulerPaginatedResult { get; }
    public PaginatedResult<InfraObjectSchedulerLogsListVm> InfraObjectSchedulerLogsPaginatedResult { get; }
    public CreateInfraObjectSchedulerCommand CreateInfraObjectSchedulerCommand { get; }
    public UpdateInfraObjectSchedulerCommand UpdateInfraObjectSchedulerCommand { get; }
    public UpdateInfraObjectSchedulerStatusCommand UpdateInfraObjectSchedulerStatusCommand { get; }
    public UpdateInfraObjectSchedulerStateCommand UpdateInfraObjectSchedulerStateCommand { get; }
    public GetInfraObjectSchedulerPaginatedListQuery GetInfraObjectSchedulerPaginatedListQuery { get; }
    public GetInfraObjectSchedulerLogsPaginationQuery GetInfraObjectSchedulerLogsPaginationQuery { get; }

    public DrReadyFixture()
    {
        var fixture = new Fixture();

        InfraObjectSchedulerPaginatedResult = fixture.Create<PaginatedResult<InfraObjectSchedulerListVm>>();
        InfraObjectSchedulerLogsPaginatedResult = fixture.Create<PaginatedResult<InfraObjectSchedulerLogsListVm>>();
        CreateInfraObjectSchedulerCommand = fixture.Create<CreateInfraObjectSchedulerCommand>();
        UpdateInfraObjectSchedulerCommand = fixture.Create<UpdateInfraObjectSchedulerCommand>();
        UpdateInfraObjectSchedulerStatusCommand = fixture.Create<UpdateInfraObjectSchedulerStatusCommand>();
        UpdateInfraObjectSchedulerStateCommand = fixture.Create<UpdateInfraObjectSchedulerStateCommand>();
        GetInfraObjectSchedulerPaginatedListQuery = fixture.Create<GetInfraObjectSchedulerPaginatedListQuery>();
        GetInfraObjectSchedulerLogsPaginationQuery = fixture.Create<GetInfraObjectSchedulerLogsPaginationQuery>();
    }

    public void Dispose()
    {

    }
}
