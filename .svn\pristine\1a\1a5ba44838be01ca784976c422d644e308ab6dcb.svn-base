using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Create;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Update;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetList;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Domain.ViewModels.SolutionHistoryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class SolutionHistoryControllerTests : IClassFixture<SolutionHistoryFixture>
{
    private readonly SolutionHistoryFixture _solutionHistoryFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly SolutionHistoryController _controller;

    public SolutionHistoryControllerTests(SolutionHistoryFixture solutionHistoryFixture)
    {
        _solutionHistoryFixture = solutionHistoryFixture;
        
        var testBuilder = new ControllerTestBuilder<SolutionHistoryController>();
        _controller = testBuilder.CreateController(
            _ => new SolutionHistoryController(),
            out _mediatorMock);
    }

    #region GetSolutionHistories Tests

    [Fact]
    public async Task GetSolutionHistories_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSolutionHistoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_solutionHistoryFixture.SolutionHistoryListVm);

        // Act
        var result = await _controller.GetSolutionHistories();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var solutionHistories = Assert.IsAssignableFrom<List<SolutionHistoryListVm>>(okResult.Value);
        Assert.Equal(5, solutionHistories.Count);
        Assert.All(solutionHistories, sh => Assert.NotNull(sh.ActionName));
        Assert.All(solutionHistories, sh => Assert.NotNull(sh.LoginName));
        Assert.All(solutionHistories, sh => Assert.NotNull(sh.Version));
        Assert.All(solutionHistories, sh => Assert.NotNull(sh.Properties));
        
        // Verify different solution types
        Assert.Contains(solutionHistories, sh => sh.Properties.Contains("\"type\": \"backup\""));
        Assert.Contains(solutionHistories, sh => sh.Properties.Contains("\"type\": \"loadbalancer\""));
        Assert.Contains(solutionHistories, sh => sh.Properties.Contains("\"type\": \"firewall\""));
        Assert.Contains(solutionHistories, sh => sh.Properties.Contains("\"type\": \"monitoring\""));
        Assert.Contains(solutionHistories, sh => sh.Properties.Contains("\"type\": \"deployment\""));
    }

    [Fact]
    public async Task GetSolutionHistories_ReturnsEmptyList_WhenNoHistoriesExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSolutionHistoryListQuery>(), default))
            .ReturnsAsync(new List<SolutionHistoryListVm>());

        // Act
        var result = await _controller.GetSolutionHistories();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<SolutionHistoryListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetSolutionHistories_ReturnsHistoriesWithDifferentSolutionTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSolutionHistoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_solutionHistoryFixture.SolutionHistoryListVm);

        // Act
        var result = await _controller.GetSolutionHistories();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var solutionHistories = Assert.IsAssignableFrom<List<SolutionHistoryListVm>>(okResult.Value);
        
        // Verify different action names
        Assert.Contains(solutionHistories, sh => sh.ActionName == "Database Backup Solution");
        Assert.Contains(solutionHistories, sh => sh.ActionName == "Load Balancer Configuration");
        Assert.Contains(solutionHistories, sh => sh.ActionName == "Firewall Rule Management");
        Assert.Contains(solutionHistories, sh => sh.ActionName == "System Monitoring Dashboard");
        Assert.Contains(solutionHistories, sh => sh.ActionName == "Automated Deployment Pipeline");
        
        // Verify different versions
        Assert.Contains(solutionHistories, sh => sh.Version == "1.0");
        Assert.Contains(solutionHistories, sh => sh.Version == "2.1");
        Assert.Contains(solutionHistories, sh => sh.Version == "1.5");
        Assert.Contains(solutionHistories, sh => sh.Version == "3.0");
        Assert.Contains(solutionHistories, sh => sh.Version == "2.3");
        
        // Verify different login names
        Assert.Contains(solutionHistories, sh => sh.LoginName == "<EMAIL>");
        Assert.Contains(solutionHistories, sh => sh.LoginName == "<EMAIL>");
        Assert.Contains(solutionHistories, sh => sh.LoginName == "<EMAIL>");
        Assert.Contains(solutionHistories, sh => sh.LoginName == "<EMAIL>");
        Assert.Contains(solutionHistories, sh => sh.LoginName == "<EMAIL>");
    }

    #endregion

    #region GetSolutionHistoryById Tests

    [Fact]
    public async Task GetSolutionHistoryById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _solutionHistoryFixture.SolutionHistoryDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSolutionHistoryDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetSolutionHistoryById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<SolutionHistoryDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Id, returnedDetail.Id);
        Assert.Equal(expectedDetail.ActionName, returnedDetail.ActionName);
        Assert.Equal(expectedDetail.LoginName, returnedDetail.LoginName);
        Assert.Equal(expectedDetail.Version, returnedDetail.Version);
        Assert.Equal(expectedDetail.Properties, returnedDetail.Properties);
        Assert.Equal(expectedDetail.Description, returnedDetail.Description);
        Assert.Equal(expectedDetail.Comments, returnedDetail.Comments);
        
        // Verify detailed properties
        Assert.Contains("\"databases\": [\"production\", \"staging\", \"development\"]", returnedDetail.Properties);
        Assert.Contains("\"backupLocation\": \"/backup/db\"", returnedDetail.Properties);
        Assert.Contains("\"verificationEnabled\": true", returnedDetail.Properties);
        Assert.Contains("\"parallelBackups\": 4", returnedDetail.Properties);
        Assert.Contains("\"encryptionAlgorithm\": \"AES-256\"", returnedDetail.Properties);
    }

    [Fact]
    public async Task GetSolutionHistoryById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetSolutionHistoryById("invalid-guid"));
    }

    [Fact]
    public async Task GetSolutionHistoryById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetSolutionHistoryById(""));
    }

    [Fact]
    public async Task GetSolutionHistoryById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSolutionHistoryDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("SolutionHistory", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetSolutionHistoryById(nonExistentId));
    }

    #endregion

    #region CreateSolutionHistory Tests

    [Fact]
    public async Task CreateSolutionHistory_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _solutionHistoryFixture.CreateSolutionHistoryCommand;
        var expectedResponse = _solutionHistoryFixture.CreateSolutionHistoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateSolutionHistory(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateSolutionHistoryResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.SolutionHistoryId);
    }

    [Fact]
    public async Task CreateSolutionHistory_WithCompleteSolutionData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateSolutionHistoryCommand
        {
            CompanyId = "COMP_NEW",
            LoginName = "<EMAIL>",
            NodeId = "NODE_NEW",
            ActionId = "ACTION_NEW",
            ActionName = "Disaster Recovery Solution",
            Properties = "{\"type\": \"disaster_recovery\", \"rto\": \"4hours\", \"rpo\": \"1hour\", \"sites\": [\"primary\", \"secondary\"], \"replication\": \"synchronous\", \"testing\": \"quarterly\"}",
            Version = "1.0",
            UpdaterId = "USER_ARCHITECT",
            Description = "Comprehensive disaster recovery solution for business continuity",
            Comments = "Initial implementation with cross-site replication and automated failover"
        };

        var expectedResponse = new CreateSolutionHistoryResponse
        {
            SolutionHistoryId = Guid.NewGuid().ToString(),
            Message = "Solution History has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateSolutionHistory(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateSolutionHistoryResponse>(createdResult.Value);
        Assert.Equal("Solution History has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.SolutionHistoryId);
    }

    #endregion

    #region UpdateSolutionHistory Tests

    [Fact]
    public async Task UpdateSolutionHistory_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _solutionHistoryFixture.UpdateSolutionHistoryCommand;
        var expectedResponse = _solutionHistoryFixture.UpdateSolutionHistoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateSolutionHistory(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateSolutionHistoryResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.SolutionHistoryId);
    }

    [Fact]
    public async Task UpdateSolutionHistory_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _solutionHistoryFixture.UpdateSolutionHistoryCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("SolutionHistory", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateSolutionHistory(command));
    }

    [Fact]
    public async Task UpdateSolutionHistory_WithUpdatedSolutionData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdateSolutionHistoryCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "COMP_001",
            LoginName = "<EMAIL>",
            NodeId = "NODE_001",
            ActionId = "ACTION_001",
            ActionName = "Enhanced Database Backup Solution",
            Properties = "{\"type\": \"backup\", \"frequency\": \"hourly\", \"retention\": \"60days\", \"compression\": true, \"encryption\": true, \"cloudBackup\": true, \"monitoring\": true}",
            Version = "2.0",
            UpdaterId = "USER_001",
            Description = "Enhanced database backup solution with cloud integration and advanced monitoring",
            Comments = "Major upgrade with cloud backup capabilities and real-time monitoring"
        };

        var expectedResponse = new UpdateSolutionHistoryResponse
        {
            SolutionHistoryId = command.Id,
            Message = "Solution History has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateSolutionHistory(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateSolutionHistoryResponse>(okResult.Value);
        Assert.Equal("Solution History has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.SolutionHistoryId);
    }

    #endregion

    #region GetSolutionHistoryByActionId Tests

    [Fact]
    public async Task GetSolutionHistoryByActionId_WithValidActionId_ReturnsOkResult()
    {
        // Arrange
        var validActionId = Guid.NewGuid().ToString();
        var expectedResult = _solutionHistoryFixture.SolutionHistoryByActionIdQueryVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSolutionHistoryByActionIdQuery>(q => q.ActionId == validActionId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetSolutionHistoryByActionId(validActionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<SolutionHistoryByActionIdQueryVm>>(okResult.Value);
        Assert.Equal(3, returnedResult.Count);
        Assert.All(returnedResult, sh => Assert.Equal("ACTION_001", sh.ActionId));
        Assert.All(returnedResult, sh => Assert.Equal("Database Backup Solution", sh.ActionName));

        // Verify version progression
        Assert.Contains(returnedResult, sh => sh.Version == "1.0");
        Assert.Contains(returnedResult, sh => sh.Version == "1.1");
        Assert.Contains(returnedResult, sh => sh.Version == "1.2");

        // Verify different comments for each version
        Assert.Contains(returnedResult, sh => sh.Comments == "First implementation");
        Assert.Contains(returnedResult, sh => sh.Comments == "Added compression and encryption");
        Assert.Contains(returnedResult, sh => sh.Comments == "Added verification and monitoring capabilities");
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_WithInvalidActionId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetSolutionHistoryByActionId("invalid-guid"));
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_WithEmptyActionId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetSolutionHistoryByActionId(""));
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_WithNonExistentActionId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentActionId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSolutionHistoryByActionIdQuery>(q => q.ActionId == nonExistentActionId), default))
            .ThrowsAsync(new NotFoundException("SolutionHistory", nonExistentActionId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetSolutionHistoryByActionId(nonExistentActionId));
    }

    [Fact]
    public async Task GetSolutionHistoryByActionId_ReturnsVersionHistory_InCorrectOrder()
    {
        // Arrange
        var actionId = Guid.NewGuid().ToString();
        var versionHistory = new List<SolutionHistoryByActionIdQueryVm>
        {
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_001",
                ActionId = actionId,
                ActionName = "Database Backup Solution",
                Version = "1.0",
                Properties = "{\"type\": \"backup\", \"version\": \"1.0\"}",
                Comments = "Initial version"
            },
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_002",
                ActionId = actionId,
                ActionName = "Database Backup Solution",
                Version = "1.1",
                Properties = "{\"type\": \"backup\", \"version\": \"1.1\"}",
                Comments = "Minor update"
            },
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_003",
                ActionId = actionId,
                ActionName = "Database Backup Solution",
                Version = "2.0",
                Properties = "{\"type\": \"backup\", \"version\": \"2.0\"}",
                Comments = "Major update"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSolutionHistoryByActionIdQuery>(q => q.ActionId == actionId), default))
            .ReturnsAsync(versionHistory);

        // Act
        var result = await _controller.GetSolutionHistoryByActionId(actionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<SolutionHistoryByActionIdQueryVm>>(okResult.Value);
        Assert.Equal(3, returnedResult.Count);

        // Verify version progression
        Assert.Equal("1.0", returnedResult[0].Version);
        Assert.Equal("1.1", returnedResult[1].Version);
        Assert.Equal("2.0", returnedResult[2].Version);

        // Verify comments progression
        Assert.Equal("Initial version", returnedResult[0].Comments);
        Assert.Equal("Minor update", returnedResult[1].Comments);
        Assert.Equal("Major update", returnedResult[2].Comments);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsSolutionHistoryCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
