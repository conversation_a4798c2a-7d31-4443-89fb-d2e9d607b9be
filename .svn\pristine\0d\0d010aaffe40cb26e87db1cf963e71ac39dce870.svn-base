﻿using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.Features.Report.Commands.Delete;
using ContinuityPatrol.Application.Features.Report.Commands.Update;
using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList;
using ContinuityPatrol.Application.Features.Report.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetList;
using ContinuityPatrol.Application.Features.Report.Queries.GetNames;
using ContinuityPatrol.Application.Features.Report.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Report.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ReportsController : CommonBaseController
{
    [HttpGet]
    public async Task<ActionResult<List<ReportListVm>>> GetReports()
    {
        Logger.LogDebug("Get All Reports");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllReportsNameCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetReportListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetReportListQuery()));
    }
   
    [HttpGet("{id}", Name = "GetReport")]
    public async Task<ActionResult<ReportDetailVm>> GetReportById(string id)
    {
        Logger.LogDebug($"Get Report Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetReportDetailQuery { Id = id }));
    }

    [HttpPost]
    public async Task<ActionResult<CreateReportResponse>> CreateReport([FromBody] CreateReportCommand createReportCommand)
    {
        Logger.LogDebug($"Create Report '{createReportCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateReport), await Mediator.Send(createReportCommand));
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult<DeleteReportResponse>> DeleteReport(string id)
    {
        Logger.LogDebug($"Delete Report Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteReportCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    public async Task<ActionResult<PaginatedResult<ReportListVm>>> GetPaginatedReports([FromQuery] GetReportPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Report Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpPut]
    public async Task<ActionResult<UpdateReportResponse>> UpdateReport([FromBody] UpdateReportCommand updateReportCommand)
    {
        Logger.LogDebug($"Update Report '{updateReportCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateReportCommand));
    }

    [HttpGet("names")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<GetReportNameVm>>> GetReportNames()
    {
        Logger.LogDebug("Get All Report Names");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllReportsNameCacheKey, () => Mediator.Send(new GetReportNameQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetReportNameQuery()));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsReportNameExist(string reportName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(reportName, "Report Name");

        Logger.LogDebug($"Check Name Exists Detail by Report Name '{reportName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetReportNameUniqueQuery { ReportName = reportName, ReportId = id }));
    }

    [Route("rposla-report")]
    [HttpGet]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<object>> GetRpoSlaReportByInfraObjectId(string infraObjectId,
       string type, string reportStartDate, string reportEndDate)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "MonitorServiceLog infraObjectId");

        Logger.LogDebug($"Get MonitorServiceLog Detail by Id '{infraObjectId}' and {type}");

        return Ok(await Mediator.Send(new GetRPOSLAReportQuery
        {
            InfraObjectId = infraObjectId,
            Type = type,
            ReportStartDate = reportStartDate,
            ReportEndDate = reportEndDate
        }));
    }

    [HttpGet("rto-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<RTOReports>> GetRtoReportByWorkflowOperationId(
        string workflowOperationId)
    {
        Logger.LogDebug($"Get RTO Report by WorkflowOperationId :{workflowOperationId}.");

        return Ok(await Mediator.Send(new GetRtoReportQuery { Id = workflowOperationId }));
    }

    [Route("licenseid"), HttpGet]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<LicenseReport>> GetLicenseReportById(string? licenseId)
    {
        //Guard.Against.InvalidGuidOrEmpty(licenseId, "License Id");

        Logger.LogDebug($"Get LicenseReport by License Id'{licenseId}'");

        return Ok(await Mediator.Send(new GetLicenseReportQuery { LicenseId = licenseId}));
    }

    [Route("License-Utilization-Report-Business-Service"), HttpGet]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<LicenseReportByBusinessServiceReport>> GetLicenseReportByBusinessServiceId(string? businessServiceId)
    {
        //Guard.Against.InvalidGuidOrEmpty(licenseId, "License Id");

        Logger.LogDebug($"Get LicenseReport by businessService Id'{businessServiceId}'");

        return Ok(await Mediator.Send(new GetLicenseReportByBusinessServiceQuery { BusinessServiceId = businessServiceId}));
    }

    [HttpGet]
    [Route("drready-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<DrReadyStatusReport>> GetDrReadyStatusReportByBusinessServiceId(string? businessServiceId)
    {
        Logger.LogDebug($"Get DRReadyStatus For DRReady Report by BusinessServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new DRReadyStatusForDRReadyReportQuery { BusinessServiceId = businessServiceId }));
    }

    [HttpGet]
    [Route("drready-execution-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<DRReadyExecutionReport>> GetDrReadyExecutionLogReport(string? businessServiceId,string? startTime, string? endTime)
    {
        Logger.LogDebug($"Get DRReadyLog execution log report by startTime '{startTime}' and endTime '{endTime}'");

        return Ok(await Mediator.Send(new GetDRReadyExecutionReportQuery { BusinessServiceId = businessServiceId, StartTime = startTime, EndTime = endTime }));
    }

    [HttpGet("businessServices-summary-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<BusinessServiceSummaryReport>> GetBusinessServiceSummaryReport()
    {
        Logger.LogDebug("Get BusinessServiceSummaryReport");

        return Ok(await Mediator.Send(new GetBusinessServiceSummaryReportQuery()));
    }

    [HttpGet("InfraObject-Summary-Report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<InfraObjectSummaryReport>> GetInfraObjectSummaryReport(string? businessServiceId)
    {
        Logger.LogDebug("Get InfraObjectSummaryReport");

        return Ok(await Mediator.Send(new GetInfraObjectSummaryReportQuery { BusinessServiceId = businessServiceId }));
    }
    [HttpGet("rposla-deviation-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<GetRpoSlaDeviationReportVm>> GetByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(string? businessServiceId, string? infraObjectId, string? createDate, string? lastModifiedDate)
    {
        Logger.LogDebug("Get RpoSlaDeviation Reports By CreatedDate and EndDate");

        return Ok(await Mediator.Send(new GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery { BusinessServiceId = businessServiceId,InfraObjectId=infraObjectId, CreatedDate = createDate, LastModifiedDate = lastModifiedDate }));
    }
    [Route("infra-Configuration-report"), HttpGet]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<InfraReport>> GetInfraObjectConfigurationReport(string infraObjectId)
    {
        Logger.LogDebug($"Get InfraObject Configuration Report '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetInfraObjectConfigurationReportQuery { InfraObjectId = infraObjectId }));
    }

    [HttpGet("user-activity-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<UserActivityReport>> GetUserActivityReport(string? userId, string createDate, string lastModifiedDate)
    {
        Logger.LogDebug($"Get UserActivity Report by UserId '{userId}' and startDate '{createDate}' and endDate'{lastModifiedDate}'");

        return Ok(await Mediator.Send(new UserActivityReportQuery { UserId = userId, CreatedDate = createDate, LastModifiedDate = lastModifiedDate }));
    }

    [HttpGet("drdrill-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<DrDrillReport>>> GetDrDrillReportByWorkflowOperationId(
       string workflowOperationId,string? runMode)
    {
        Logger.LogDebug($"Get DrDrill Report by WorkflowOperationId :{workflowOperationId}. And RunMode '{runMode}'");

        return Ok(await Mediator.Send(new GetWorkflowOperationDrDrillReportQuery { Id = workflowOperationId ,RunMode = runMode}));
    }

    [HttpGet("runbook-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<GetRunBookReportVm>> GetRunbookReportByWorkflowId(string workflowId)
    {
        Logger.LogDebug($"Get RunBook Report by WorkflowId :{workflowId}.");

        return Ok(await Mediator.Send(new GetRunBookReportQuery { WorkflowId = workflowId }));
    }

    [HttpGet("bulkImport-report")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetBulkImportReportVm>> GetBulkImportReport(string operationId)
    {
        Logger.LogDebug("Get All BulkImportReport");

        return Ok(await Mediator.Send(new GetBulkImportReportListQuery{Id = operationId }));
    }

    [HttpGet("airGap-report-id")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<AirGapLogReportVm>> GetAirGapReport(string airGapId)
    {
        Logger.LogDebug($"Get air gap Report by airGapId :{airGapId}.");

        return Ok(await Mediator.Send(new GetAirGapQuery { Id = airGapId }));
    }

    [HttpGet("drift-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<DriftReportVm>> GetDriftReport(string startDate, string endDate, string infraObjectId, string driftStatusId)
    {
        Logger.LogDebug($"Get All DriftReport by infraObject id : {infraObjectId}");

        return Ok(await Mediator.Send(new GetDriftReportQuery { Id = infraObjectId, StartDate = startDate, EndDate = endDate, DriftStatus = driftStatusId }));
    }

    [HttpGet("drift-report-infraobject-id")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<DriftEventReportVm>>> GetDriftReportInfraId(string startDate, string endDate)
    {
        Logger.LogDebug($"Get All DriftReportInfraObject by sart date : {startDate} end date : {endDate}");

        return Ok(await Mediator.Send(new GetDriftReportInfraObjectQuery { StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("drift-report-status")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<DriftEventReportVm>>> GetDriftReportStatus(string startDate, string endDate, string infraObjectId)
    {
        Logger.LogDebug($"Get All DriftReport Status by infraObject id : {infraObjectId}");

        return Ok(await Mediator.Send(new GetDriftReportStatusQuery { Id = infraObjectId, StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("airgap-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<GetAirGapListVm>>> GetAirGapList(string startDate, string endDate)
    {
        Logger.LogDebug($"Get All AirGAalist by start date : {startDate} end date : {endDate}");

        return Ok(await Mediator.Send(new GetAirGapListQuery { StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("cyber-snap-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<List<GetCyberSnapsListVm>>> GetCyberSnapsList(string startDate, string endDate)
    {
        Logger.LogDebug($"Get all Cyber Snaps List by start date : {startDate} end date : {endDate}");

        return Ok(await Mediator.Send(new GetCyberSnapsListQuery { StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("cyber-snap-report-tagname")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<GetCyberSnapsReportVm>> GetCyberSnapsBySnapTagName(string cyberSnapTagName, string startDate, string endDate)
    {
        Logger.LogDebug($"Get details CyberSnaps by Snap Id : {cyberSnapTagName}");

        return Ok(await Mediator.Send(new GetCyberSnapsReportQuery { CyberSnapTagName = cyberSnapTagName, StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("infraobject-scheduler-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<GetResiliencyReadinessSchedulerLogReportVm>> GetInfraObjectSchedulerLogList(string startDate, string endDate)
    {
        Logger.LogDebug($"Get InfraObject Scheduler Logs List by : {startDate},{endDate}");

        return Ok(await Mediator.Send(new GetInfraObjectSchedulerLogsReportQuery { StartDate = startDate, EndDate = endDate }));
    }

    [HttpGet("schedule-workflowaction-result-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<SchedulerWorkflowActionResultsVm>> GetScheduleWorkflowActionResultReport(string workflowId, string infraReferenceId)
    {
        Logger.LogDebug($"Get Schedule Workflow Action Results Report List by : {workflowId}");

        return Ok(await Mediator.Send(new GetSchedulerWorkflowActionResultsQuery { WorkflowId = workflowId, InfraObjectId = infraReferenceId }));
    }

    [HttpGet("cg-execution-report")]
    [Authorize(Policy = Permissions.Reports.View)]
    public async Task<ActionResult<CGExecutionReportResultVm>> GetCGExecutionReport(string workflowOperationId)
    {
        Logger.LogDebug($"Get CG Execution Report List by : {workflowOperationId}");

        return Ok(await Mediator.Send(new GetCGExecutionReportQuery { WorkflowOperationId = workflowOperationId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllReportsNameCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}