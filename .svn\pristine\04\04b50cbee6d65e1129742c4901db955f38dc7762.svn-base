﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Drift.Controllers;
using AutoMapper;
using ContinuityPatrol.Shared.Services.Provider;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Drift.Controllers
{
    public class DriftParameterControllerTests
    {
        private readonly DriftParameterController _controller;

        private readonly Mock<IPublisher> _publisher =new();
        private readonly Mock<IDataProvider> _dataProvider = new();
        private readonly Mock<IMapper> _mapper = new();
        private readonly Mock<ILogger<DriftParameterController>> _logger = new();

        public DriftParameterControllerTests()
        {
            _controller = new DriftParameterController(/*_publisher.Object,*/_logger.Object,_dataProvider.Object,_mapper.Object, _publisher.Object);
        }

        [Fact]
        public async Task List_Returns_ViewResult()
        {
            var result = await _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
 
        }
    }
}
