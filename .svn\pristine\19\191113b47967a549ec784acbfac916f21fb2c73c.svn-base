﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Wrapper;
namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Queries;

public class GetLicenseInfoPaginatedListQueryHandlerTests
{
    private readonly Mock<ILicenseInfoRepository> _mockRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetLicenseInfoPaginatedListQueryHandler _handler;

    public GetLicenseInfoPaginatedListQueryHandlerTests()
    {
        _mockRepository = new Mock<ILicenseInfoRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetLicenseInfoPaginatedListQueryHandler(_mockRepository.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task Handle_Should_Call_PaginatedListAllAsync_When_Entity_Is_Null()
    {
        // Arrange
        var request = new GetLicenseInfoPaginatedListQuery
        {
            Entity = null,
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var licenseInfoData = new PaginatedResult<Domain.Entities.LicenseInfo>(
            true,
            new List<Domain.Entities.LicenseInfo> { new Domain.Entities.LicenseInfo { Id = 1, LicenseId = "License A" } },
            null,
            1,
            1,
            10
        );

        var mappedData = new PaginatedResult<LicenseInfoListVm>(
            true,
            new List<LicenseInfoListVm> { new LicenseInfoListVm { Id = "1", LicenseId = "License A" } },
            null,
            1,
            1,
            10
        );

        _mockRepository
            .Setup(x => x.PaginatedListAllAsync(
                request.PageNumber,
                request.PageSize,
                It.IsAny<LicenseInfoFilterSpecification>(),
                request.SortColumn,
                request.SortOrder))
            .ReturnsAsync(licenseInfoData);

        _mockMapper
            .Setup(x => x.Map<PaginatedResult<LicenseInfoListVm>>(licenseInfoData))
            .Returns(mappedData);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("License A", result.Data[0].LicenseId);

        _mockRepository.Verify(x => x.PaginatedListAllAsync(
            request.PageNumber,
            request.PageSize,
            It.IsAny<LicenseInfoFilterSpecification>(),
            request.SortColumn,
            request.SortOrder), Times.Once);
    }
    [Fact]
    public void AssignValues_ShouldSetAllPropertiesCorrectly()
    {
        // Arrange
        var query = new GetLicenseInfoPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 20,
            Entity = "DC-001"
        };
        var q1= query with { };
        Assert.NotNull(q1);
        // Assert
        Assert.Equal(2, query.PageNumber);
        Assert.Equal(20, query.PageSize);
        Assert.Equal("DC-001", query.Entity);
    }
    [Fact]
    public async Task Handle_Should_Call_GetLicenseInfoEntityQueryable_When_Entity_Is_Not_Null()
    {
        // Arrange
        var request = new GetLicenseInfoPaginatedListQuery
        {
            Entity = "TestEntity",
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var licenseInfoData = new PaginatedResult<Domain.Entities.LicenseInfo>(
            true,
            new List<Domain.Entities.LicenseInfo> { new Domain.Entities.LicenseInfo { Id = 2, LicenseId = "License B" } },
            null,
            1,
            1,
            10
        );

        var mappedData = new PaginatedResult<LicenseInfoListVm>(
            true,
            new List<LicenseInfoListVm> { new LicenseInfoListVm { Id = "2", LicenseId = "License B" } },
            null,
            1,
            1,
            10
        );

        _mockRepository
            .Setup(x => x.GetLicenseInfoEntityQueryable(
                request.Entity,
                request.PageNumber,
                request.PageSize,
                It.IsAny<LicenseInfoFilterSpecification>(),
                request.SortColumn,
                request.SortOrder))
            .ReturnsAsync(licenseInfoData);

        _mockMapper
            .Setup(x => x.Map<PaginatedResult<LicenseInfoListVm>>(licenseInfoData))
            .Returns(mappedData);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("License B", result.Data[0].LicenseId);

        _mockRepository.Verify(x => x.GetLicenseInfoEntityQueryable(
            request.Entity,
            request.PageNumber,
            request.PageSize,
            It.IsAny<LicenseInfoFilterSpecification>(),
            request.SortColumn,
            request.SortOrder), Times.Once);
    }
}
