using ContinuityPatrol.Application.Features.DataLag.Commands.Create;
using ContinuityPatrol.Application.Features.DataLag.Commands.Delete;
using ContinuityPatrol.Application.Features.DataLag.Commands.Update;
using ContinuityPatrol.Application.Features.DataLag.Events.Create;
using ContinuityPatrol.Application.Features.DataLag.Events.Delete;
using ContinuityPatrol.Application.Features.DataLag.Events.Update;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class DataLagFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<DataLag> DataLags { get; set; }
    public List<DataLag> InvalidDataLags { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // Commands
    public CreateDataLagCommand CreateDataLagCommand { get; set; }
    public UpdateDataLagCommand UpdateDataLagCommand { get; set; }
    public DeleteDataLagCommand DeleteDataLagCommand { get; set; }

    // Queries
    public GetDataLagDetailQuery GetDataLagDetailQuery { get; set; }
    public GetDataLagListQuery GetDataLagListQuery { get; set; }
    public GetDataLagDetailByBusinessServiceIdQuery GetDataLagDetailByBusinessServiceIdQuery { get; set; }

    // Events
    public DataLagCreatedEvent DataLagCreatedEvent { get; set; }
    public DataLagUpdatedEvent DataLagUpdatedEvent { get; set; }
    public DataLagDeletedEvent DataLagDeletedEvent { get; set; }

    // View Models
    public DataLagListVm DataLagListVm { get; set; }
    public DataLagDetailVm DataLagDetailVm { get; set; }

    // Responses
    public CreateDataLagResponse CreateDataLagResponse { get; set; }
    public UpdateDataLagResponse UpdateDataLagResponse { get; set; }
    public DeleteDataLagResponse DeleteDataLagResponse { get; set; }

    public DataLagFixture()
    {
        try
        {
            // Create test data using AutoFixture
            DataLags = AutoDataLagFixture.Create<List<DataLag>>();
            InvalidDataLags = AutoDataLagFixture.Create<List<DataLag>>();
            UserActivities = AutoDataLagFixture.Create<List<UserActivity>>();

            // Set invalid data lags to inactive
            foreach (var invalidDataLag in InvalidDataLags)
            {
                invalidDataLag.IsActive = false;
            }

            // Commands
            CreateDataLagCommand = AutoDataLagFixture.Create<CreateDataLagCommand>();
            UpdateDataLagCommand = AutoDataLagFixture.Create<UpdateDataLagCommand>();
            DeleteDataLagCommand = AutoDataLagFixture.Create<DeleteDataLagCommand>();

            // Set command IDs to match existing entities
            if (DataLags.Any())
            {
                UpdateDataLagCommand.Id = DataLags.First().ReferenceId;
                DeleteDataLagCommand.Id = DataLags.First().ReferenceId;
            }

            // Queries
            GetDataLagDetailQuery = AutoDataLagFixture.Create<GetDataLagDetailQuery>();
            GetDataLagListQuery = AutoDataLagFixture.Create<GetDataLagListQuery>();
            GetDataLagDetailByBusinessServiceIdQuery = AutoDataLagFixture.Create<GetDataLagDetailByBusinessServiceIdQuery>();

            // Set query IDs to match existing entities
            if (DataLags.Any())
            {
                GetDataLagDetailQuery.Id = DataLags.First().ReferenceId;
                GetDataLagDetailByBusinessServiceIdQuery.BusinessServiceId = DataLags.First().BusinessServiceId;
            }

            // Events
            DataLagCreatedEvent = AutoDataLagFixture.Create<DataLagCreatedEvent>();
            DataLagUpdatedEvent = AutoDataLagFixture.Create<DataLagUpdatedEvent>();
            DataLagDeletedEvent = AutoDataLagFixture.Create<DataLagDeletedEvent>();

            // View Models
            DataLagListVm = AutoDataLagFixture.Create<DataLagListVm>();
            DataLagDetailVm = AutoDataLagFixture.Create<DataLagDetailVm>();

            // Responses
            CreateDataLagResponse = AutoDataLagFixture.Create<CreateDataLagResponse>();
            UpdateDataLagResponse = AutoDataLagFixture.Create<UpdateDataLagResponse>();
            DeleteDataLagResponse = AutoDataLagFixture.Create<DeleteDataLagResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            DataLags = new List<DataLag>();
            InvalidDataLags = new List<DataLag>();
            UserActivities = new List<UserActivity>();
            CreateDataLagCommand = new CreateDataLagCommand();
            UpdateDataLagCommand = new UpdateDataLagCommand();
            DeleteDataLagCommand = new DeleteDataLagCommand();
            GetDataLagDetailQuery = new GetDataLagDetailQuery();
            GetDataLagListQuery = new GetDataLagListQuery();
            GetDataLagDetailByBusinessServiceIdQuery = new GetDataLagDetailByBusinessServiceIdQuery();
            DataLagCreatedEvent = new DataLagCreatedEvent();
            DataLagUpdatedEvent = new DataLagUpdatedEvent();
            DataLagDeletedEvent = new DataLagDeletedEvent();
            DataLagListVm = new DataLagListVm();
            DataLagDetailVm = new DataLagDetailVm();
            CreateDataLagResponse = new CreateDataLagResponse();
            UpdateDataLagResponse = new UpdateDataLagResponse();
            DeleteDataLagResponse = new DeleteDataLagResponse();
        }

        // Configure AutoMapper for DataLag mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<DataLagProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoDataLagFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize DataLag entity
            fixture.Customize<DataLag>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "Test Business Service")
                .With(b => b.TotalBusinessFunction, 100)
                .With(b => b.BFAvailable, 85)
                .With(b => b.BFImpact, 10)
                .With(b => b.BFExceed, 3)
                .With(b => b.BFUnConfigured, 2)
                .With(b => b.BFNotAvailable, 0)
                .With(b => b.BFThreshold, 8)
                .With(b => b.TotalInfraObject, 200)
                .With(b => b.InfraAvailable, 180)
                .With(b => b.InfraImpact, 15)
                .With(b => b.InfraExceed, 5)
                .With(b => b.InfraNotAvailable, 0)
                .With(b => b.InfraThreshold, 12)
                .With(b => b.InfraUnderConfigured, 0));

            // Customize CreateDataLagCommand
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateDataLagCommand>(p => p.BusinessServiceName, 100));
            fixture.Customize<CreateDataLagCommand>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "New Test Business Service")
                .With(b => b.TotalBusinessFunction, 150)
                .With(b => b.BFAvailable, 130)
                .With(b => b.BFImpact, 12)
                .With(b => b.BFExceed, 5)
                .With(b => b.BFUnConfigured, 3)
                .With(b => b.BFNotAvailable, 0)
                .With(b => b.BFThreshold, 10)
                .With(b => b.TotalInfraObject, 300)
                .With(b => b.InfraAvailable, 270)
                .With(b => b.InfraImpact, 20)
                .With(b => b.InfraExceed, 8)
                .With(b => b.InfraNotAvailable, 2)
                .With(b => b.InfraThreshold, 15)
                .With(b => b.InfraUnderConfigured, 0));

            // Customize UpdateDataLagCommand
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateDataLagCommand>(p => p.BusinessServiceName, 100));
            fixture.Customize<UpdateDataLagCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "Updated Test Business Service")
                .With(b => b.TotalBusinessFunction, 120)
                .With(b => b.BFAvailable, 100)
                .With(b => b.BFImpact, 15)
                .With(b => b.BFExceed, 4)
                .With(b => b.BFUnConfigured, 1)
                .With(b => b.BFNotAvailable, 0)
                .With(b => b.BFThreshold, 12)
                .With(b => b.TotalInfraObject, 250)
                .With(b => b.InfraAvailable, 220)
                .With(b => b.InfraImpact, 18)
                .With(b => b.InfraExceed, 7)
                .With(b => b.InfraNotAvailable, 5)
                .With(b => b.InfraThreshold, 16)
                .With(b => b.InfraUnderConfigured, 0));

            // Customize DeleteDataLagCommand
            fixture.Customize<DeleteDataLagCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize GetDataLagDetailQuery
            fixture.Customize<GetDataLagDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize GetDataLagDetailByBusinessServiceIdQuery
            fixture.Customize<GetDataLagDetailByBusinessServiceIdQuery>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString()));

            // Customize Events
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataLagCreatedEvent>(p => p.Name, 100));
            fixture.Customize<DataLagCreatedEvent>(c => c
                .With(b => b.Name, "Created Test Business Service"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataLagUpdatedEvent>(p => p.Name, 100));
            fixture.Customize<DataLagUpdatedEvent>(c => c
                .With(b => b.Name, "Updated Test Business Service"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DataLagDeletedEvent>(p => p.Name, 100));
            fixture.Customize<DataLagDeletedEvent>(c => c
                .With(b => b.Name, "Deleted Test Business Service"));

            // Customize View Models
            fixture.Customize<DataLagListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "List Test Business Service")
                .With(b => b.TotalBusinessFunction, 100)
                .With(b => b.BFAvailable, 85)
                .With(b => b.BFImpact, 10)
                .With(b => b.BFExceed, 3)
                .With(b => b.TotalInfraObject, 200)
                .With(b => b.InfraAvailable, 180)
                .With(b => b.InfraImpact, 15)
                .With(b => b.InfraExceed, 5));

            fixture.Customize<DataLagDetailVm>(c => c
               // .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "Detail Test Business Service")
                .With(b => b.TotalBusinessFunction, 100)
                .With(b => b.BFAvailable, 85)
                .With(b => b.BFImpact, 10)
                .With(b => b.BFExceed, 3)
                .With(b => b.BFUnConfigured, 2)
                .With(b => b.BFNotAvailable, 0)
                .With(b => b.BFThreshold, 8)
                .With(b => b.TotalInfraObject, 200)
                .With(b => b.InfraAvailable, 180)
                .With(b => b.InfraImpact, 15)
                .With(b => b.InfraExceed, 5)
                .With(b => b.InfraNotAvailable, 0)
                .With(b => b.InfraThreshold, 12)
                .With(b => b.InfraUnderConfigured, 0));

            // Customize Responses
            fixture.Customize<CreateDataLagResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Success, true)
                .With(b => b.Message, "DataLag created successfully"));

            fixture.Customize<UpdateDataLagResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Success, true)
                .With(b => b.Message, "DataLag updated successfully"));

            fixture.Customize<DeleteDataLagResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Success, true)
                .With(b => b.Message, "DataLag deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "DataLag")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
