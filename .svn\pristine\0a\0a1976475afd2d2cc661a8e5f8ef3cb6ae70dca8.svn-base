﻿using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Services.Api.Impl.Admin;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Admin;

public class DynamicDashboardMapServiceTests : IClassFixture<DynamicDashboardMapFixture>
{
    private readonly DynamicDashboardMapFixture _fixture;
    private readonly Mock<IBaseClient> _clientMock;
    private readonly DynamicDashboardMapService _service;

    public DynamicDashboardMapServiceTests(DynamicDashboardMapFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new DynamicDashboardMapService(_clientMock.Object);
    }

    [Fact]
    public async Task GetDynamicDashboardMapList_ReturnsExpectedList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<DynamicDashboardMapListVm>>(It.IsAny<RestRequest>(), "GetDynamicDashboardMapList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetDynamicDashboardMapList();

        Assert.Equal(_fixture.ListVm.Count, result.Count);
    }

    [Fact]
    public async Task CreateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task DeleteAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync(_fixture.Id);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsDetail()
    {
        _clientMock.Setup(x => x.Get<DynamicDashboardMapDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId(_fixture.Id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DetailVm.Id, result.Id);
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_ReturnsTrue()
    {
        _clientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsDynamicDashboardMapNameExist(_fixture.Name, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_ReturnsExpectedPage()
    {
        _clientMock.Setup(x => x.Get<PaginatedResult<DynamicDashboardMapListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedListVm);

        var result = await _service.GetPaginatedDynamicDashboardMaps(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedListVm.TotalCount, result.TotalCount);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_ReturnsExpectedResult()
    {
        var expectedVm = _fixture.ListVm.First();
        _clientMock.Setup(x => x.Get<DynamicDashboardMapListVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(expectedVm);

        var result = await _service.GetDefaultDashboardByRoleId(_fixture.RoleId);

        Assert.Equal(expectedVm.Id, result.Id);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_ReturnsExpectedResult()
    {
        var expectedVm = _fixture.ListVm.First();
        _clientMock.Setup(x => x.Get<DynamicDashboardMapListVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(expectedVm);

        var result = await _service.GetDefaultDashboardByUserId(_fixture.UserId);

        Assert.Equal(expectedVm.Id, result.Id);
    }
}
