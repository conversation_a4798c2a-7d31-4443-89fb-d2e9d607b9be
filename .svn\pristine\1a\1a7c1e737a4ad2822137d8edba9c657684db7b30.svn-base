﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Queries;

public class GetDataSetRunQueryHandlerTests : IClassFixture<DataSetFixture>
{
    private readonly DataSetFixture _dataSetFixture;
    private readonly Mock<IDataSetRepository> _mockDataSetRepository;
    private readonly GetDataSetRunQueryHandler _handler;

    public GetDataSetRunQueryHandlerTests(DataSetFixture dataSetFixture)
    {
        _dataSetFixture = dataSetFixture;

        _mockDataSetRepository = DataSetRepositoryMocks.GetDataSetRepository(_dataSetFixture.DataSets);

        _handler = new GetDataSetRunQueryHandler(_dataSetFixture.Mapper, _mockDataSetRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_DataSetRunQueryVm_When_Valid()
    {
        var testQuery = "SELECT * FROM TestTable WHERE IsActive = 1";
        var expectedTableValue = "{'data': [{'id': 1, 'name': 'Test'}]}";
        var mockTableResult = new DataSetRunQueryVm { TableValue = expectedTableValue };

        _mockDataSetRepository.Setup(x => x.GetTableJson(testQuery))
            .ReturnsAsync(mockTableResult);

        var result = await _handler.Handle(new GetDataSetRunQuery { Table = testQuery }, CancellationToken.None);

        result.ShouldBeOfType<DataSetRunQueryVm>();
        result.TableValue.ShouldBe(expectedTableValue);
    }

    [Fact]
    public async Task Handle_Return_MappedResult_When_ValidQuery()
    {
        var testQuery = "SELECT COUNT(*) FROM Users";
        var mockTableResult = new DataSetRunQueryVm { TableValue = "{'count': 100}" };

        _mockDataSetRepository.Setup(x => x.GetTableJson(testQuery))
            .ReturnsAsync(mockTableResult);

        var result = await _handler.Handle(new GetDataSetRunQuery { Table = testQuery }, CancellationToken.None);

        result.ShouldNotBeNull();
        result.TableValue.ShouldBe("{'count': 100}");
    }

    [Fact]
    public async Task Handle_Call_GetTableJson_OneTime()
    {
        var testQuery = "SELECT * FROM Products";
        var mockTableResult = new DataSetRunQueryVm { TableValue = "{}" };

        _mockDataSetRepository.Setup(x => x.GetTableJson(testQuery))
            .ReturnsAsync(mockTableResult);

        await _handler.Handle(new GetDataSetRunQuery { Table = testQuery }, CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetTableJson(testQuery), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyTableValue_When_NoData()
    {
        var testQuery = "SELECT * FROM EmptyTable";
        var mockTableResult = new DataSetRunQueryVm { TableValue = string.Empty };

        _mockDataSetRepository.Setup(x => x.GetTableJson(testQuery))
            .ReturnsAsync(mockTableResult);

        var result = await _handler.Handle(new GetDataSetRunQuery { Table = testQuery }, CancellationToken.None);

        result.TableValue.ShouldBe(string.Empty);
    }

    [Fact]
    public async Task Handle_Handle_ComplexQuery_Successfully()
    {
        var complexQuery = @"SELECT u.Name, u.Email, COUNT(o.Id) as OrderCount
                            FROM Users u
                            LEFT JOIN Orders o ON u.Id = o.UserId
                            WHERE u.IsActive = 1
                            GROUP BY u.Name, u.Email
                            ORDER BY OrderCount DESC";
        var expectedResult = "{'columns': ['Name', 'Email', 'OrderCount'], 'rows': []}";
        var mockTableResult = new DataSetRunQueryVm { TableValue = expectedResult };

        _mockDataSetRepository.Setup(x => x.GetTableJson(complexQuery))
            .ReturnsAsync(mockTableResult);

        var result = await _handler.Handle(new GetDataSetRunQuery { Table = complexQuery }, CancellationToken.None);

        result.ShouldNotBeNull();
        result.TableValue.ShouldBe(expectedResult);
        _mockDataSetRepository.Verify(x => x.GetTableJson(complexQuery), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_JsonTableValue_When_ValidData()
    {
        var testQuery = "SELECT Id, Name, CreatedDate FROM DataSets";
        var jsonTableValue = @"{
            'columns': ['Id', 'Name', 'CreatedDate'],
            'rows': [
                {'Id': 1, 'Name': 'Dataset1', 'CreatedDate': '2024-01-01'},
                {'Id': 2, 'Name': 'Dataset2', 'CreatedDate': '2024-01-02'}
            ]
        }";
        var mockTableResult = new DataSetRunQueryVm { TableValue = jsonTableValue };

        _mockDataSetRepository.Setup(x => x.GetTableJson(testQuery))
            .ReturnsAsync(mockTableResult);

        var result = await _handler.Handle(new GetDataSetRunQuery { Table = testQuery }, CancellationToken.None);

        result.TableValue.ShouldBe(jsonTableValue);
    }
}
