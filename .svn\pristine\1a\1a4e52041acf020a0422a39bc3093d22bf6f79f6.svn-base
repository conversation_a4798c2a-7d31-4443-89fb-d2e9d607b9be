﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;

public class GetWorkflowOperationPaginatedListQueryHandler : IRequestHandler<GetWorkflowOperationPaginatedListQuery,
    PaginatedResult<WorkflowOperationListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public GetWorkflowOperationPaginatedListQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
    }

    public async Task<PaginatedResult<WorkflowOperationListVm>> Handle(GetWorkflowOperationPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new WorkflowOperationFilterSpecification(request.SearchString);

        var queryable = await _workflowOperationRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var workflowOperationList = _mapper.Map<PaginatedResult<WorkflowOperationListVm>>(queryable);

        return workflowOperationList;
    }
}