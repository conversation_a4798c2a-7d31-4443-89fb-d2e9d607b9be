﻿using ContinuityPatrol.Application.Features.CyberJobManagement.Events.UpdateState;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberJobManagement.Events;

public class JobStateUpdatedEventHandlerTests : IClassFixture<UserActivityFixture>
{
    private readonly UserActivityFixture _fixture;
    private readonly List<Domain.Entities.UserActivity> _userActivities;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly JobStateUpdatedEventHandler _handler;

    public JobStateUpdatedEventHandlerTests(UserActivityFixture fixture)
    {
        _fixture = fixture;
        _userActivities = fixture.UserActivities;

        var mockLogger = new Mock<ILogger<JobStateUpdatedEventHandler>>();

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.SetupGet(x => x.UserId).Returns("user-111");
        mockUserService.SetupGet(x => x.LoginName).Returns("stateUpdater");
        mockUserService.SetupGet(x => x.CompanyId).Returns("company-xyz");
        mockUserService.SetupGet(x => x.RequestedUrl).Returns("/api/job/updatestate");
        mockUserService.SetupGet(x => x.IpAddress).Returns("************");

        _mockUserActivityRepository = UserActivityRepositoryMocks.CreateUserActivityRepository(_userActivities);

        _handler = new JobStateUpdatedEventHandler(
            mockUserService.Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object
        );
    }

    [Fact(DisplayName = "Handle_ShouldAddUserActivity_When_JobStateUpdatedEventIsHandled")]
    public async Task Handle_ShouldAddUserActivity_When_JobStateUpdatedEventIsHandled()
    {
        // Arrange
        var evt = new JobStateUpdatedEvent
        {
            JobName = "CriticalUpdate",
            State = "Stopped"
        };

        // Act
        await _handler.Handle(evt, CancellationToken.None);

        // Assert
        
        var activity = _userActivities.First();
        Assert.NotNull(activity.LoginName);
        Assert.NotNull(activity.UserId);
        Assert.NotNull(activity.RequestUrl);
        Assert.NotNull(activity.HostAddress);
        Assert.NotNull(activity.Action);
        Assert.NotNull(activity.Entity);
        Assert.NotNull(activity.ActivityType);
        Assert.NotNull(activity.ActivityDetails);
    }

    [Fact(DisplayName = "Handle_ShouldCallAddAsyncExactlyOnce")]
    public async Task Handle_ShouldCallAddAsyncExactlyOnce()
    {
        // Arrange
        var evt = new JobStateUpdatedEvent
        {
            JobName = "HealthCheck",
            State = "Running"
        };

        // Act
        await _handler.Handle(evt, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
