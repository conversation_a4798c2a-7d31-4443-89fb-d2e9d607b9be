using AutoFixture;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Create;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Update;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetList;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SolutionHistoryModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class SolutionHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<SolutionHistory> SolutionHistories { get; set; }
    public List<SolutionHistory> InvalidSolutionHistories { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<SolutionHistoryListVm> SolutionHistoryListVm { get; }
    public SolutionHistoryDetailVm SolutionHistoryDetailVm { get; }
    public List<SolutionHistoryByActionIdQueryVm> SolutionHistoryByActionIdQueryVm { get; }

    // Commands
    public CreateSolutionHistoryCommand CreateSolutionHistoryCommand { get; set; }
    public UpdateSolutionHistoryCommand UpdateSolutionHistoryCommand { get; set; }

    // Queries
    public GetSolutionHistoryDetailQuery GetSolutionHistoryDetailQuery { get; set; }
    public GetSolutionHistoryListQuery GetSolutionHistoryListQuery { get; set; }
    public GetSolutionHistoryByActionIdQuery GetSolutionHistoryByActionIdQuery { get; set; }

    // Responses
    public CreateSolutionHistoryResponse CreateSolutionHistoryResponse { get; set; }
    public UpdateSolutionHistoryResponse UpdateSolutionHistoryResponse { get; set; }

    public SolutionHistoryFixture()
    {
        try
        {
            // Create test data using AutoFixture
            SolutionHistories = AutoSolutionHistoryFixture.Create<List<SolutionHistory>>();
            InvalidSolutionHistories = AutoSolutionHistoryFixture.Create<List<SolutionHistory>>();
            UserActivities = AutoSolutionHistoryFixture.Create<List<UserActivity>>();

            // Set invalid solution histories to inactive
            foreach (var invalidHistory in InvalidSolutionHistories)
            {
                invalidHistory.IsActive = false;
            }

            // Commands
            CreateSolutionHistoryCommand = AutoSolutionHistoryFixture.Create<CreateSolutionHistoryCommand>();
            UpdateSolutionHistoryCommand = AutoSolutionHistoryFixture.Create<UpdateSolutionHistoryCommand>();

            // Set command IDs to match existing entities
            if (SolutionHistories.Any())
            {
                UpdateSolutionHistoryCommand.Id = SolutionHistories.First().ReferenceId;
            }

            // Queries
            GetSolutionHistoryDetailQuery = AutoSolutionHistoryFixture.Create<GetSolutionHistoryDetailQuery>();
            GetSolutionHistoryListQuery = AutoSolutionHistoryFixture.Create<GetSolutionHistoryListQuery>();
            GetSolutionHistoryByActionIdQuery = AutoSolutionHistoryFixture.Create<GetSolutionHistoryByActionIdQuery>();

            // Set query IDs to match existing entities
            if (SolutionHistories.Any())
            {
                GetSolutionHistoryDetailQuery.Id = SolutionHistories.First().ReferenceId;
                GetSolutionHistoryByActionIdQuery.ActionId = SolutionHistories.First().ActionId;
            }

            // Responses
            CreateSolutionHistoryResponse = AutoSolutionHistoryFixture.Create<CreateSolutionHistoryResponse>();
            UpdateSolutionHistoryResponse = AutoSolutionHistoryFixture.Create<UpdateSolutionHistoryResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            SolutionHistories = new List<SolutionHistory>();
            InvalidSolutionHistories = new List<SolutionHistory>();
            UserActivities = new List<UserActivity>();
            CreateSolutionHistoryCommand = new CreateSolutionHistoryCommand();
            UpdateSolutionHistoryCommand = new UpdateSolutionHistoryCommand();
            GetSolutionHistoryDetailQuery = new GetSolutionHistoryDetailQuery();
            GetSolutionHistoryListQuery = new GetSolutionHistoryListQuery();
            GetSolutionHistoryByActionIdQuery = new GetSolutionHistoryByActionIdQuery();
            CreateSolutionHistoryResponse = new CreateSolutionHistoryResponse();
            UpdateSolutionHistoryResponse = new UpdateSolutionHistoryResponse();
        }

        // Configure View Models
        SolutionHistoryListVm = new List<SolutionHistoryListVm>
        {
            new SolutionHistoryListVm
            {
                Id = "SH_001",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_001",
                ActionId = "ACTION_001",
                ActionName = "Database Backup Solution",
                Properties = "{\"type\": \"backup\", \"frequency\": \"daily\", \"retention\": \"30days\", \"compression\": true, \"encryption\": true}",
                Version = "1.0",
                UpdaterId = "USER_001",
                Description = "Automated database backup solution for critical systems",
                Comments = "Initial implementation with encryption and compression"
            },
            new SolutionHistoryListVm
            {
                Id = "SH_002",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_002",
                ActionId = "ACTION_002",
                ActionName = "Load Balancer Configuration",
                Properties = "{\"type\": \"loadbalancer\", \"algorithm\": \"round-robin\", \"healthCheck\": true, \"timeout\": 30, \"retries\": 3}",
                Version = "2.1",
                UpdaterId = "USER_002",
                Description = "High availability load balancer configuration",
                Comments = "Updated with health check improvements"
            },
            new SolutionHistoryListVm
            {
                Id = "SH_003",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_003",
                ActionId = "ACTION_003",
                ActionName = "Firewall Rule Management",
                Properties = "{\"type\": \"firewall\", \"rules\": 150, \"protocols\": [\"TCP\", \"UDP\", \"ICMP\"], \"logging\": true, \"alerting\": true}",
                Version = "1.5",
                UpdaterId = "USER_003",
                Description = "Comprehensive firewall rule management system",
                Comments = "Enhanced with real-time alerting capabilities"
            },
            new SolutionHistoryListVm
            {
                Id = "SH_004",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_004",
                ActionId = "ACTION_004",
                ActionName = "System Monitoring Dashboard",
                Properties = "{\"type\": \"monitoring\", \"metrics\": [\"CPU\", \"Memory\", \"Disk\", \"Network\"], \"alerts\": true, \"retention\": \"90days\"}",
                Version = "3.0",
                UpdaterId = "USER_004",
                Description = "Real-time system monitoring and alerting dashboard",
                Comments = "Major version upgrade with enhanced visualization"
            },
            new SolutionHistoryListVm
            {
                Id = "SH_005",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_005",
                ActionId = "ACTION_005",
                ActionName = "Automated Deployment Pipeline",
                Properties = "{\"type\": \"deployment\", \"stages\": [\"build\", \"test\", \"deploy\"], \"rollback\": true, \"notifications\": true}",
                Version = "2.3",
                UpdaterId = "USER_005",
                Description = "CI/CD pipeline for automated application deployment",
                Comments = "Added rollback capabilities and enhanced notifications"
            }
        };

        SolutionHistoryDetailVm = new SolutionHistoryDetailVm
        {
            Id = "SH_001",
            CompanyId = "COMP_001",
            LoginName = "<EMAIL>",
            NodeId = "NODE_001",
            ActionId = "ACTION_001",
            ActionName = "Database Backup Solution",
            Properties = "{\"type\": \"backup\", \"frequency\": \"daily\", \"retention\": \"30days\", \"compression\": true, \"encryption\": true, \"databases\": [\"production\", \"staging\", \"development\"], \"backupLocation\": \"/backup/db\", \"verificationEnabled\": true, \"parallelBackups\": 4, \"maxRetryAttempts\": 3, \"notificationEmail\": \"<EMAIL>\", \"scheduleTime\": \"02:00\", \"compressionLevel\": \"high\", \"encryptionAlgorithm\": \"AES-256\"}",
            Version = "1.0",
            UpdaterId = "USER_001",
            Description = "Automated database backup solution for critical systems with comprehensive verification and monitoring",
            Comments = "Initial implementation with encryption and compression. Includes automated verification and email notifications for backup status."
        };

        SolutionHistoryByActionIdQueryVm = new List<SolutionHistoryByActionIdQueryVm>
        {
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_001",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_001",
                ActionId = "ACTION_001",
                ActionName = "Database Backup Solution",
                Properties = "{\"type\": \"backup\", \"version\": \"1.0\"}",
                Version = "1.0",
                UpdaterId = "USER_001",
                Description = "Initial database backup solution",
                Comments = "First implementation"
            },
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_006",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_001",
                ActionId = "ACTION_001",
                ActionName = "Database Backup Solution",
                Properties = "{\"type\": \"backup\", \"version\": \"1.1\"}",
                Version = "1.1",
                UpdaterId = "USER_001",
                Description = "Updated database backup solution",
                Comments = "Added compression and encryption"
            },
            new SolutionHistoryByActionIdQueryVm
            {
                Id = "SH_007",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                NodeId = "NODE_001",
                ActionId = "ACTION_001",
                ActionName = "Database Backup Solution",
                Properties = "{\"type\": \"backup\", \"version\": \"1.2\"}",
                Version = "1.2",
                UpdaterId = "USER_001",
                Description = "Enhanced database backup solution",
                Comments = "Added verification and monitoring capabilities"
            }
        };

        // Configure AutoMapper for SolutionHistory mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<SolutionHistoryProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoSolutionHistoryFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize SolutionHistory entity
            fixture.Customize<SolutionHistory>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.CompanyId, "COMP_001")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.NodeId, "NODE_001")
                .With(b => b.ActionId, "ACTION_001")
                .With(b => b.ActionName, "Test Solution")
                .With(b => b.Properties, "{\"type\": \"test\", \"status\": \"active\"}")
                .With(b => b.Version, "1.0")
                .With(b => b.UpdaterId, "USER_001")
                .With(b => b.Description, "Test solution description")
                .With(b => b.Comments, "Test comments"));

            // Customize CreateSolutionHistoryCommand
            fixture.Customize<CreateSolutionHistoryCommand>(c => c
                .With(b => b.CompanyId, "COMP_NEW")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.NodeId, "NODE_NEW")
                .With(b => b.ActionId, "ACTION_NEW")
                .With(b => b.ActionName, "New Solution")
                .With(b => b.Properties, "{\"type\": \"new\", \"status\": \"created\"}")
                .With(b => b.Version, "1.0")
                .With(b => b.UpdaterId, "USER_NEW")
                .With(b => b.Description, "New solution description")
                .With(b => b.Comments, "New solution comments"));

            // Customize UpdateSolutionHistoryCommand
            fixture.Customize<UpdateSolutionHistoryCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.CompanyId, "COMP_001")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.NodeId, "NODE_001")
                .With(b => b.ActionId, "ACTION_001")
                .With(b => b.ActionName, "Updated Solution")
                .With(b => b.Properties, "{\"type\": \"updated\", \"status\": \"modified\"}")
                .With(b => b.Version, "2.0")
                .With(b => b.UpdaterId, "USER_001")
                .With(b => b.Description, "Updated solution description")
                .With(b => b.Comments, "Updated solution comments"));

            // Customize Queries
            fixture.Customize<GetSolutionHistoryDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetSolutionHistoryByActionIdQuery>(c => c
                .With(b => b.ActionId, "ACTION_001"));

            // Customize Responses
            fixture.Customize<CreateSolutionHistoryResponse>(c => c
                .With(b => b.SolutionHistoryId, Guid.NewGuid().ToString())
                .With(b => b.Message, "Solution History has been created successfully"));

            fixture.Customize<UpdateSolutionHistoryResponse>(c => c
                .With(b => b.SolutionHistoryId, Guid.NewGuid().ToString())
                .With(b => b.Message, "Solution History has been updated successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "SolutionHistory")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
