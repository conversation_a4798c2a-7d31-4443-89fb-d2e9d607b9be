using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserLoginFixture : IDisposable
{
    public List<UserLogin> UserLoginPaginationList { get; set; }
    public List<UserLogin> UserLoginList { get; set; }
    public UserLogin UserLoginDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string TestUserId = "USER_123";
    public const string TestSessionId = "SESSION_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserLoginFixture()
    {
        var fixture = new Fixture();

        UserLoginList = fixture.Create<List<UserLogin>>();

        UserLoginPaginationList = fixture.CreateMany<UserLogin>(20).ToList();

        UserLoginDto = fixture.Create<UserLogin>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public UserLogin CreateUserLogin(
        string userId = TestUserId,
        int invalidLoginAttempt = 0,
        DateTime? lastLoginDate = null,
        string lastLoginIp = "*************",
        int lastAlertId = 0,
        DateTime? lastPasswordChanged = null,
        string sessionId = TestSessionId,
        bool isProperLoggedOut = true,
        DateTime? lastLoggedOutDate = null,
        bool isActive = true)
    {
        return new UserLogin
        {
            UserId = userId,
            InvalidLoginAttempt = invalidLoginAttempt,
            LastLoginDate = lastLoginDate ?? DateTime.Now.AddDays(-1),
            LastLoginIp = lastLoginIp,
            LastAlertId = lastAlertId,
            LastPasswordChanged = lastPasswordChanged ?? DateTime.Now.AddDays(-30),
            SessionId = sessionId,
            IsProperLoggedOut = isProperLoggedOut,
            LastLoggedOutDate = lastLoggedOutDate ?? DateTime.Now.AddHours(-2),
            IsActive = isActive
        };
    }

    public List<UserLogin> CreateMultipleUserLogins(
        int count,
        bool isActive = true)
    {
        var userLogins = new List<UserLogin>();
        for (int i = 1; i <= count; i++)
        {
            userLogins.Add(CreateUserLogin(
                userId: $"USER_{i:D3}",
                invalidLoginAttempt: i % 5,
                lastLoginIp: $"192.168.1.{100 + i}",
                lastAlertId: i,
                sessionId: $"SESSION_{i:D3}",
                isActive: isActive
            ));
        }
        return userLogins;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
