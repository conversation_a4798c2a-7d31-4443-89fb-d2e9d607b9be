﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Queries;

public class GetDataSetByIdQueryHandlerTests : IClassFixture<DataSetFixture>
{
    private readonly DataSetFixture _dataSetFixture;
    private readonly Mock<IDataSetRepository> _mockDataSetRepository;
    private readonly GetDataSetByIdQueryHandler _handler;
    private readonly GetDataSetByIdQueryHandler _invalidHandler;

    public GetDataSetByIdQueryHandlerTests(DataSetFixture dataSetFixture)
    {
        _dataSetFixture = dataSetFixture;

        _mockDataSetRepository = DataSetRepositoryMocks.GetDataSetRepository(_dataSetFixture.DataSets);

        _handler = new GetDataSetByIdQueryHandler(_mockDataSetRepository.Object);

        var mockInvalidDataSetRepository = DataSetRepositoryMocks.GetDataSetEmptyRepository();
        _invalidHandler = new GetDataSetByIdQueryHandler(mockInvalidDataSetRepository.Object);

        _dataSetFixture.DataSets[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _dataSetFixture.DataSets[0].IsActive = true;
        _dataSetFixture.DataSets[0].StoredQuery = "SELECT * FROM TestTable WHERE IsActive = 1";
    }

    [Fact]
    public async Task Handle_Return_DataSetById_When_Valid()
    {
        var testDataSetId = _dataSetFixture.DataSets[0].ReferenceId;
        var expectedQueryResult = "{'data': [{'id': 1, 'name': 'Test'}]}";

        _mockDataSetRepository.Setup(x => x.GetTableJson(It.IsAny<string>()))
            .ReturnsAsync(new DataSetRunQueryVm { TableValue = expectedQueryResult });

        var result = await _handler.Handle(new GetDataSetByIdQuery { Id = testDataSetId }, CancellationToken.None);

        result.ShouldBeOfType<GetDataSetByIdVm>();
        result.QueryResult.ShouldBe(expectedQueryResult);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidDataSetId()
    {
        var invalidId = "invalid-id-12345";

        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(new GetDataSetByIdQuery { Id = invalidId }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("DataSet");
        exceptionDetails.Message.ShouldContain(invalidId);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_DataSetNotFound()
    {
        var nonExistentId = "non-existent-id-12345";

        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() =>
            _invalidHandler.Handle(new GetDataSetByIdQuery { Id = nonExistentId }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("DataSet");
        exceptionDetails.Message.ShouldContain(nonExistentId);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_DataSetIsInactive()
    {
        _dataSetFixture.DataSets[0].IsActive = false;
        var inactiveDataSetId = _dataSetFixture.DataSets[0].ReferenceId;

        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(new GetDataSetByIdQuery { Id = inactiveDataSetId }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("DataSet");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OneTime()
    {
        var testDataSetId = _dataSetFixture.DataSets[0].ReferenceId;

        _mockDataSetRepository.Setup(x => x.GetTableJson(It.IsAny<string>()))
            .ReturnsAsync(new DataSetRunQueryVm { TableValue = "{}" });

        await _handler.Handle(new GetDataSetByIdQuery { Id = testDataSetId }, CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetByReferenceIdAsync(testDataSetId), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_GetTableJson_OneTime_With_StoredQuery()
    {
        var testDataSetId = _dataSetFixture.DataSets[0].ReferenceId;
        var expectedStoredQuery = _dataSetFixture.DataSets[0].StoredQuery;

        _mockDataSetRepository.Setup(x => x.GetTableJson(It.IsAny<string>()))
            .ReturnsAsync(new DataSetRunQueryVm { TableValue = "{}" });

        await _handler.Handle(new GetDataSetByIdQuery { Id = testDataSetId }, CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetTableJson(expectedStoredQuery), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_QueryResult_From_TableValue()
    {
        var testDataSetId = _dataSetFixture.DataSets[0].ReferenceId;
        var expectedTableValue = "{'columns': ['id', 'name'], 'rows': [{'id': 1, 'name': 'Test Dataset'}]}";

        _mockDataSetRepository.Setup(x => x.GetTableJson(It.IsAny<string>()))
            .ReturnsAsync(new DataSetRunQueryVm { TableValue = expectedTableValue });

        var result = await _handler.Handle(new GetDataSetByIdQuery { Id = testDataSetId }, CancellationToken.None);

        result.QueryResult.ShouldBe(expectedTableValue);
    }

    [Fact]
    public async Task Handle_Return_EmptyQueryResult_When_NoData()
    {
        var testDataSetId = _dataSetFixture.DataSets[0].ReferenceId;

        _mockDataSetRepository.Setup(x => x.GetTableJson(It.IsAny<string>()))
            .ReturnsAsync(new DataSetRunQueryVm { TableValue = string.Empty });

        var result = await _handler.Handle(new GetDataSetByIdQuery { Id = testDataSetId }, CancellationToken.None);

        result.QueryResult.ShouldBe(string.Empty);
    }
}
