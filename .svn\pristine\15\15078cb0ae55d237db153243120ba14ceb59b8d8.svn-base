﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.UpdateLog;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupListByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Services.Db.Impl.Orchestration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Orchestration;

public class WorkflowOperationGroupServiceTests : BaseServiceTestSetup<WorkflowOperationGroupService>, IClassFixture<WorkflowOperationGroupServiceFixture>
{
    private readonly WorkflowOperationGroupServiceFixture _fixture;

    public WorkflowOperationGroupServiceTests(WorkflowOperationGroupServiceFixture fixture)
    {
        InitializeService(accessor => new WorkflowOperationGroupService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateWorkflowOperationGroupResponse
        {
            Message = "Created",
            WorkflowOperationId = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Success()
    {
        var response = new DeleteWorkflowOperationGroupResponse
        {
            Message = "Deleted"
        };
        MediatorMock.Setup(m => m.Send(It.IsAny<DeleteWorkflowOperationGroupCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(Guid.NewGuid().ToString());

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowOperationGroupListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.OperationGroupList);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupList();

        Assert.Equal(_fixture.OperationGroupList.Count, result.Count);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupById_Should_Return_Data()
    {
        _fixture.DetailQuery.Id = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowOperationGroupDetailQuery>(q => q.Id == _fixture.DetailQuery.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailResponse);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupById(_fixture.DetailQuery.Id);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateWorkflowOperationGroupResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.UpdateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task IsWorkflowOperationGroupNameExist_Should_Return_True()
    {
        MediatorMock.Setup(m => m.Send(
                It.Is<GetWorkflowOperationGroupNameUniqueQuery>(q => q.WorkflowOperationGroupName == _fixture.UniqueQuery.WorkflowOperationGroupName),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsWorkflowOperationGroupNameExist(_fixture.UniqueQuery.WorkflowOperationGroupName, null);

        Assert.True(result);
    }

    [Fact]
    public async Task GetOperationGroupByWorkflowIdAndOperationId_Should_Return_Data()
    {
        var workflowId = Guid.NewGuid().ToString();
        var operationId = Guid.NewGuid().ToString();
        var expected = new List<WorkflowOperationGroupListByWorkflowIdVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowOperationGroupListByWorkflowIdQuery>(
            q => q.WorkflowId == workflowId && q.WorkflowOperationId == operationId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetOperationGroupByWorkflowIdAndOperationId(workflowId, operationId);

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupByInfraObjectId_Should_Return_Data()
    {
        var id = Guid.NewGuid().ToString();
        var expected = new List<WorkflowOperationGroupByInfraObjectIdVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowOperationGroupByInfraObjectIdQuery>(q => q.InfraObjectId == id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupByInfraObjectId(id);

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupByNodeId_Should_Return_Data()
    {
        var nodeId = Guid.NewGuid().ToString();
        var expected = new List<WorkflowOperationGroupByNodeIdVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowOperationGroupByNodeIdQuery>(q => q.NodeId == nodeId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupByNodeId(nodeId);

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupByRunningUserId_Should_Return_Data()
    {
        var userId = "user-123";
        var expected = new List<WorkflowOperationGroupRunningUserVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowOperationGroupRunningUserDetailQuery>(q => q.UserId == userId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupByRunningUserId(userId);

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupByWorkflowOperationId_Should_Return_Data()
    {
        var id = Guid.NewGuid().ToString();
        var expected = new List<GetByWorkflowOperationIdVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetByWorkflowOperationIdQuery>(q => q.WorkflowOperationId == id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupByWorkflowOperationId(id);

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupNames_Should_Return_List()
    {
        var expected = new List<WorkflowOperationGroupNameVm> { new() };

        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowOperationGroupNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupNames();

        Assert.Single(result);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupRunningList_Should_Return_List()
    {
        var expected = new List<WorkflowOperationGroupRunningStatusVm> { new() };

        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowOperationGroupRunningStatusQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupRunningList();

        Assert.Single(result);
    }

    [Fact]
    public async Task GetLogDataByGroupId_Should_Return_List()
    {
        var groupId = Guid.NewGuid().ToString();
        var expected = new List<GetLogByGroupIdVm> { new() };

        MediatorMock.Setup(m => m.Send(It.Is<GetLogByGroupIdQuery>(q => q.GroupId == groupId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetLogDataByGroupId(groupId);

        Assert.Single(result);
    }

    [Fact]
    public async Task CheckWindowsServiceConnection_Should_Return_Status()
    {
        // Arrange
        var expected = new GetWorkflowServiceResponse
        {
            ActiveNodes = new List<string> { "Node1", "Node2" }
        };

        MediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowServiceStatusQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        // Act
        var result = await ServiceUnderTest.CheckWindowsServiceConnection("some-type");

        // Assert
        Assert.NotNull(result);
        Assert.IsType<GetWorkflowServiceResponse>(result);
        Assert.Contains("Node1", result.ActiveNodes); 
    }

    [Fact]
    public async Task GetPaginatedWorkflowOperationGroup_Should_Return_Data()
    {
        var query = new GetWorkflowOperationGroupPaginatedListQuery();
        var expected = new PaginatedResult<WorkflowOperationGroupListVm>
        {
            Data = new List<WorkflowOperationGroupListVm> { new() },
            TotalCount = 1
        };

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetPaginatedWorkflowOperationGroup(query);

        Assert.Single(result.Data);
    }

    [Fact]
    public async Task GetWorkflowOperationGroupRunningStatusList_Should_Return_Data()
    {
        var expected = new List<ProfileRunningCountListVm> { new() };

        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowOperationGroupRunningStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.GetWorkflowOperationGroupRunningStatusList();

        Assert.Single(result);
    }

    [Fact]
    public async Task UpdateOperationGroupLog_Should_Return_Response()
    {
        var command = new UpdateOperationGroupLogCommand { WorkflowName = "TestWorkflow" };
        var expected = new UpdateOperationGroupLogResponse { Message = "Updated" };

        MediatorMock.Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expected);

        var result = await ServiceUnderTest.UpdateOperationGroupLog(command);

        Assert.Equal("Updated", result.Message);
    }
}
