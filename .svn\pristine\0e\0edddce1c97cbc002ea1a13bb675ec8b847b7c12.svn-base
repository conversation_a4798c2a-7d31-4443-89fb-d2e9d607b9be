﻿using ContinuityPatrol.Application.Features.UserGroup.Commands.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.UserGroup.Validators;

public class CreateUserGroupValidatorTests
{
    private readonly Mock<IUserGroupRepository> _repo = new();

    private CreateUserGroupValidator CreateSut() => new(_repo.Object);

    [Fact]
    public async Task Validate_Should_Pass_When_Input_Is_Valid_And_Name_Not_UniqueCheck_Fails()
    {
        // Arrange
        var cmd = new CreateUserGroupCommand
        {
            GroupName = "ValidGroup1",
            GroupDescription = "Some description"
        };

        // Validator expects GroupName() to return TRUE => it returns !IsGroupNameUnique
        // So, to PASS, IsGroupNameUnique must be FALSE (name is NOT unique in DB).
        _repo.Setup(r => r.IsGroupNameUnique(cmd.GroupName)).ReturnsAsync(false);

        var sut = CreateSut();

        // Act
        var result = await sut.TestValidateAsync(cmd);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public async Task Validate_Should_Fail_When_GroupName_Is_Null_Or_Empty(string name)
    {
        var cmd = new CreateUserGroupCommand { GroupName = name };

        var sut = CreateSut();

        var result = await sut.TestValidateAsync(cmd);

        result.ShouldHaveValidationErrorFor(c => c.GroupName);
    }

    [Theory]
    [InlineData("ab")] // < 3
    [InlineData("a")]
    public async Task Validate_Should_Fail_When_GroupName_Less_Than_3(string name)
    {
        var cmd = new CreateUserGroupCommand { GroupName = name };

        var sut = CreateSut();

        var result = await sut.TestValidateAsync(cmd);

        result.ShouldHaveValidationErrorFor(c => c.GroupName)
            .WithErrorMessage("Group Name should contain between 3 to 100 characters.");
        ;
    }

    [Fact]
    public async Task Validate_Should_Fail_When_GroupName_Greater_Than_100()
    {
        var cmd = new CreateUserGroupCommand { GroupName = new string('a', 101) };

        var sut = CreateSut();

        var result = await sut.TestValidateAsync(cmd);

        result.ShouldHaveValidationErrorFor(c => c.GroupName)
              .WithErrorMessage("Group Name should contain between 3 to 100 characters.");
    }

    [Theory]
    [InlineData("_StartsWithUnderscore")]
    [InlineData("0StartsWithZero")]
    [InlineData(" Has Leading Space")]
    public async Task Validate_Should_Fail_When_GroupName_Does_Not_Match_Regex(string name)
    {
        var cmd = new CreateUserGroupCommand { GroupName = name };

        var sut = CreateSut();

        var result = await sut.TestValidateAsync(cmd);

        result.ShouldHaveValidationErrorFor(c => c.GroupName)
              .WithErrorMessage("Please Enter Valid Group Name");
    }

    [Fact]
    public async Task Validate_Should_Fail_When_Description_Exceeds_250()
    {
        var cmd = new CreateUserGroupCommand
        {
            GroupName = "ValidName",
            GroupDescription = new string('x', 251)
        };

        var sut = CreateSut();

        var result = await sut.TestValidateAsync(cmd);

        result.ShouldHaveValidationErrorFor(c => c.GroupDescription)
              .WithErrorMessage("User Group Group Description Maximum 250 characters.");
    }

    [Fact]
    public async Task Validate_Should_Fail_When_GroupName_Is_Unique_In_Db()
    {
        // Arrange
        var cmd = new CreateUserGroupCommand
        {
            GroupName = "UniqueName",
            GroupDescription = "desc"
        };

        // If IsGroupNameUnique returns TRUE -> MustAsync(GroupName) returns FALSE -> validation error
        _repo.Setup(r => r.IsGroupNameUnique(cmd.GroupName)).ReturnsAsync(true);

        var sut = CreateSut();

        // Act
        var result = await sut.TestValidateAsync(cmd);

        // Assert
        result.ShouldHaveValidationErrorFor(_ => _)
              .WithErrorMessage("A Group with the same name  already exists.");
    }
}