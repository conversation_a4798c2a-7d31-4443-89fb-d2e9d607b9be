using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class BackUpService :  IBackUpService
{
    private readonly IBaseClient _client;

    public BackUpService(IBaseClient client)
    {
        _client = client;

    }

    public async Task<List<BackUpListVm>> GetBackUpList()
    {
        var request = new RestRequest("api/v6/backups");

        return await _client.GetFromCache<List<BackUpListVm>>(request, "GetBackUpList");
    }

    public async Task<BaseResponse> CreateAsync(CreateBackUpCommand createBackUpCommand)
    {
        var request = new RestRequest("api/v6/backups", Method.Post);

        request.AddJsonBody(createBackUpCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBackUpCommand updateBackUpCommand)
    {
        var request = new RestRequest("api/v6/backups", Method.Put);

        request.AddJsonBody(updateBackUpCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/backups/{id}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<BackUpDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/backups/{id}");

        return await _client.Get<BackUpDetailVm>(request);
    }
   
    public async Task<BaseResponse> ExecuteBackUp(BackUpExecuteCommand backUpExecuteCommand)
    {
        var request = new RestRequest("api/v6/backups/backup-execute", Method.Put);

        request.AddJsonBody(backUpExecuteCommand);

        return await _client.Put<BaseResponse>(request);
    }
    #region NameExist
    public async Task<bool> IsBackUpNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/backups/name-exist?backupName={name}&id={id}");

     return await _client.Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<BackUpListVm>> GetPaginatedBackUps(GetBackUpPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/backups/paginated-list");

      return await _client.Get<PaginatedResult<BackUpListVm>>(request);
  }
    #endregion

    public async Task<GetByConfigDetailVm> GetBackUpByConfig()
    {
        var request = new RestRequest("api/v6/backups/get-config");

        return await _client.Get<GetByConfigDetailVm>(request);
    }
}
