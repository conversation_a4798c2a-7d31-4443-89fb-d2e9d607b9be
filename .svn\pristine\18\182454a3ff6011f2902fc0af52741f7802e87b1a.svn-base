﻿using ContinuityPatrol.Application.Features.DynamicDashboardMap.Events.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicDashboardMap.Events;

public class DynamicDashboardMapUpdatedEventHandlerTests
{
    private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
    private readonly Mock<ILoggedInUserService> _userServiceMock;
    private readonly Mock<ILogger<DynamicDashboardMapUpdatedEventHandler>> _loggerMock;
    private readonly List<Domain.Entities.UserActivity> _userActivities;
    private readonly DynamicDashboardMapUpdatedEventHandler _handler;

    public DynamicDashboardMapUpdatedEventHandlerTests()
    {
        _userActivities = new List<Domain.Entities.UserActivity>();
        _userActivityRepoMock = new Mock<IUserActivityRepository>();
        _userActivityRepoMock = DynamicDashboardMapRepositoryMocks.UpdateDynamicDashboardMapEventRepository(_userActivities);
        _userActivityRepoMock.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync((Domain.Entities.UserActivity ua) =>
            {
                ua.Id = new Random().Next(1, 1000);
                ua.ReferenceId = Guid.NewGuid().ToString();
                _userActivities.Add(ua);
                return ua;
            });

        _userServiceMock = new Mock<ILoggedInUserService>();
        _userServiceMock.Setup(x => x.UserId).Returns("user-101");
        _userServiceMock.Setup(x => x.LoginName).Returns("test-user");
        _userServiceMock.Setup(x => x.RequestedUrl).Returns("http://localhost/api/map/update");
        _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _loggerMock = new Mock<ILogger<DynamicDashboardMapUpdatedEventHandler>>();

        _handler = new DynamicDashboardMapUpdatedEventHandler(
            _userServiceMock.Object,
            _loggerMock.Object,
            _userActivityRepoMock.Object
        );
    }

    [Fact]
    public async Task Handle_Should_Log_And_AddUserActivity_When_ValidEvent()
    {
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "UpdatedMap" };

        await _handler.Handle(updateEvent, CancellationToken.None);

        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == "user-101" &&
            ua.LoginName == "test-user" &&
            ua.RequestUrl == "http://localhost/api/map/update" &&
            ua.HostAddress == "127.0.0.1" &&
            ua.Action == "Update DynamicDashboardMap" &&
            ua.Entity == "DynamicDashboardMap" &&
            ua.ActivityType == ActivityType.Update.ToString() &&
            ua.ActivityDetails.Contains("UpdatedMap")
        )), Times.Once);

        _loggerMock.Verify(logger => logger.Log(
            LogLevel.Information,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, t) => obj.ToString()!.Contains("UpdatedMap")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()
        ), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Handle_MissingUserId_Gracefully()
    {
        _userServiceMock.Setup(x => x.UserId).Returns(string.Empty);
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "EmptyUserIdTest" };

        await _handler.Handle(updateEvent, CancellationToken.None);

        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == string.Empty &&
            ua.ActivityDetails.Contains("EmptyUserIdTest")
        )), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_NotThrow_When_LoggerFails()
    {
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "LoggerFailTest" };

        _loggerMock.Setup(logger => logger.Log(
            It.IsAny<LogLevel>(),
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()
        )).Throws(new Exception("Logger failure"));

        var ex = await Record.ExceptionAsync(() => _handler.Handle(updateEvent, CancellationToken.None));
        Assert.Null(ex);
    }

    [Fact]
    public async Task Handle_Should_Handle_Long_Name_Without_Exception()
    {
        var longName = new string('X', 1000);
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = longName };

        var ex = await Record.ExceptionAsync(() => _handler.Handle(updateEvent, CancellationToken.None));

        Assert.Null(ex);
        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails.Contains(longName)
        )), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Set_Correct_Entity_Name()
    {
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "EntityNameTest" };

        await _handler.Handle(updateEvent, CancellationToken.None);

        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Entity == "DynamicDashboardMap"
        )), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Include_Valid_HostAddress()
    {
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "HostCheck" };

        await _handler.Handle(updateEvent, CancellationToken.None);

        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.HostAddress == "127.0.0.1"
        )), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Copy_LoggedInUserService_Properties()
    {
        var updateEvent = new DynamicDashboardMapUpdatedEvent { Name = "ServicePropTest" };

        await _handler.Handle(updateEvent, CancellationToken.None);

        _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == "user-101" &&
            ua.LoginName == "test-user" &&
            ua.RequestUrl == "http://localhost/api/map/update" &&
            ua.HostAddress == "127.0.0.1"
        )), Times.Once);
    }
}
