using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class WorkflowRepositoryTests : IClassFixture<WorkflowFixture>,IClassFixture<WorkflowPermissionFixture>
{
    private readonly WorkflowFixture _workflowFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly WorkFlowRepository _repository;
    private readonly WorkFlowRepository _repositoryNotParent;
    private readonly WorkflowPermissionFixture _workflowPermissionFixture;  

    public WorkflowRepositoryTests(WorkflowFixture workflowFixture, WorkflowPermissionFixture workflowPermissionFixture)
    {
        _workflowFixture = workflowFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new WorkFlowRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _workflowPermissionFixture = workflowPermissionFixture;

    }

    #region GetWorkflowNames Tests

    [Fact]
    public async Task GetWorkflowNames_ReturnsEmptyList_WhenNoData()
    {
        var repository = new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());

        var result = await repository.GetWorkflowNames();
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowNames_ReturnsWorkflowsWithAllProperties_WhenDataExists()
    {
        // Add test data with complete structure
        var workflow = new Workflow
        {
            Name = "Test Workflow",
            CompanyId = "COMPANY_123",
            Properties = "{\"key\": \"value\"}",
            IsVerify = true,
            IsLock = false,
            IsPublish = true,
            Version = "1.0",
            IsDraft = false,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now.AddDays(-1),
            LastModifiedBy = "USER_123",
            LastModifiedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowNames();

        Assert.Single(result);
        var returnedWorkflow = result.First();
        Assert.Equal("Test Workflow", returnedWorkflow.Name);
        Assert.Equal(workflow.ReferenceId, returnedWorkflow.ReferenceId);
        Assert.False(returnedWorkflow.IsLock);
        Assert.True(returnedWorkflow.IsPublish);
        Assert.True(returnedWorkflow.IsVerify);
        Assert.Equal("USER_123", returnedWorkflow.CreatedBy);
    }

    //[Fact]
    //public async Task GetWorkflowNames_ReturnsOnlyActiveWorkflows()
    //{
    //    // Add active and inactive workflows
    //    var activeWorkflow = new Workflow
    //    {
    //        Name = "Active Workflow",
    //        CompanyId = "COMPANY_123",
    //        IsActive = true,
    //        IsPublish = true,
    //        ReferenceId = Guid.NewGuid().ToString(),
    //        CreatedBy = "USER_123",
    //        CreatedDate = DateTime.Now
    //    };

    //    var inactiveWorkflow = new Workflow
    //    {
    //        Name = "Inactive Workflow",
    //        CompanyId = "COMPANY_123",
    //        IsActive = false,
    //        IsPublish = true,
    //        ReferenceId = Guid.NewGuid().ToString(),
    //        CreatedBy = "USER_123",
    //        CreatedDate = DateTime.Now
    //    };

    //    await _dbContext.WorkFlows.AddRangeAsync(new[] { activeWorkflow, inactiveWorkflow });
    //    await _dbContext.SaveChangesAsync();

    //    var result = await _repository.GetWorkflowNames();

    //    Assert.Single(result);
    //    Assert.Equal("Active Workflow", result.First().Name);
    //}

    [Fact]
    public async Task GetWorkflowNames_ReturnsWorkflowsOrderedByName()
    {
        // Add workflows in random order
        var workflows = new[]
        {
            new Workflow
            {
                Name = "Zebra Workflow",
                CompanyId = "COMPANY_123",
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedBy = "USER_123",
                CreatedDate = DateTime.Now
            },
            new Workflow
            {
                Name = "Alpha Workflow",
                CompanyId = "COMPANY_123",
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedBy = "USER_123",
                CreatedDate = DateTime.Now
            },
            new Workflow
            {
                Name = "Beta Workflow",
                CompanyId = "COMPANY_123",
                IsActive = true,
                IsPublish = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedBy = "USER_123",
                CreatedDate = DateTime.Now
            }
        };

        await _dbContext.WorkFlows.AddRangeAsync(workflows);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowNames();

        Assert.Equal(3, result.Count);
        Assert.Equal("Alpha Workflow", result[0].Name);
        Assert.Equal("Beta Workflow", result[1].Name);
        Assert.Equal("Zebra Workflow", result[2].Name);
    }

    #endregion

    #region IsWorkflowNameExist Tests

    [Fact]
    public async Task IsWorkflowNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        var result = await _repository.IsWorkflowNameExist("Non-existent Workflow", "");
        Assert.False(result);
    }

    [Fact]
    public async Task IsWorkflowNameExist_ReturnsTrue_WhenNameExists()
    {
        var workflow = new Workflow
        {
            Name = "Existing Workflow",
            CompanyId = "COMPANY_123",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsWorkflowNameExist("Existing Workflow", "");
        Assert.True(result);
    }

    [Fact]
    public async Task IsWorkflowNameExist_ReturnsFalse_WhenNameExistsButIdMatches()
    {
        var workflowId = Guid.NewGuid().ToString();
        var workflow = new Workflow
        {
            Name = "Test Workflow",
            CompanyId = "COMPANY_123",
            IsActive = true,
            ReferenceId = workflowId,
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        // Should return false when checking the same workflow (update scenario)
        var result = await _repository.IsWorkflowNameExist("Test Workflow", workflowId);
        Assert.False(result);
    }

    #endregion

    #region IsWorkflowNameUnique Tests

    [Fact]
    public async Task IsWorkflowNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        var result = await _repository.IsWorkflowNameUnique("Unique Workflow");
        Assert.False(result);
    }

    [Fact]
    public async Task IsWorkflowNameUnique_ReturnsTrue_WhenNameExists()
    {
        var workflow = new Workflow
        {
            Name = "Existing Workflow",
            CompanyId = "COMPANY_123",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsWorkflowNameUnique("Existing Workflow");
        Assert.True(result);
    }

    #endregion

    #region GetWorkflowPropertiesByServerId Tests

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_ReturnsEmptyList_WhenNoMatchingWorkflows()
    {
        var result = await _repository.GetWorkflowPropertiesByServerId("NON_EXISTENT_SERVER");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_ReturnsMatchingWorkflows_WhenServerIdExists()
    {
        var serverId = "SERVER_123";
        var workflow = new Workflow
        {
            Name = "Server Workflow",
            CompanyId = "COMPANY_123",
            Properties = $"{{\"serverId\": \"{serverId}\", \"other\": \"value\"}}",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPropertiesByServerId(serverId);

        Assert.Single(result);
        Assert.Equal("Server Workflow", result.First().Name);
        Assert.Contains(serverId, result.First().Properties);
    }

    #endregion

    #region GetWorkflowPropertiesByDatabaseId Tests

    [Fact]
    public async Task GetWorkflowPropertiesByDatabaseId_ReturnsEmptyList_WhenNoMatchingWorkflows()
    {
        var result = await _repository.GetWorkflowPropertiesByDatabaseId("NON_EXISTENT_DATABASE");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByDatabaseId_ReturnsMatchingWorkflows_WhenDatabaseIdExists()
    {
        var databaseId = "DATABASE_456";
        var workflow = new Workflow
        {
            Name = "Database Workflow",
            CompanyId = "COMPANY_123",
            Properties = $"{{\"databaseId\": \"{databaseId}\", \"config\": \"test\"}}",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPropertiesByDatabaseId(databaseId);

        Assert.Single(result);
        Assert.Equal("Database Workflow", result.First().Name);
        Assert.Contains(databaseId, result.First().Properties);
    }

    #endregion

    #region GetWorkflowPropertiesByReplicationId Tests

    [Fact]
    public async Task GetWorkflowPropertiesByReplicationId_ReturnsEmptyList_WhenNoMatchingWorkflows()
    {
        var result = await _repository.GetWorkflowPropertiesByReplicationId("NON_EXISTENT_REPLICATION");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByReplicationId_ReturnsMatchingWorkflows_WhenReplicationIdExists()
    {
        var replicationId = "REPLICATION_789";
        var workflow = new Workflow
        {
            Name = "Replication Workflow",
            CompanyId = "COMPANY_123",
            Properties = $"{{\"replicationId\": \"{replicationId}\", \"settings\": \"active\"}}",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPropertiesByReplicationId(replicationId);

        Assert.Single(result);
        Assert.Equal("Replication Workflow", result.First().Name);
        Assert.Contains(replicationId, result.First().Properties);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsEmptyList_WhenNoData()
    {
        var result = await _repository.ListAllAsync();
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsWorkflowsBasedOnUserPermissions_WhenDataExists()
    {
        var userId = "USER_123";
        var companyId = "COMPANY_123";

        // Create mock user service for non-parent user
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);
        mockUserService.Setup(x => x.IsParent).Returns(false);

        var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

        var workflow = new Workflow
        {
            Name = "User Workflow",
            CompanyId = companyId,
            CreatedBy = userId,
            IsPublish = false,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsync();

        Assert.Single(result);
        Assert.Equal("User Workflow", result.First().Name);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsPublishedWorkflows_ForParentUser()
    {
        var userId = "PARENT_USER";

        // Create mock user service for parent user
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

        var publishedWorkflow = new Workflow
        {
            Name = "Published Workflow",
            CompanyId = "COMPANY_123",
            CreatedBy = "OTHER_USER",
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        var unpublishedWorkflow = new Workflow
        {
            Name = "Unpublished Workflow",
            CompanyId = "COMPANY_123",
            CreatedBy = "OTHER_USER",
            IsPublish = false,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddRangeAsync(new[] { publishedWorkflow, unpublishedWorkflow });
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsync();

        Assert.Single(result);
        Assert.Equal("Published Workflow", result.First().Name);
    }

    #endregion

    #region ListAllAsyncForDashboardView Tests

    [Fact]
    public async Task ListAllAsyncForDashboardView_ReturnsEmptyList_WhenNoData()
    {
        var result = await _repository.ListAllAsyncForDashboardView();
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsyncForDashboardView_ReturnsWorkflowsWithUserPermissionLimitedProperties()
    {
        var userId = "USER_123";
        var companyId = "COMPANY_123";

        // Create mock user service
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);
        mockUserService.Setup(x => x.IsParent).Returns(false);

        var _mockWorkflowRepository = new Mock<IWorkflowRepository>();
        var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

        var workflow = new Workflow
        {
            Name = "Dashboard Workflow",
            CompanyId = companyId,
            CreatedBy = userId,
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Properties = "{\"complex\": \"data\"}",
            Version = "2.0",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var workflowPermissions=_workflowPermissionFixture.WorkflowPermissionList;
        workflowPermissions[0].Type = "workflow";
        workflowPermissions[0].UserProperties = "[{\"id\":\"USER_123\",\"name\":\"mani\"}]";
        workflowPermissions[0].AccessProperties = $"[{{\"id\":\"{workflow.ReferenceId}\",\"name\":\"rerer\"}}]";;
        workflowPermissions[0].UserAccess = "Users";
        workflowPermissions[0].AccessType = "temporary";
        workflowPermissions[0].ScheduleTime = "[{\"startDate\":\"18/07/2024 18:35:00 PM\",\"endDate\":\"19/12/2035 12:58:00 PM\"}]";
       
        workflowPermissions[1].Type = "workflow";
        workflowPermissions[1].UserProperties = "[{\"id\":\"USER_123\",\"name\":\"mani\"}]";
        workflowPermissions[1].AccessProperties = $"[{{\"id\":\"{workflow.ReferenceId}\",\"name\":\"rerer\"}}]";
        workflowPermissions[1].UserAccess = "Users";
        workflowPermissions[1].AccessType = "temporary";
        workflowPermissions[1].ScheduleTime = "[{\"startDate\":\"18/07/2024 18:35:00 PM\",\"endDate\":\"19/158:00 PM\"}]";

        await _dbContext.WorkflowPermissions.AddRangeAsync(workflowPermissions);    
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsyncForDashboardView();

        Assert.Single(result);
        var dashboardWorkflow = result.First();
        Assert.Equal("Dashboard Workflow", dashboardWorkflow.Name);
        Assert.Equal(workflow.ReferenceId, dashboardWorkflow.ReferenceId);
        Assert.True(dashboardWorkflow.Id > 0);
        // Properties should be null/empty for dashboard view (limited data)
    }
    [Fact]
    public async Task ListAllAsyncForDashboardView_ReturnsWorkflowsWithUserGroupPermissionLimitedProperties()
    {
        var userId = "USER_123";
        var companyId = "COMPANY_123";

        // Create mock user service
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);
        mockUserService.Setup(x => x.IsParent).Returns(false);

        var _mockWorkflowRepository = new Mock<IWorkflowRepository>();
        var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

        var workflow = new Workflow
        {
            Name = "Dashboard Workflow",
            CompanyId = companyId,
            CreatedBy = userId,
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            Properties = "{\"complex\": \"data\"}",
            Version = "2.0",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        await _dbContext.UserGroup.AddAsync(new UserGroup
        {
            ReferenceId = "c06fc2a3-cdad-4dec-93c3-fe1bbbcf9818",
            GroupName = "rerer",
            UserProperties= "[{\"id\":\"USER_123\",\"loginname\":\"abcdd\"}]",
            IsActive = true,
        });
        await _dbContext.SaveChangesAsync();

        var workflowPermissions=_workflowPermissionFixture.WorkflowPermissionList;
        workflowPermissions[0].Type = "workflow";
        workflowPermissions[0].UserProperties = "[{\"id\":\"USER_123\",\"name\":\"man12\"},{\"id\":\"c06fc2a3-cdad-4dec-93c3-fe1bbbcf9818\",\"name\":\"rerer\"},{\"id\":\"8f8763e5-56cd-423a-98ce-f8939fc9fbcf\",\"name\":\"MongoDb_WF\"},{\"id\":\"07898841-c24d-4026-8f14-2cfbef8d7682\",\"name\":\"Mysql_wf\"},{\"id\":\"ea6c1b53-4356-4718-8dca-73eb2f5b2bc1\",\"name\":\"New_test123\"},{\"id\":\"3c8203ff-1b72-4d20-a982-101f1fe0a249\",\"name\":\"nive11\"},{\"id\":\"b59f60ba-1dcc-48ca-97a2-210af703f8b5\",\"name\":\"Pratheesh_S\"}]";
        workflowPermissions[0].AccessProperties = $"[{{\"id\":\"{workflow.ReferenceId}\",\"name\":\"rerer\"}}]";;
        workflowPermissions[0].UserAccess = "UserGroup";
        workflowPermissions[0].AccessType = "temporary";
        workflowPermissions[0].ScheduleTime = "[{\"startDate\":\"18/07/2024 18:35:00 PM\",\"endDate\":\"19/12/2035 12:58:00 PM\"}]";
       
        workflowPermissions[1].Type = "workflow";
        workflowPermissions[1].UserProperties = "[{\"id\":\"USER_123\",\"name\":\"man12\"},{\"id\":\"c06fc2a3-cdad-4dec-93c3-fe1bbbcf9818\",\"name\":\"rerer\"},{\"id\":\"8f8763e5-56cd-423a-98ce-f8939fc9fbcf\",\"name\":\"MongoDb_WF\"},{\"id\":\"07898841-c24d-4026-8f14-2cfbef8d7682\",\"name\":\"Mysql_wf\"},{\"id\":\"ea6c1b53-4356-4718-8dca-73eb2f5b2bc1\",\"name\":\"New_test123\"},{\"id\":\"3c8203ff-1b72-4d20-a982-101f1fe0a249\",\"name\":\"nive11\"},{\"id\":\"b59f60ba-1dcc-48ca-97a2-210af703f8b5\",\"name\":\"Pratheesh_S\"}]";
        workflowPermissions[1].AccessProperties = $"[{{\"id\":\"{workflow.ReferenceId}\",\"name\":\"rerer\"}}]";
        workflowPermissions[1].UserAccess = "UserGroup";
        workflowPermissions[1].AccessType = "temporary";
        workflowPermissions[1].ScheduleTime = "[{\"startDate\":\"18/07/2024 18:35:00 PM\",\"endDate\":\"19/12/202:00 PM\"}]";
       
        workflowPermissions[2].Type = "workflow";
        workflowPermissions[2].UserProperties = "[{\"id\":\"USER_123\",\"name\":\"man12\"},{\"id\":\"c06fc2a3-cdad-4dec-93c3-fe1bbbcf9818\",\"name\":\"rerer\"},{\"id\":\"8f8763e5-56cd-423a-98ce-f8939fc9fbcf\",\"name\":\"MongoDb_WF\"},{\"id\":\"07898841-c24d-4026-8f14-2cfbef8d7682\",\"name\":\"Mysql_wf\"},{\"id\":\"ea6c1b53-4356-4718-8dca-73eb2f5b2bc1\",\"name\":\"New_test123\"},{\"id\":\"3c8203ff-1b72-4d20-a982-101f1fe0a249\",\"name\":\"nive11\"},{\"id\":\"b59f60ba-1dcc-48ca-97a2-210af703f8b5\",\"name\":\"Pratheesh_S\"}]";
        workflowPermissions[2].AccessProperties = $"[{{\"id\":\"{workflow.ReferenceId}\",\"name\":\"rerer\"}}]";
        workflowPermissions[2].UserAccess = "UserGroup";
        workflowPermissions[2].AccessType = "permanent";
        workflowPermissions[2].ScheduleTime = "[{\"startDate\":\"18/07/2024 18:35:00 PM\",\"endDate\":\"19/12/2035 12:58:00 PM\"}]";

        await _dbContext.WorkflowPermissions.AddRangeAsync(workflowPermissions);    
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsyncForDashboardView();

        Assert.Single(result);
        var dashboardWorkflow = result.First();
        Assert.Equal("Dashboard Workflow", dashboardWorkflow.Name);
        Assert.Equal(workflow.ReferenceId, dashboardWorkflow.ReferenceId);
        Assert.True(dashboardWorkflow.Id > 0);

    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenWorkflowDoesNotExist()
    {
        // Use a valid GUID format that doesn't exist in the database
        var nonExistentGuid = "083e0ff4-61b6-4cc5-b84b-4afa49f73d7a";

        var result = await _repository.GetByReferenceIdAsync(nonExistentGuid);
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsWorkflow_WhenWorkflowExists()
    {
        var referenceId = Guid.NewGuid().ToString();
        var workflow = new Workflow
        {
            Name = "Reference Workflow",
            CompanyId = "COMPANY_123",
            ReferenceId = referenceId,
            IsActive = true,
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(referenceId);

        Assert.NotNull(result);
        Assert.Equal("Reference Workflow", result.Name);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_RespectsCompanyFilter_ForNonParentUser()
    {
        var userId = "USER_123";
        var userCompanyId = "COMPANY_123";
        var otherCompanyId = "COMPANY_456";
        var referenceId = Guid.NewGuid().ToString();

        // Create mock user service for non-parent user
        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.UserId).Returns(userId);
        mockUserService.Setup(x => x.CompanyId).Returns(userCompanyId);
        mockUserService.Setup(x => x.IsParent).Returns(false);

        var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

        var workflowInOtherCompany = new Workflow
        {
            Name = "Other Company Workflow",
            CompanyId = otherCompanyId,
            ReferenceId = referenceId,
            IsActive = true,
            CreatedBy = "OTHER_USER",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflowInOtherCompany);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetByReferenceIdAsync(referenceId);

        // Should return null because workflow belongs to different company
        Assert.Null(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ReturnsEmptyQuery_WhenNoData()
    {
        var query = _repository.GetPaginatedQuery();
        var result = query.ToList();
        Assert.Empty(result);
    }

    [Fact]
    public void GetPaginatedQuery_ReturnsOrderedByIdDescending()
    {
        // Add workflows with different IDs
        var workflows = new[]
        {
            new Workflow
            {
                Name = "First Workflow",
                CompanyId = "COMPANY_123",
                CreatedBy = "USER_123",
                IsPublish = true,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now.AddDays(-2)
            },
            new Workflow
            {
                Name = "Second Workflow",
                CompanyId = "COMPANY_123",
                CreatedBy = "USER_123",
                IsPublish = true,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now.AddDays(-1)
            }
        };

        _dbContext.WorkFlows.AddRange(workflows);
        _dbContext.SaveChanges();

        var query = _repository.GetPaginatedQuery();
        var result = query.ToList();

        Assert.Equal(2, result.Count);
        // Should be ordered by ID descending (newest first)
        Assert.True(result[0].Id > result[1].Id);
    }

    [Fact]
    public void GetPaginatedQuery_ReturnsOnlyActiveWorkflows()
    {
        var activeWorkflow = new Workflow
        {
            Name = "Active Workflow",
            CompanyId = "COMPANY_123",
            CreatedBy = "USER_123",
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        var inactiveWorkflow = new Workflow
        {
            Name = "Inactive Workflow",
            CompanyId = "COMPANY_123",
            CreatedBy = "USER_123",
            IsPublish = true,
            IsActive = false,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        _dbContext.WorkFlows.AddRange(new[] { activeWorkflow, inactiveWorkflow });
        _dbContext.SaveChanges();

        var query = _repository.GetPaginatedQuery();
        var result = query.ToList();

        Assert.Single(result);
        Assert.Equal("Active Workflow", result.First().Name);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task IsWorkflowNameExist_HandlesNullName()
    {
        var result = await _repository.IsWorkflowNameExist(null, "");
        Assert.False(result);
    }

    [Fact]
    public async Task IsWorkflowNameExist_HandlesEmptyName()
    {
        var result = await _repository.IsWorkflowNameExist("", "");
        Assert.False(result);
    }

    [Fact]
    public async Task IsWorkflowNameUnique_HandlesNullName()
    {
        var result = await _repository.IsWorkflowNameUnique(null);
        Assert.False(result);
    }

    [Fact]
    public async Task IsWorkflowNameUnique_HandlesEmptyName()
    {
        var result = await _repository.IsWorkflowNameUnique("");
        Assert.False(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_HandlesNullServerId()
    {
        var result = await _repository.GetWorkflowPropertiesByServerId(null);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByDatabaseId_HandlesNullDatabaseId()
    {
        var result = await _repository.GetWorkflowPropertiesByDatabaseId(null);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByReplicationId_HandlesNullReplicationId()
    {
        var result = await _repository.GetWorkflowPropertiesByReplicationId(null);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsArgumentNullException_WhenReferenceIdIsNull()
    {
       
        var exception = await Assert.ThrowsAsync<ArgumentNullException>(
            () => _repository.GetByReferenceIdAsync(null));

        Assert.Equal("Id", exception.ParamName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsInvalidArgumentException_WhenReferenceIdIsEmpty()
    {
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync(""));

        Assert.Equal("Input 'Id' is not valid format.", exception.Message);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsInvalidArgumentException_WhenReferenceIdIsWhitespace()
    {
       
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync("   "));

        Assert.Equal("Input 'Id' is not valid format.", exception.Message);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsInvalidArgumentException_WhenReferenceIdIsInvalidGuidFormat()
    {
       
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync("invalid-guid-format"));

        Assert.Equal("Input 'Id' is not valid format.", exception.Message);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsInvalidArgumentException_WhenReferenceIdIsEmptyGuid()
    {
      
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync("00000000-0000-0000-0000-000000000000"));

        Assert.Equal("Input 'Id' is not valid format.", exception.Message);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ThrowsInvalidArgumentException_WhenReferenceIdIsNumericString()
    {
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync("12345"));

        Assert.Equal("Input 'Id' is not valid format.", exception.Message);
    }

    #endregion

    #region Performance Tests

    [Fact]
    public async Task GetWorkflowNames_PerformanceTest_WithLargeDataset()
    {
        // Add a large number of workflows
        var workflows = new List<Workflow>();
        for (int i = 0; i < 1000; i++)
        {
            workflows.Add(new Workflow
            {
                Name = $"Workflow {i:D4}",
                CompanyId = "COMPANY_123",
                CreatedBy = "USER_123",
                IsPublish = true,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedDate = DateTime.Now.AddDays(-i)
            });
        }

        await _dbContext.WorkFlows.AddRangeAsync(workflows);
        await _dbContext.SaveChangesAsync();

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _repository.GetWorkflowNames();
        stopwatch.Stop();

        Assert.Equal(1000, result.Count);
        // Performance assertion - should complete within reasonable time
        Assert.True(stopwatch.ElapsedMilliseconds < 5000,
            $"GetWorkflowNames took {stopwatch.ElapsedMilliseconds}ms with 1000 workflows, expected < 5000ms");

        // Verify ordering is maintained
        Assert.Equal("Workflow 0000", result.First().Name);
        Assert.Equal("Workflow 0999", result.Last().Name);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_PerformanceTest_WithComplexProperties()
    {
        var serverId = "SERVER_PERF_TEST";
        var workflows = new List<Workflow>();

        for (int i = 0; i < 500; i++)
        {
            workflows.Add(new Workflow
            {
                Name = $"Complex Workflow {i}",
                CompanyId = "COMPANY_123",
                Properties = $"{{\"serverId\": \"{serverId}\", \"config\": {{\"nested\": {{\"deep\": \"value{i}\"}}, \"array\": [1,2,3,4,5]}}, \"metadata\": \"large_string_data_here\"}}",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CreatedBy = "USER_123",
                CreatedDate = DateTime.Now
            });
        }

        await _dbContext.WorkFlows.AddRangeAsync(workflows);
        await _dbContext.SaveChangesAsync();

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = await _repository.GetWorkflowPropertiesByServerId(serverId);
        stopwatch.Stop();

        Assert.Equal(500, result.Count);
        Assert.True(stopwatch.ElapsedMilliseconds < 3000,
            $"GetWorkflowPropertiesByServerId took {stopwatch.ElapsedMilliseconds}ms with 500 complex workflows, expected < 3000ms");
    }

    #endregion

    #region Data Integrity Tests

    [Fact]
    public async Task GetWorkflowNames_ReturnsConsistentData_AcrossMultipleCalls()
    {
        // Add test data
        var workflow = new Workflow
        {
            Name = "Consistent Workflow",
            CompanyId = "COMPANY_123",
            CreatedBy = "USER_123",
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        // Call multiple times and verify consistency
        var result1 = await _repository.GetWorkflowNames();
        var result2 = await _repository.GetWorkflowNames();
        var result3 = await _repository.GetWorkflowNames();

        Assert.Equal(result1.Count, result2.Count);
        Assert.Equal(result2.Count, result3.Count);
        Assert.Equal(result1.First().Name, result2.First().Name);
        Assert.Equal(result2.First().Name, result3.First().Name);
        Assert.Equal(result1.First().ReferenceId, result2.First().ReferenceId);
        Assert.Equal(result2.First().ReferenceId, result3.First().ReferenceId);
    }

    #endregion

    #region GetWorkflowPermissions Tests

    [Fact]
    public async Task GetWorkflowPermissions_ReturnsEmptyList_WhenNoPermissions()
    {
        var result = await _repository.GetWorkflowPermissions("workflow");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPermissions_ReturnsEmptyList_WhenTypeDoesNotMatch()
    {
        // Add permission with different type
        var permission = new WorkflowPermission
        {
            Type = "other",
            UserAccess = "users",
            UserProperties = "USER_123",
            AccessType = "permanent",
            AccessProperties = "[{\"id\":\"workflow1\",\"name\":\"Test Workflow\"}]",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkflowPermissions.AddAsync(permission);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPermissions("workflow");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPermissions_HandlesNullType()
    {
        var result = await _repository.GetWorkflowPermissions(null);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPermissions_HandlesEmptyType()
    {
        var result = await _repository.GetWorkflowPermissions("");
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetWorkflowPermissions_IsCaseInsensitive()
    {
        var permission = new WorkflowPermission
        {
            Type = "WORKFLOW",
            UserAccess = "users",
            UserProperties = "USER_123",
            AccessType = "permanent",
            AccessProperties = "[{\"id\":\"workflow1\",\"name\":\"Test Workflow\"}]",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkflowPermissions.AddAsync(permission);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPermissions("workflow");
        Assert.Empty(result); // Will be empty because user service doesn't match the permission
    }

    #endregion

    #region Complex Integration Tests

    //[Fact]
    //public async Task GetWorkflowNames_WithPermissions_ReturnsDistinctWorkflows()
    //{
    //    var userId = "USER_123";
    //    var companyId = "COMPANY_123";

    //    // Create mock user service for non-parent user
    //    var mockUserService = new Mock<ILoggedInUserService>();
    //    mockUserService.Setup(x => x.UserId).Returns(userId);
    //    mockUserService.Setup(x => x.CompanyId).Returns(companyId);
    //    mockUserService.Setup(x => x.IsParent).Returns(false);

    //    var repository = new WorkFlowRepository(_dbContext, mockUserService.Object);

       
    //    var workflow1 = new Workflow
    //    {
    //        Name = "User Workflow",
    //        CompanyId = companyId,
    //        Properties = "{\"type\": \"user\", \"config\": {\"enabled\": true}}",
    //        IsVerify = true,
    //        IsLock = false,
    //        IsPublish = false,
    //        Version = "1.0",
    //        IsDraft = false,
    //        IsActive = true,
    //        ReferenceId = "workflow1",
    //        CreatedBy = userId, // Same as current user
    //        CreatedDate = DateTime.Now.AddDays(-2),
    //        LastModifiedBy = userId,
    //        LastModifiedDate = DateTime.Now.AddDays(-1)
    //    };

       
    //    var workflow2 = new Workflow
    //    {
    //        Name = "Published Workflow",
    //        CompanyId = companyId,
    //        Properties = "{\"type\": \"shared\", \"config\": {\"public\": true, \"permissions\": [\"read\", \"execute\"]}}",
    //        IsVerify = false,
    //        IsLock = true,
    //        IsPublish = true,
    //        Version = "2.1",
    //        IsDraft = false,
    //        IsActive = true,
    //        ReferenceId = "workflow2",
    //        CreatedBy = userId, // Same as current user to satisfy the CreatedBy condition
    //        CreatedDate = DateTime.Now.AddDays(-5),
    //        LastModifiedBy = userId,
    //        LastModifiedDate = DateTime.Now.AddDays(-3)
    //    };

    //    // Add WorkflowPermission to grant access to an additional workflow via permissions
    //    var permissionWorkflow = new Workflow
    //    {
    //        Name = "Permission Workflow",
    //        CompanyId = companyId,
    //        Properties = "{\"type\": \"permission\", \"config\": {\"shared\": true}}",
    //        IsVerify = true,
    //        IsLock = false,
    //        IsPublish = false,
    //        Version = "1.5",
    //        IsDraft = false,
    //        IsActive = true,
    //        ReferenceId = "workflow3",
    //        CreatedBy = "OTHER_USER", // Different user
    //        CreatedDate = DateTime.Now.AddDays(-3),
    //        LastModifiedBy = "OTHER_USER",
    //        LastModifiedDate = DateTime.Now.AddDays(-2)
    //    };

    //    var workflowPermission = new WorkflowPermission
    //    {
    //        Type = "workflow",
    //        UserAccess = "users",
    //        UserProperties = $"[{{\"userId\":\"{userId}\",\"name\":\"Test User\"}}]", // JSON that contains userId
    //        AccessType = "permanent",
    //        AccessProperties = "[{\"id\":\"workflow3\",\"name\":\"Permission Workflow\"}]", // Must not be null
    //        ScheduleTime = null, // Can be null for permanent access
    //        CompanyId = companyId,
    //        IsActive = true,
    //        ReferenceId = Guid.NewGuid().ToString(),
    //        CreatedBy = "ADMIN_USER",
    //        CreatedDate = DateTime.Now.AddDays(-1),
    //        LastModifiedBy = "ADMIN_USER",
    //        LastModifiedDate = DateTime.Now
    //    };

    //    await _dbContext.WorkFlows.AddRangeAsync(new[] { workflow1, workflow2, permissionWorkflow });
    //    await _dbContext.WorkflowPermissions.AddAsync(workflowPermission);
    //    await _dbContext.SaveChangesAsync();

    //    // Verify the permission was saved correctly
    //    var savedPermission = await _dbContext.WorkflowPermissions
    //        .FirstOrDefaultAsync(x => x.Type == "workflow");
    //    Assert.NotNull(savedPermission);
    //    Assert.NotNull(savedPermission.AccessProperties);
    //    Assert.NotNull(savedPermission.UserProperties);

    //    // Debug: Check if UserProperties contains the userId
    //    Assert.True(savedPermission.UserProperties.Contains(userId),
    //        $"UserProperties '{savedPermission.UserProperties}' should contain userId '{userId}'");

    //    // Debug: Check AccessType
    //    Assert.Equal("permanent", savedPermission.AccessType.ToLower());

    //    // Debug: Test the GetWorkflowPermissions method directly
    //    var permissionIds = await repository.GetWorkflowPermissions("workflow");
    //    Assert.NotEmpty(permissionIds); // This should contain "workflow3"
    //    Assert.Contains("workflow3", permissionIds);

    //    var result = await repository.GetWorkflowNames();

      
    //    Assert.Equal(3, result.Count);

    //    // Verify workflow1 details (user's own unpublished workflow)
    //    var userWorkflow = result.FirstOrDefault(w => w.Name == "User Workflow");
    //    Assert.NotNull(userWorkflow);
    //    Assert.Equal("User Workflow", userWorkflow.Name);
    //    Assert.Equal("workflow1", userWorkflow.ReferenceId);
    //    Assert.False(userWorkflow.IsLock);
    //    Assert.False(userWorkflow.IsPublish);
    //    Assert.True(userWorkflow.IsVerify);
    //    Assert.Equal(userId, userWorkflow.CreatedBy);

    //    // Verify workflow2 details (user's own published workflow)
    //    var publishedWorkflow = result.FirstOrDefault(w => w.Name == "Published Workflow");
    //    Assert.NotNull(publishedWorkflow);
    //    Assert.Equal("Published Workflow", publishedWorkflow.Name);
    //    Assert.Equal("workflow2", publishedWorkflow.ReferenceId);
    //    Assert.True(publishedWorkflow.IsLock);
    //    Assert.True(publishedWorkflow.IsPublish);
    //    Assert.False(publishedWorkflow.IsVerify);
    //    Assert.Equal(userId, publishedWorkflow.CreatedBy);

    //    // Verify workflow3 details (accessed via permissions)
    //    var permissionBasedWorkflow = result.FirstOrDefault(w => w.Name == "Permission Workflow");
    //    Assert.NotNull(permissionBasedWorkflow);
    //    Assert.Equal("Permission Workflow", permissionBasedWorkflow.Name);
    //    Assert.Equal("workflow3", permissionBasedWorkflow.ReferenceId);
    //    Assert.False(permissionBasedWorkflow.IsLock);
    //    Assert.False(permissionBasedWorkflow.IsPublish);
    //    Assert.True(permissionBasedWorkflow.IsVerify);
    //    Assert.Equal("OTHER_USER", permissionBasedWorkflow.CreatedBy);
    //}

    [Fact]
    public async Task Repository_HandlesMultiplePropertiesSearch_Correctly()
    {
        var serverId = "SERVER_123";
        var databaseId = "DB_456";
        var replicationId = "REP_789";

        var workflow = new Workflow
        {
            Name = "Multi-Property Workflow",
            CompanyId = "COMPANY_123",
            Properties = $"{{\"serverId\": \"{serverId}\", \"databaseId\": \"{databaseId}\", \"replicationId\": \"{replicationId}\"}}",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var serverResult = await _repository.GetWorkflowPropertiesByServerId(serverId);
        var databaseResult = await _repository.GetWorkflowPropertiesByDatabaseId(databaseId);
        var replicationResult = await _repository.GetWorkflowPropertiesByReplicationId(replicationId);

        Assert.Single(serverResult);
        Assert.Single(databaseResult);
        Assert.Single(replicationResult);

        Assert.Equal("Multi-Property Workflow", serverResult.First().Name);
        Assert.Equal("Multi-Property Workflow", databaseResult.First().Name);
        Assert.Equal("Multi-Property Workflow", replicationResult.First().Name);
    }

    #endregion

    #region Boundary Tests

    [Fact]
    public async Task GetWorkflowNames_HandlesVeryLongWorkflowName()
    {
        var longName = new string('A', 1000); // Very long name
        var workflow = new Workflow
        {
            Name = longName,
            CompanyId = "COMPANY_123",
            CreatedBy = "USER_123",
            IsPublish = true,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowNames();

        Assert.Single(result);
        Assert.Equal(longName, result.First().Name);
    }

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_HandlesVeryLargeProperties()
    {
        var serverId = "SERVER_LARGE";
        var largeProperties = "{\"serverId\": \"" + serverId + "\", \"data\": \"" + new string('X', 10000) + "\"}";

        var workflow = new Workflow
        {
            Name = "Large Properties Workflow",
            CompanyId = "COMPANY_123",
            Properties = largeProperties,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetWorkflowPropertiesByServerId(serverId);

        Assert.Single(result);
        Assert.Equal("Large Properties Workflow", result.First().Name);
        Assert.Contains(serverId, result.First().Properties);
    }

    [Fact]
    public async Task IsWorkflowNameExist_HandlesSpecialCharacters()
    {
        var specialName = "Workflow!@#$%^&*()_+-=[]{}|;':\",./<>?";
        var workflow = new Workflow
        {
            Name = specialName,
            CompanyId = "COMPANY_123",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_123",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkFlows.AddAsync(workflow);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsWorkflowNameExist(specialName, "");
        Assert.True(result);
    }

    #endregion

    #region Parent/Child Company Logic Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllWorkflows_WhenIsParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Parent Workflow 1",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            },
            new Workflow
            {
                Name = "Child Workflow 1",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            }
        };

        await _repository.AddRangeAsync(workflows);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Parent can see all workflows
        Assert.Contains(result, x => x.Name == "Parent Workflow 1");
        Assert.Contains(result, x => x.Name == "Child Workflow 1");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredWorkflows_WhenIsNotParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Parent Workflow 1",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = false,
                CreatedBy= "USER_123"
            },
            new Workflow
            {
                Name = "Child Workflow 1",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "b213cf2a-6b1d-479d-a76c-b711137b10a0",
                IsActive = true,
                IsPublish = true,CreatedBy="USER_Child1245"
            },
            new Workflow
            {
                Name = "Child Workflow 2",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true,
                CreatedBy="USER_Child1245"
            }
        };


        await _repositoryNotParent.AddRangeAsync(workflows);
        var permission = new WorkflowPermission
        {
            Type = "workflow",
            UserAccess = "users",
            UserProperties = "USER_Child1245",
            AccessType = "permanent",
            AccessProperties = "[{\"id\":\"b213cf2a-6b1d-479d-a76c-b711137b10a0\",\"name\":\"Test Workflow\"}]",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_Child1245",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkflowPermissions.AddAsync(permission);
        await _dbContext.SaveChangesAsync();

     

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Child can only see their company workflows
        Assert.All(result, x => Assert.Contains("Child Workflow", x.Name));
        Assert.DoesNotContain(result, x => x.Name == "Parent Workflow 1");
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnWorkflow_WhenIsParent()
    {
        // Arrange
        var workflow = _workflowFixture.WorkflowDto;
        await _repository.AddAsync(workflow);

        // Act
        var result = await _repository.GetByReferenceIdAsync(workflow.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(workflow.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnWorkflow_WhenIsNotParentAndSameCompany()
    {
        // Arrange
        var workflow = _workflowFixture.WorkflowDto;
        workflow.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(workflow);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(workflow.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(workflow.ReferenceId, result.ReferenceId);
        Assert.Equal("ChHILD_COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIsNotParentAndDifferentCompany()
    {
        // Arrange
        var workflow = _workflowFixture.WorkflowDto;
        workflow.CompanyId = "OTHER_COMPANY_456";
        await _repositoryNotParent.AddAsync(workflow);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(workflow.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetWorkflowNames_ShouldReturnAllWorkflows_WhenIsParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Parent Workflow",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            },
            new Workflow
            {
                Name = "Child Workflow",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            }
        };

        await _repository.AddRangeAsync(workflows);

        // Act
        var result = await _repository.GetWorkflowNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.Name == "Parent Workflow");
        Assert.Contains(result, x => x.Name == "Child Workflow");
    }

    [Fact]
    public async Task GetWorkflowNames_ShouldReturnFilteredWorkflows_WhenIsNotParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Parent Workflow",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = false
            },
            new Workflow
            {
                Name = "Child Workflow 1",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "b213cf2a-6b1d-479d-a76c-b711137b10a0",
                IsActive = true,
                IsPublish = true,
                CreatedBy="USER_Child1245"
            },
            new Workflow
            {
                Name = "Child Workflow 2",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true,
                CreatedBy="USER_Child1245"
            }
        };

        var permission = new WorkflowPermission
        {
            Type = "workflow",
            UserAccess = "users",
            UserProperties = "USER_Child1245",
            AccessType = "permanent",
            AccessProperties = "[{\"id\":\"b213cf2a-6b1d-479d-a76c-b711137b10a0\",\"name\":\"Test Workflow\"}]",
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedBy = "USER_Child1245",
            CreatedDate = DateTime.Now
        };

        await _dbContext.WorkflowPermissions.AddAsync(permission);
        await _dbContext.SaveChangesAsync();
        await _repositoryNotParent.AddRangeAsync(workflows);

        // Act
        var result = await _repositoryNotParent.GetWorkflowNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains("Child Workflow", x.Name));
        Assert.DoesNotContain(result, x => x.Name == "Parent Workflow");
    }

    [Fact]
    public async Task ListAllAsyncForDashboardView_ShouldFilterByCompany_WhenIsNotParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Parent Dashboard Workflow",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = false
            },
            new Workflow
            {
                Name = "Child Dashboard Workflow",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            }
        };

        await _repositoryNotParent.AddRangeAsync(workflows);

        // Act
        var result = await _repositoryNotParent.ListAllAsyncForDashboardView();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Child Dashboard Workflow", result.First().Name);
    }

    #endregion

    #region Additional Edge Cases Tests

    [Fact]
    public async Task GetWorkflowPropertiesByServerId_ShouldHandlePartialMatches()
    {
        // Arrange
        var serverId = "SERVER_123";
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Exact Match Workflow",
                Properties = $"{{\"serverId\": \"{serverId}\"}}",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new Workflow
            {
                Name = "Partial Match Workflow",
                Properties = $"{{\"data\": \"some data with {serverId} in middle\"}}",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new Workflow
            {
                Name = "No Match Workflow",
                Properties = "{\"serverId\": \"OTHER_SERVER\"}",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(workflows);

        // Act
        var result = await _repository.GetWorkflowPropertiesByServerId(serverId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.Name == "Exact Match Workflow");
        Assert.Contains(result, x => x.Name == "Partial Match Workflow");
        Assert.DoesNotContain(result, x => x.Name == "No Match Workflow");
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var workflows = _workflowFixture.WorkflowPaginationList.Take(5).ToList();
        workflows.ForEach(x => x.IsPublish = true);
        workflows.ForEach(x => x.IsActive = true);

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(workflows);
        var initialCount = workflows.Count;

        var toUpdate = workflows.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedWorkflowName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = workflows.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedWorkflowName").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldFilterByCompany_WhenIsNotParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Workflow 1",
                CompanyId = "ChHILD_COMPANY_123", // Should be included
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true,
                CreatedBy="USER_Child1245"
            },
            new Workflow
            {
                Name = "Workflow 2",
                CompanyId = "OTHER_COMPANY_456", // Should be excluded
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            },
            new Workflow
            {
                Name = "Workflow 3",
                CompanyId = "ChHILD_COMPANY_123", // Should be included
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true,
                CreatedBy="USER_Child1245"
            }
        };

        await _repositoryNotParent.AddRangeAsync(workflows);

        // Act
        var paginatedResult = _repositoryNotParent.GetPaginatedQuery().ToList();

        // Assert
        Assert.Equal(2, paginatedResult.Count);
        Assert.All(paginatedResult, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldNotFilterByCompany_WhenIsParent()
    {
        // Arrange
        var workflows = new List<Workflow>
        {
            new Workflow
            {
                Name = "Workflow 1",
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            },
            new Workflow
            {
                Name = "Workflow 2",
                CompanyId = "OTHER_COMPANY_456",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            },
            new Workflow
            {
                Name = "Workflow 3",
                CompanyId = "ANOTHER_COMPANY_789",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsPublish = true
            }
        };

        await _repository.AddRangeAsync(workflows);

        // Act
        var paginatedResult = _repository.GetPaginatedQuery().ToList();

        // Assert
        Assert.Equal(3, paginatedResult.Count);
        Assert.Contains(paginatedResult, x => x.CompanyId == "COMPANY_123");
        Assert.Contains(paginatedResult, x => x.CompanyId == "OTHER_COMPANY_456");
        Assert.Contains(paginatedResult, x => x.CompanyId == "ANOTHER_COMPANY_789");
    }

    #endregion
}
