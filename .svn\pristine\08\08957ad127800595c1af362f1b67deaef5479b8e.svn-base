﻿using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class AlertReceiverServiceFixture
{
    public List<AlertReceiverListVm> ListVm { get; }
    public AlertReceiverDetailVm DetailVm { get; }
    public CreateAlertReceiverCommand CreateCommand { get; }
    public UpdateAlertReceiverCommand UpdateCommand { get; }
    public BaseResponse Response { get; }
    public PaginatedResult<AlertReceiverListVm> PaginatedResult { get; }

    public AlertReceiverServiceFixture()
    {
        var fixture = new Fixture();

        ListVm = fixture.CreateMany<AlertReceiverListVm>(3).ToList();
        DetailVm = fixture.Create<AlertReceiverDetailVm>();
        CreateCommand = fixture.Create<CreateAlertReceiverCommand>();
        UpdateCommand = fixture.Create<UpdateAlertReceiverCommand>();
        Response = fixture.Create<BaseResponse>();
        PaginatedResult = fixture.Create<PaginatedResult<AlertReceiverListVm>>();
    }
}