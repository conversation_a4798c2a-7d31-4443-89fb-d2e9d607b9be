﻿using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetList;
using ContinuityPatrol.Services.Db.Impl.Manage;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Manage;

public class Db2HaDrMonitorStatusServiceTests : BaseServiceTestSetup<Db2HaDrMonitorStatusService>, IClassFixture<Db2HaDrMonitorStatusServiceFixture>
{
    private readonly Db2HaDrMonitorStatusServiceFixture _fixture;

    public Db2HaDrMonitorStatusServiceTests(Db2HaDrMonitorStatusServiceFixture fixture)
    {
        InitializeService(accessor => new Db2HaDrMonitorStatusService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateDb2HaDrMonitorStatusResponse
        {
            Message = "Created",
            Id = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateDb2HaDrMonitorStatusResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.UpdateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task GetAllDb2HaDrMonitorStatus_Should_Return_List()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<Db2HaDrMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ListResponse);

        var result = await ServiceUnderTest.GetAllDb2HaDrMonitorStatus();

        Assert.Equal(_fixture.ListResponse.Count, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var id = Guid.NewGuid().ToString();
        _fixture.DetailQuery.Id = id;

        MediatorMock.Setup(m => m.Send(It.Is<Db2HaDrMonitorStatusDetailQuery>(q => q.Id == id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailResponse);

        var result = await ServiceUnderTest.GetByReferenceId(id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DetailResponse.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByType_Should_Return_List()
    {
        var type = "TestType";
        _fixture.ByTypeQuery.Type = type;

        MediatorMock.Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ByTypeResponse);

        var result = await ServiceUnderTest.GetDb2HaDrMonitorStatusByType(type);

        Assert.Equal(_fixture.ByTypeResponse.Count, result.Count);
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_Should_Return_Paginated()
    {
        MediatorMock.Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResponse);

        var result = await ServiceUnderTest.GetPaginatedDb2HaDrMonitorStatus(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResponse.TotalCount, result.TotalCount);
    }
}
