const approvalPreventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

function toggleWizard(showFirst) {
    $('.firstWizard').toggleClass("d-none", !showFirst);
    $('.secondWizard').toggleClass("d-none", showFirst);
    $('#nextButton').toggleClass("d-none", !showFirst);
    $('#previousButton').toggleClass("d-none", showFirst);
    $('#saveProcessName').toggleClass("d-none", showFirst);
}

function removeObjectByPropsId(obj, targetPropsId) {
    if (!obj || typeof obj !== 'object') return obj;
    if (obj.propsId === targetPropsId) {
        return Array.isArray(obj.properties) && obj.properties.length > 0 ? obj.properties[0] : null;
    }

    if (Array.isArray(obj.properties)) {
        obj.properties = obj.properties
            .map(child => removeObjectByPropsId(child, targetPropsId))
            .filter(Boolean);
    }
    return obj;
}

async function GetBusinessServiceList(UserApproval = null) {
    try {
        const result = await $.ajax({
            type: "GET",
            url: `${RootUrl}Manage/Approval/GetBusinessServiceList`,
            dataType: "json",
            traditional: true
        });

        const $select = $('#selectOperationalService');
        $select.empty().append('<option value=""></option>');

        if (Array.isArray(result) && result.length > 0) {
            result.forEach(({ id, name }) => {
                $select.append(`<option value="${id}">${name}</option>`);
            });

            if (UserApproval?.businessServiceProperties) {
                $select.val(UserApproval.businessServiceProperties).trigger('change');
            }
        } else {
            errorNotification("No business services found.");
        }
    } catch (error) {
        errorNotification("Failed to fetch business services.");
        console.error("AJAX error:", error);
    }
}

function replaceObjectIfEmptyName(obj, newObject) {
    if (!obj || typeof obj !== 'object') return obj;
    const cloned = structuredClone ? structuredClone(obj) : JSON.parse(JSON.stringify(obj));
    if (Array.isArray(cloned.properties)) {
        cloned.properties = cloned.properties.map(property =>
            property.name === ""
                ? newObject
                : replaceObjectIfEmptyName(property, newObject)
        );
    }
    return cloned;
}


function clearFieldsAfterSace() {
    $("#approvalProcessName, #approvalTemplateName").val("");
    $("#approvalProcessNameError, #approvalTemplateNameError")
        .text("")
        .removeClass("field-validation-error");
    $("#createTemplateModal, #saveprocessModal").modal("hide");
}

function updateByPropsId(obj, propsID, newData) {
    if (obj === null || typeof obj !== 'object') return obj;

    if (obj.propsId === propsID) {
        return { ...obj, ...newData };
    }

    if (Array.isArray(obj)) {
        return obj.map(item => updateByPropsId(item, propsID, newData));
    }

    const updatedObj = {};
    for (const key in obj) {
        updatedObj[key] = updateByPropsId(obj[key], propsID, newData);
    }

    return updatedObj;
}

function createBlankNode() {
    return {
        name: "",
        properties: [],
        userLists: [],
        user: "",
        ruleSet: [],    
        SLA: {},
        notification: []
    };
}

function createEndConnectorLine(container, lastDiv) {
    const endArrowId = getRandomId("arrow");
    const endPolylineId = getRandomId("polyline");
    const position = lastDiv.position();
    const width = lastDiv.outerWidth();
    const height = lastDiv.outerHeight();
    const centerX = position.left + width / 2;
    const startY = position.top + height;
    const endY = position.top + height * 2;
    const points = [[centerX, startY], [centerX, endY]];

    const svgLine = `
        <svg id="endsvg" style="overflow:visible; position:absolute; top:0; left:0;">
            <defs>
                <marker id="${endArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 Z" fill="black" />
                </marker>
            </defs>
            <polyline class="${endPolylineId}" 
                      points="${points}" 
                      fill="none" stroke="green" stroke-width="1"
                      stroke-dasharray="4,4"
                      marker-end="url(#${endArrowId})" />
        </svg>`;

    container.append(svgLine);
}
function validateProcessForm() {
    const isValidName = commonInputValidation(
        $("#approvalProcessName").val(),
        $("#approvalProcessNameError"),
        "Enter process name"
    );

    const isValidDuration = commonInputValidation(
        $("#inputDuration").val(),
        $("#enterDurationError"),
        "Enter duration"
    );

    const isValidSelectDuration = commonInputValidation(
        $("#selectDuration").val(),
        $("#selectDurationError"),
        "Select duration"
    );


    const msg = $('#userList').prop('checked') ? "Select user" : "Select user group";
    let user = $("#userNameList").val();
    let usergroup = $("#userGroupNameList").val();
    const userValue = user.length > 0 ? user : usergroup;
    const isValidUser = userValue.length > 0
        ? true
        : commonInputValidation("", $("#UserNameError"), msg);

    return { isValidName, isValidDuration, isValidSelectDuration, isValidUser, userValue };
}

function getSelectedUsers() {
    const selected = [];
    if ($('#userList').prop('checked')) {
        $("#userNameList :selected").each(function () {
            const id = $(this).val();
            const name = $(this).text();
            if (id) {
                selected.push({ id, name });
                selectedUsersArrayLists.push(id);
            }
        });
    } else {
        $("#userGroupNameList :selected").each(function () {
            const id = $(this).val();
            const name = $(this).text();
            if (id) {
                selected.push({ id, name });
                selectedUsersArrayLists.push(id);
            }
        });
    }

   
    return selected;
}

function resetTemplateState() {
    userListsLength = "";
    addTransition = true;
    editWhileCreate = false;
    startButtonValue = 1;
    approvalTemplateName = "";
    businessServiceName = "";
    businessFunctionName = "";
    editedData = "";
    propertiesID = "";
    editedProcessName = "";
}

function resetTemplateUI() {
    $("#templateTitle").text("Untitled Template");
    $("#saveProcessName").text("Add");
    $("#saveApprovalTemplate").text("Save").css("visibility", "hidden");
    $("#saveTemplateName").text("Save");
    $("#approvalContainer").empty();
    $("#templateID").val("");
}

function initBlankTemplateTree() {
    processNameArray = {
        propsId: "",
        name: "",
        straightLineData: "",
        properties: [],
        userLists: [],
        ruleSet: [],
        SLA: {},
        notification: []
    };
}


function updateProcessDeleteButtonVisibility(flatList) {
    $("#processDelete").toggle(flatList.length !== 1);
}

function resetApprovalContainer() {
    $("#approvalContainer").empty();
    startButtonValue = 1;
    addTransition = false;
}
function getRuleObject(typeSelector, countSelector, totalSelector) {
    return {
        type: $(typeSelector).val(),
        ruleCount: $(countSelector).val(),
        approverCount: $(totalSelector).text()
    };
}

function validateRuleFields() {
    const appOneValid = commonInputValidation($("#ApOne").val(), $("#ApOneError"), "Select option");
    const appTwoValid = commonInputValidation($("#ApTwo").val(), $("#ApTwoError"), "Enter approvers");
    const rejOneValid = commonInputValidation($("#RjOne").val(), $("#RjOneError"), "Select option");
    const rejTwoValid = commonInputValidation($("#RjTwo").val(), $("#RjTwoError"), "Enter approvers");
    return appOneValid && appTwoValid && rejOneValid && rejTwoValid;
}

function buildNotificationObject() {
    return {
        email: $('#emailIcon').is(':checked'),
        sms: $('#messageIcon').is(':checked'),
        application: true
    };
}

function updateRuleCountsIfChanged(userValue) {
    const userCountChanged = userListsLength !== userValue.length;
    const isUpdating = $("#saveProcessName").text() === "Update";

    if (!userCountChanged || !isUpdating) return;

    const selectedApType = $("#ApOne option:selected").text();
    const selectedRjType = $("#RjOne option:selected").text();

    if (selectedApType === "All") {
        $("#ApTwo").val(userValue.length);
    } else {
        $("#ApTwo").val("");
    }

    if (selectedRjType === "All") {
        $("#RjTwo").val(userValue.length);
    } else if (["Majority", "Custom"].includes(selectedRjType)) {
        $("#RjTwo").val("");
    }
}
function createEndButton() {
    return `
        <div id="endbutton" class="endButton"
             style="border-radius:20px; font-weight:bold; box-shadow:0 2px 5px rgba(0,0,0,0.2);">
            <img title="End" src="/img/input_Icons/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image">
        </div>`;
}
function addProcessNode(newName, userRadio, textDescription,selectedUsers,enterDuration,selectDuration,approvalObject,rejectObject,notification,tree = processNameArray) {
    if (!tree.properties) tree.properties = [];

    const isEmptyNode = tree.name === "";

    if (isEmptyNode) {
        Object.assign(tree, {
            name: newName,
            propsId: getRandomId('propsId'),
            userLists: selectedUsers,
            description: textDescription,
            user: userRadio,
            SLA: { duration: enterDuration, period: selectDuration },
            ruleSet: [approvalObject, rejectObject],
            notification: [notification],
        });
     
        tree.properties.push(createBlankNode());
        return true;
    }

    for (const child of tree.properties) {
        if (addProcessNode(newName,userRadio,textDescription,selectedUsers,enterDuration,selectDuration,approvalObject,rejectObject,notification,child)) {
            return true;
        }
    }
   return false;
}

async function IsNameExist(url, data, errorFunc) {
    return !data.name?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function templateNameValidation(value, id = null, nameExistURL, errorelement, nullerror) {

    if (!value) {
        errorelement.text(nullerror).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorelement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = { name: value, id: id };
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorelement, validationResults);
};

function generateStartNode() {
    const startSVGId = getRandomId('startsvg');
    const startEndArrowId = getRandomId('arrow');
    const startPolylineId = getRandomId('polyline');

    const $container = $("#approvalContainer");
    const startHTML = `
        <div id="startTemplate"
             style="margin-bottom:2.5rem; border-radius:5px; font-weight:bold; position:relative;">
            <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start">
        </div>`;
    $container.append(startHTML);

    const $startBox = $("#startTemplate");
    const boxWidth = $startBox.outerWidth();
    const boxHeight = $startBox.outerHeight();
    const marginBottom = 2.5 * 16;
    const position = $startBox.position();
    const centerX = position.left + (boxWidth / 2);
    const points = [[centerX, boxWidth], [centerX, boxHeight + marginBottom]];

    const svgLine = `
        <svg id="${startSVGId}" style="overflow:visible; position:absolute; top:0; left:0;">
            <defs>
                <marker id="${startEndArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 Z" fill="black" />
                </marker>
            </defs>
            <polyline class="polyline${startPolylineId}" 
                      points="${points}" 
                      fill="none" stroke="green" stroke-width="1" 
                      stroke-dasharray="4,4" 
                      marker-end="url(#${startEndArrowId})" />
        </svg>`;
    $container.append(svgLine);
}

function generateConnectorLine() {
    const processLineId = getRandomId("process-line");
    const svgId = getRandomId("process-svg");
    const $container = $("#approvalContainer");
    const $lastDiv = $container.children("div:last");
    const position = $lastDiv.position();
    const offset = $lastDiv.offset();
    const width = $lastDiv.outerWidth();
    const height = $lastDiv.outerHeight();
    const topOffset = offset.top - position.top;
    const svgTop = offset.top - topOffset + height + 5;
    const polylinePoints = [[width / 2, 0], [width / 2, height - 10]];

    const svg = `
        <svg id="${svgId}" style="position: absolute; top: ${svgTop}; left: ${position.left}; height: ${height - 10}px; width: ${width}px">
            <defs>
                <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 Z" fill="black"/>
                </marker>
            </defs>
            <polyline id="${processLineId}"
                      fill="none"
                      stroke="green"
                      stroke-width="1"
                      stroke-dasharray="4,4"
                      marker-end="url(#arrow)" />
        </svg>`;
    $container.append(svg);
    $(`#${processLineId}`).attr("points", polylinePoints);
}

function generateProcessBox(id, title, templateData) {
    return `
        <div id="${id}" 
             role="button"
             class="process-box" 
             title='${title}'
             data-template='${JSON.stringify(templateData)}'
             style="display: flex; align-items: center; justify-content: center; margin-bottom:2.5rem;
                    min-height: 40px; width:200px; height:40px; background-color:#dbf5ff;
                    border:1px solid #87afc6; border-radius:8px; cursor: pointer; font-weight:bold;">
            <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: block; max-width: 80%;">
                <i class="cp-template-levels me-2"></i>${title}
            </span>
        </div>`;
}

async function createEditTemplate(
    data, userRadio, textDescription, selectedUsers, enterDuration, selectDuration,
    approvalObject, rejectObject, notification
) {
    // Clean up end node if present
    $(".endButton").remove();
    $("#endsvg").remove();

    const processName = data?.name ?? data;
    const processBoxId = getRandomId('processBox');
    const $container = $("#approvalContainer");

    $("#closeOffcanvas").trigger("click");
    $container.scrollTop(0);

    if (startButtonValue === 1) {
        generateStartNode();
        startButtonValue = 2;
    } else {
        generateConnectorLine();
    }

    $container.append(generateProcessBox(processBoxId, processName, data));

    // Update titlesArray with unique process titles
    titlesArray = [...new Set(
        Array.from(document.querySelectorAll('.process-box')).map(div =>
            div.getAttribute('title'))
    )];

    // Scroll to latest appended element
    $container.scrollTop($container[0].scrollHeight);

    // Add to data tree if it's a new addition
    if ($("#saveProcessName").text() === addProcessName) {
        addProcessNode(
            processName, userRadio,textDescription, selectedUsers, enterDuration,
            selectDuration, approvalObject, rejectObject, notification
        );
    }
}


function getUsersIdWhileEdit(data) {
    data.forEach(level => {
        if (Array.isArray(level.userLists)) {
            level.userLists.forEach(user => {
                selectedUsersArrayLists.push(user.id);
            });
        }
    });
}

function handleTemplateEdit(templateData) {
    if (!templateData) return;

    // Destructure relevant fields from the template object
    const {
        id,
        name,
        properties,
        businessServiceId,
        businessFunctionId
    } = templateData;

    // Reset state and UI
    selectedUsersArrayLists = [];
    editWhileCreate = false;
    addTransition = false;
    startButtonValue = 1;

    processNameArray = {
        propsId: "",
        name: "",
        straightLineData: "",
        properties: [],
        userLists: [],
        ruleSet: [],
        SLA: {},
        notification: []
    };

    // UI Updates
    $("#addTransition").prop("disabled", false);
    $("#templateTitle").text(name);
    $("#approvalContainer").empty();
    $("#saveProcessName").text("Update");
    $("#saveApprovalTemplate").text("Update").css("visibility", "visible");
    $("#saveTemplateName").text("Update");
    $("#approvalTemplateName").val(name);
    $("#selectOperationalService").val(businessServiceId).trigger("change");
    $("#createTemplateModal").modal("show");
    $("#templateID").val(id);

    // Global state
    approvalTemplateName = name;
    businessServiceName = businessServiceId;
    businessFunctionName = businessFunctionId;

    // Parse and process properties
    const parsedProperties = JSON.parse(properties);
    getUsersIdWhileEdit(parsedProperties);

    $("#processDelete").toggle(parsedProperties.length !== 1);

    // Rebuild original structure and recursively render
    const original = rebuildOriginalStructure(parsedProperties);
    editedData = original;
    propertiesID = "";
    editedProcessName = "";

    setTimeout(() => recursiveEdit(original), 500);
}



function flattenPropertiesWithRoot(input) {
    const flat = [];

    if (!input || typeof input !== 'object') return flat;

    const { properties = [], straightLineData = [], ...root } = input;

    flat.push({ ...root, straightLineData });

    function traverse(propsArray) {
        for (const { name, properties: childProps = [], ...rest } of propsArray) {
            if (name !== "") {
                flat.push({ name, ...rest });
                if (Array.isArray(childProps) && childProps.length > 0) {
                    traverse(childProps);
                }
            }
        }
    }

    traverse(properties);
    return flat;
}


function rebuildOriginalStructure(array) {
    if (!Array.isArray(array) || array.length === 0) return {};
    let result = { name: "" };

    for (let i = array.length - 1; i >= 0; i--) {
        result = {
            ...array[i],
            properties: [result]
        };
    }
    return result;
}

function recursiveEdit(data, editWhileCreate = null) {

    if (data.name) {
        createEditTemplate(data);

        if (data.properties && Array.isArray(data.properties)) {
            data.properties.forEach(function (child) {
                recursiveEdit(child, editWhileCreate);
            });
        }
    } else if (!editWhileCreate) {
        $('#endTemplate').trigger("click");
    }
}

function findObjectsByName(obj, targetName) {
    let results = [];

    if (obj.name === targetName) {
        results.push(obj);
    }
    if (Array.isArray(obj.properties)) {
        obj.properties.forEach(property => {
            results = results.concat(findObjectsByName(property, targetName));
        });
    }
    return results;
}

function toggleDropdowns() {
    if ($('#userList').prop('checked')) {
        $('#userNameList').closest('.input-group').removeClass('d-none');
        $('#userGroupNameList').closest('.input-group').addClass('d-none');
   
    } else if ($('#userGroupList').prop('checked')) {
        $('#userNameList').closest('.input-group').addClass('d-none');
        $('#userGroupNameList').closest('.input-group').removeClass('d-none');
     
    }
}


async function getUsersData(edit = null, selectedUserLists,Type) {
    await $.ajax({
        type: "GET",
        url: RootUrl + approvalURL.usersList,
        data: { },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.length > 0) {

                const userTypeList = result.filter(item => item.type === Type);

                let options = [];
                const userID = $("#userLoginName").attr("userid");
                const userNameList = (Type === "Individual")
                    ? $('#userNameList')
                    : $('#userGroupNameList');

                userNameList.empty();
                userTypeList?.forEach(function (item) {
                    if (Type == "Individual") {               
                        if (userID !== item.userId) {
                            if (edit === "edit") {
                                if (!selectedUserLists.includes(item.userId)) {
                                    options.push($('<option>').val(item.userId).text(item.userName));
                                }
                            } else {
                                if (selectedUsersArrayLists?.length && !selectedUsersArrayLists.includes(item.userId)) {
                                    options.push($('<option>').val(item.userId).text(item.userName));
                                } else if (selectedUsersArrayLists?.length === 0) {
                                    options.push($('<option>').val(item.userId).text(item.userName));
                                }
                            }
                        }
                    } else {
                   
                        if (userID !== item.id) {
                            if (edit === "edit") {
                                if (!selectedUserLists.includes(item.id)) {
                                    options.push($('<option>').val(item.id).text(item.userName));
                                }
                            } else {
                                if (selectedUsersArrayLists?.length && !selectedUsersArrayLists.includes(item.id)) {
                                    options.push($('<option>').val(item.id).text(item.userName));
                                } else if (selectedUsersArrayLists?.length === 0) {
                                    options.push($('<option>').val(item.id).text(item.userName));
                                }
                            }
                        }
                    }
                  
                });
                userNameList.append(options)
            }
            else {
                errorNotification(result);
            }
        }
    })
}

function collectUserLists(obj) {
    let collected = [];

    if (Array.isArray(obj)) {
        for (const item of obj) {
            collected = collected.concat(collectUserLists(item));
        }
    } else if (typeof obj === 'object' && obj !== null) {
        if (obj.userLists && Array.isArray(obj.userLists)) {
            collected = collected.concat(obj.userLists);
        }
        for (const key in obj) {
            collected = collected.concat(collectUserLists(obj[key]));
        }
    }
    return collected;
}

function getUserIdsFromList(userList = []) {
    return userList.map(user => user.id);
}

function clearValidationErrors() {
    $("#approvalProcessNameError, #enterDurationError, #ApTwoError, #RjTwoError")
        .text("")
        .removeClass("field-validation-error");
}

async function populateProcessForm(data, idArray, newSelectableIds) {
    edit = "edit";
    propertiesID = data?.propsId;
    editedProcessName = data?.name;
    userListsLength = idArray.length;

    $("#approvalProcessName").val(data?.name);

    if (data.user === "user") {
        $('#userList').prop('checked', true);
        $('#userGroupList').prop('checked', false);
        $('#dynamicLabel').html("User List");      
        $('#userGroupNameList').val("").empty();
        $("#UserNameError").text("").removeClass("field-validation-error");

    } else if (data.user === "usergroup") {   
        $('#userGroupList').prop('checked', true);
        $('#userList').prop('checked', false);
        $('#dynamicLabel').html("User Group List");
        $('#userNameList').val("").empty();
        $("#UserNameError").text("").removeClass("field-validation-error");
    } 
    toggleDropdowns();
    if (data.user === "user" || data.user === "usergroup") {
        const isUser = data.user === "user";
        const mode = isUser ? "Individual" : "Group     ";    
        getUsersData(edit, newSelectableIds, mode).then(async () => {
            if (mode == "Individual") {
                $("#userNameList").val(idArray);
            }
            else {
                $("#userGroupNameList").val(idArray);
            }
        });
    }
 
    $("#textDescription").val(data?.description);
    $("#inputDuration").val(data?.SLA?.duration);
    $("#selectDuration").val(data?.SLA?.period).trigger("change");

    const [approvalRule, rejectRule] = data?.ruleSet || [];
    $("#ApOne").val(approvalRule?.type).trigger("change");
    $("#ApTwo").val(approvalRule?.ruleCount);
    $("#RjOne").val(rejectRule?.type).trigger("change");
    $("#RjTwo").val(rejectRule?.ruleCount);

    const [notification = {}] = data?.notification || [];
    $("#emailIcon").prop("checked", notification.email);
    $("#messageIcon").prop("checked", notification.sms);
    $("#applicationIcon").prop("checked", notification.application);

    $("#selectOperationalService").val(data?.businessServiceId).trigger("change");
    $("#processDelete").removeClass("d-none");
    $("#saveProcessName").text("Update");
    setTimeout(() => $("#offcanvasExample").offcanvas("show"), 500);

}

$(document).on('click', '.process-box', async function () {
   
    $("#offcanvasExample").offcanvas("hide");
    const $this = $(this);
    let data = JSON.parse($this.attr("data-template"));

    // Handle string reference (name lookup)
    if (typeof data === "string") {
        const results = findObjectsByName(processNameArray, data);
        data = results[0];
        editWhileCreate = true;
    }

    const idArray = getUserIdsFromList(data?.userLists || []);
    let selectedUserLists = [];

    const saveMode = $("#saveApprovalTemplate").text().toLowerCase();
    if (saveMode === "save") {
        selectedUserLists = collectUserLists(processNameArray);
    } else if (saveMode === "update") {
        selectedUserLists = collectUserLists(editedData);
    }

    const existingIds = selectedUserLists.map(user => user.id);
    const newSelectableIds = existingIds.filter(id => !idArray.includes(id));

 /*   await getUsersData("edit", newSelectableIds);*/

    toggleWizard(true);
    clearValidationErrors();
    populateProcessForm(data, idArray, newSelectableIds);
});


function handleApproverRule(value) {
    const $apTwo = $("#ApTwo");
    const $apTwoError = $("#ApTwoError");

    if (value === "All") {
        $apTwo.val($("#ApThree").text());
        $apTwo.prop("disabled", true);
        $apTwoError.text("").removeClass("field-validation-error");
    } else {
        $apTwo.prop("disabled", false);
    }
}

function handleRejectRule(value) {
    const $rjTwo = $("#RjTwo");
    const $rjTwoError = $("#RjTwoError");

    if (value === "All") {
        $rjTwo.val($("#RjThree").text()).prop("disabled", true);
        $rjTwoError.text("").removeClass("field-validation-error");
    } else if (value === "At Least One") {
        $rjTwo.val(1).prop("disabled", true);
        $rjTwoError.text("").removeClass("field-validation-error");
    } else {
        $rjTwo.prop("disabled", false);
    }
}

function commonInputValidation(value, errorElement, errorMessage, status = null) {
    if (!value) {
        errorElement.text(errorMessage).addClass("field-validation-error");
        return false;
    }

    switch (status) {
        case "approver":
            handleApproverRule(value);
            break;
        case "reject":
            handleRejectRule(value);
            break;
    }

    errorElement.text("").removeClass("field-validation-error");
    return true;
}


//function addEditProcessLine(fromName, toName) {      //transitionName, transitionColour
//    let processLine = getRandomId("process-line");
//    let processSvg = getRandomId("process-svg");
//    let container = $("#approvalContainer");
//    let fromElement = $(`div[title='${fromName}']`);
//    let toElement = $(`div[title='${toName}']`);

//    let fromNamePosition = fromElement.position();
//    let fromNameWidth = fromElement.outerWidth();
//    let fromNameHeight = fromElement.outerHeight();

//    let toNamePosition = toElement.position();
//    let toNameWidth = toElement.outerWidth();
//    let toNameHeight = toElement.outerHeight();

//    // will be use
//    //let fromRightCenter = {
//    //    x: fromNamePosition.left + fromNameWidth,
//    //    y: fromNamePosition.top + fromNameHeight / 2
//    //};

//    //let toLeftCenter = {
//    //    x: toNamePosition.left + toNameWidth,
//    //    y: toNamePosition.top + toNameHeight / 2
//    //};

//    //let rightOffsetX = fromRightCenter.x + 200;

//    //let polylinePoints = `
//    //                        ${fromRightCenter.x},${fromRightCenter.y}
//    //                        ${rightOffsetX},${fromRightCenter.y}
//    //                        ${rightOffsetX},${toLeftCenter.y}
//    //                        ${toLeftCenter.x},${toLeftCenter.y}
//    //                    `.trim();

//    let heightOffset = 26;

//    let fromCenter = {
//        x: fromNamePosition?.left, // + fromNameWidth / 2,
//        y: fromNamePosition?.top + fromNameHeight / 2 + heightOffset
//    };

//    let toCenter = {
//        x: toNamePosition?.left + toNameWidth / 2,
//        y: toNamePosition?.top + toNameHeight / 2 - heightOffset
//    };

//    //let polylinePoints = `${fromCenter.x},${fromCenter.y} ${toCenter.x},${toCenter.y}`;
//    let polylinePoints = [[fromNameWidth / 2, 0], [fromNameWidth / 2, fromNameHeight - 10]]; //margin bottom 40px for every level.

//    //Note: if give height and width 100% for svg means level mouse click is not working.

//    //let svg = $(`#${processSvg}`);

//    //if (svg.length === 0) {
//    container.append(`
//        <svg id="${processSvg}" style="position: absolute; top: ${fromCenter.y}; left: ${fromCenter.x}; height: ${fromNameHeight - 10}px; width: ${fromNameWidth}px">
//            <defs>
//                <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
//                    <path d="M0,0 L10,5 L0,10 Z" fill="black"/>
//                </marker>
//            </defs>
//            <polyline id="${processLine}"
//                      fill="none"
//                      stroke="green"
//                      stroke-width="1"
//                      stroke-dasharray="4, 4"
//                      marker-end="url(#arrow)" />
//        </svg>
//    `);
//    //}

//    $(`#${processLine}`).attr("points", polylinePoints);

//    $("#addTransitionModal").modal("hide");
//}

//function addProcessLine(fromName, toName) {  //, transitionName, transitionColour
//    straightLineData.push({ lineId: getRandomId('propsId'), fromName: fromName, toName: toName }); //, transitionName: transitionName, transitionColour: transitionColour
//    //if (editedstraightLine.length) {
//    //    editedstraightLine.push({ lineId: getRandomId('propsId'), fromName: fromName, toName: toName }); //, transitionName: transitionName, transitionColour: transitionColour
//    //}
//    //addEditProcessLine(fromName, toName); //, transitionName, transitionColour
//}
