﻿using ContinuityPatrol.Application.Features.MonitorService.Queries.GetDetail;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Shared.Core.Exceptions;
using FluentAssertions;

namespace ContinuityPatrol.Application.UnitTests.Features.MonitorService.Queries
{
    public class GetMonitorServiceDetailQueryHandlerTests
    {
        private readonly Mock<IMonitorServiceRepository> _monitorServiceRepositoryMock;
        private readonly IMapper _mapper;
        private readonly GetMonitorServiceDetailQueryHandler _handler;

        public GetMonitorServiceDetailQueryHandlerTests()
        {
            _monitorServiceRepositoryMock = new Mock<IMonitorServiceRepository>();

            var configuration = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MonitorServiceProfile>(); // Use your actual AutoMapper profile here
            });
            _mapper = configuration.CreateMapper();

            _handler = new GetMonitorServiceDetailQueryHandler(_mapper, _monitorServiceRepositoryMock.Object);
        }

        [Fact]
        public async Task Should_Return_MonitorServiceDetail_When_Exists()
        {
            // Arrange
            var id = 123;
            var monitorService = new Domain.Entities.MonitorService
            {
                Id = id,
                InfraObjectName = "Infra_1",
                BusinessServiceName = "Business_1",
                ServerName = "Server_1",
                Status = "Active"
            };

            _monitorServiceRepositoryMock
                .Setup(repo => repo.GetByReferenceIdAsync(id.ToString()))
                .ReturnsAsync(monitorService);

            var query = new GetMonitorServiceDetailQuery { Id = id.ToString() };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.InfraObjectName.Should().Be("Infra_1");
            result.BusinessServiceName.Should().Be("Business_1");
            result.ServerName.Should().Be("Server_1");
            result.Status.Should().Be("Active");
        }

        [Fact]
        public async Task Should_Map_Existing_MonitorService_Properties_To_ViewModel()
        {
            // Arrange
            var monitorService = new Domain.Entities.MonitorService
            {
                Id = 123,
                ReferenceId = "123",
                BusinessServiceId = "BS001",
                BusinessServiceName = "Business Service",
                InfraObjectId = "INF001",
                InfraObjectName = "Infra Object",
                ServerId = "SRV001",
                ServerName = "Server One",
                Type = "Custom",
                Status = "Active",
                IsServiceUpdate = "true",
                NodeId = "NODE01",
                NodeName = "Node One",
                LastExecutionTime = "2025-07-28T10:00:00Z",
                Properties = "{\"interval\":5}",
                MonitoringType = "Heartbeat",
                CompanyId = "COMP01"
            };

            _monitorServiceRepositoryMock
                .Setup(r => r.GetByReferenceIdAsync("123"))
                .ReturnsAsync(monitorService);

            var query = new GetMonitorServiceDetailQuery { Id = "123" };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be("123");
            result.BusinessServiceId.Should().Be("BS001");
            result.BusinessServiceName.Should().Be("Business Service");
            result.InfraObjectId.Should().Be("INF001");
            result.InfraObjectName.Should().Be("Infra Object");
            result.ServerId.Should().Be("SRV001");
            result.ServerName.Should().Be("Server One");
            result.Type.Should().Be("Custom");
            result.Status.Should().Be("Active");
            result.IsServiceUpdate.Should().Be("true");
            result.NodeId.Should().Be("NODE01");
            result.NodeName.Should().Be("Node One");
            result.LastExecutionTime.Should().Be("2025-07-28T10:00:00Z");
            result.Properties.Should().Be("{\"interval\":5}");
            result.MonitoringType.Should().Be("Heartbeat");
        }


        [Fact]
        public async Task Should_Throw_NotFoundException_When_Mapper_Returns_Null()
        {
            // Arrange
            var monitorService = new Domain.Entities.MonitorService { Id = 789 };

            _monitorServiceRepositoryMock
                .Setup(repo => repo.GetByReferenceIdAsync("789"))
                .ReturnsAsync(monitorService);

            var mapperMock = new Mock<IMapper>();
            mapperMock.Setup(m => m.Map<GetMonitorServiceDetailVm>(It.IsAny<Domain.Entities.MonitorService>()))
                .Returns((GetMonitorServiceDetailVm)null!); // Simulate mapping failure

            var handler = new GetMonitorServiceDetailQueryHandler(mapperMock.Object, _monitorServiceRepositoryMock.Object);

            var query = new GetMonitorServiceDetailQuery { Id = "789" };

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(query, CancellationToken.None));
        }

        [Fact]
        public async Task Should_Call_Repository_Once()
        {
            // Arrange
            var id = 001;
            _monitorServiceRepositoryMock
                .Setup(repo => repo.GetByReferenceIdAsync(id.ToString()))
                .ReturnsAsync(new Domain.Entities.MonitorService { Id = id });

            var query = new GetMonitorServiceDetailQuery { Id = id.ToString() };

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _monitorServiceRepositoryMock.Verify(repo => repo.GetByReferenceIdAsync(id.ToString()), Times.Once);
        }

        [Fact]
        public async Task Should_Throw_NotFoundException_When_Id_Is_Null()
        {
            // Arrange
            var query = new GetMonitorServiceDetailQuery { Id = null! };

            _monitorServiceRepositoryMock
                .Setup(repo => repo.GetByReferenceIdAsync(null!))
                .ReturnsAsync((Domain.Entities.MonitorService)null!);

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));
        }
        [Fact]
        public void Should_SetAndGet_AllPropertiesCorrectly()
        {
            // Arrange
            var vm = new GetMonitorServiceDetailVm
            {
                Id = "1",
                BusinessServiceId = "BS001",
                BusinessServiceName = "BusinessService",
                InfraObjectId = "INFRA01",
                InfraObjectName = "InfraObject",
                ServerId = "SRV01",
                ServerName = "Server",
                WorkflowId = "WF001",
                WorkflowName = "Workflow",
                ServicePath = "/service/path",
                Type = "HTTP",
                ThreadType = "Worker",
                Status = "Running",
                WorkflowVersion = "v1.0",
                WorkflowType = "Standard",
                IsServiceUpdate = "True",
                NodeId = "NODE001",
                NodeName = "Node1",
                FailedActionId = "ACT001",
                FailedActionName = "RestartService",
                LastExecutionTime = "2025-07-28T10:00:00Z",
                Properties = "{\"threshold\":90}",
                MonitoringType = "Scheduled"
            };

            // Assert
            Assert.Equal("1", vm.Id);
            Assert.Equal("BS001", vm.BusinessServiceId);
            Assert.Equal("BusinessService", vm.BusinessServiceName);
            Assert.Equal("INFRA01", vm.InfraObjectId);
            Assert.Equal("InfraObject", vm.InfraObjectName);
            Assert.Equal("SRV01", vm.ServerId);
            Assert.Equal("Server", vm.ServerName);
            Assert.Equal("WF001", vm.WorkflowId);
            Assert.Equal("Workflow", vm.WorkflowName);
            Assert.Equal("/service/path", vm.ServicePath);
            Assert.Equal("HTTP", vm.Type);
            Assert.Equal("Worker", vm.ThreadType);
            Assert.Equal("Running", vm.Status);
            Assert.Equal("v1.0", vm.WorkflowVersion);
            Assert.Equal("Standard", vm.WorkflowType);
            Assert.Equal("True", vm.IsServiceUpdate);
            Assert.Equal("NODE001", vm.NodeId);
            Assert.Equal("Node1", vm.NodeName);
            Assert.Equal("ACT001", vm.FailedActionId);
            Assert.Equal("RestartService", vm.FailedActionName);
            Assert.Equal("2025-07-28T10:00:00Z", vm.LastExecutionTime);
            Assert.Equal("{\"threshold\":90}", vm.Properties);
            Assert.Equal("Scheduled", vm.MonitoringType);
        }
    }
}
