﻿using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionFieldMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class WorkflowActionFieldMasterServiceFixture : IDisposable
{
    public List<WorkflowActionFieldMasterListVm> WorkflowActionFieldMasterListVm { get; }
    public WorkflowActionFieldMasterDetailVm WorkflowActionFieldMasterDetailVm { get; }
    public PaginatedResult<WorkflowActionFieldMasterListVm> PaginatedListVm { get; }
    public CreateWorkflowActionFieldMasterCommand CreateCommand { get; }
    public UpdateWorkflowActionFieldMasterCommand UpdateCommand { get; }
    public GetWorkflowActionFieldMasterPaginatedListQuery PaginatedListQuery { get; }
    public GetWorkflowActionFieldMasterDetailQuery DetailQuery { get; }

    public WorkflowActionFieldMasterServiceFixture()
    {
        var fixture = new Fixture();

        WorkflowActionFieldMasterListVm = fixture.Create<List<WorkflowActionFieldMasterListVm>>();
        WorkflowActionFieldMasterDetailVm = fixture.Create<WorkflowActionFieldMasterDetailVm>();
        PaginatedListVm = fixture.Create<PaginatedResult<WorkflowActionFieldMasterListVm>>();
        CreateCommand = fixture.Create<CreateWorkflowActionFieldMasterCommand>();
        UpdateCommand = fixture.Create<UpdateWorkflowActionFieldMasterCommand>();
        PaginatedListQuery = fixture.Create<GetWorkflowActionFieldMasterPaginatedListQuery>();
        DetailQuery = fixture.Create<GetWorkflowActionFieldMasterDetailQuery>();
    }

    public void Dispose()
    {
        // Cleanup logic if needed
    }
}
