﻿using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Events.UpdateConditionActionId;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberJobWorkflowScheduler.Commands;
public class CyberJobWorkflowSchedulerCommandHandlerTests
{
    private readonly Mock<ICyberJobWorkflowSchedulerRepository> _repositoryMock;
    private readonly Mock<IPublisher> _publisherMock;
    private readonly CyberJobWorkflowSchedulerCommandHandler _handler;

    public CyberJobWorkflowSchedulerCommandHandlerTests()
    {
        _repositoryMock = new Mock<ICyberJobWorkflowSchedulerRepository>();
        _publisherMock = new Mock<IPublisher>();
        _handler = new CyberJobWorkflowSchedulerCommandHandler(_repositoryMock.Object, _publisherMock.Object);
    }

    [Fact(DisplayName = "Handle_ValidCommand_ShouldUpdateConditionActionIdAndPublishEvent")]
    public async Task Handle_ValidCommand_ShouldUpdateConditionActionIdAndPublishEvent()
    {
        // Arrange
        var jobId = "job-001";
        var request = new CyberJobWorkflowSchedulerCommand
        {
            JobId = jobId,
            ConditionActionId = 123
        };

        var entity = new Domain.Entities.CyberJobWorkflowScheduler
        {
            JobId = jobId,
            ReferenceId = "ref-001",
            ConditionActionId = 1234
        };

        _repositoryMock.Setup(r => r.GetCyberJobWorkflowSchedulerByJobId(jobId)).ReturnsAsync(entity);

       // _repositoryMock.Setup(r => r.UpdateAsync(entity)).Returns(Task.CompletedTask);

        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.CyberJobWorkflowScheduler>()))
               .ReturnsAsync((Domain.Entities.CyberJobWorkflowScheduler scheduler) => scheduler);

        _publisherMock.Setup(p => p.Publish(It.IsAny<INotification>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull( result.Id);
        Assert.NotNull(result.Message);


        _repositoryMock.Verify(r => r.UpdateAsync(entity), Times.Once);
        _publisherMock.Verify(p => p.Publish(It.Is<CyberJobWorkflowSchedulerConditionActionIdUpdatedEvent>(
            e => e.JobId == jobId), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "Handle_InvalidJobId_ShouldThrowNotFoundException")]
    public async Task Handle_InvalidJobId_ShouldThrowNotFoundException()
    {
        // Arrange
        var jobId = "invalid-job";
        var request = new CyberJobWorkflowSchedulerCommand
        {
            JobId = jobId,
            ConditionActionId = 12
        };

        _repositoryMock.Setup(r => r.GetCyberJobWorkflowSchedulerByJobId(jobId)).ReturnsAsync((Domain.Entities.CyberJobWorkflowScheduler?)null);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

    }
}
