﻿using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;
using ContinuityPatrol.Application.Features.ReportSchedule.Event.Update;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ReportSchedule.Commands;

public class UpdateReportScheduleTests
{
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
    private readonly Mock<ILoadBalancerRepository> _mockNodeConfigurationRepository;
    private readonly Mock<IJobScheduler> _mockClient;
    private readonly UpdateReportScheduleCommandHandler _handler;

    public UpdateReportScheduleTests()
    {
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();
        _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
        _mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();
        _mockClient = new Mock<IJobScheduler>();
        _handler = new UpdateReportScheduleCommandHandler(
            _mockMapper.Object,
            _mockPublisher.Object,
            _mockReportScheduleRepository.Object,
            _mockNodeConfigurationRepository.Object,
            _mockClient.Object);
    }

    [Fact]
    public async Task Handle_ShouldUpdateReportSchedule_WhenReportScheduleExists()
    {
        var command = new UpdateReportScheduleCommand
        {
            Id = "existing-guid",
            ReportName = "Updated Report",
            ScheduleTime = "2024-12-20T10:00:00",
        };

        var existingReportSchedule = new Domain.Entities.ReportSchedule
        {
            ReferenceId = command.Id,
            ReportName = "Original Report",
            IsActive = true
        };

        _mockReportScheduleRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingReportSchedule);

        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var destSchedule = dest as Domain.Entities.ReportSchedule;
                if (destSchedule != null)
                {
                    destSchedule.ReportName = command.ReportName;
                    destSchedule.ScheduleTime = command.ScheduleTime;
                }
            });

        _mockReportScheduleRepository
          .Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.ReportSchedule>()))
          .ReturnsAsync((Domain.Entities.ReportSchedule rs) => rs);


        _mockPublisher.Setup(p => p.Publish(It.IsAny<ReportScheduleUpdateEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockNodeConfigurationRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new Domain.Entities.LoadBalancer { ConnectionType = "http", IPAddress = "127.0.0.1", Port = 8080 });

        _mockClient.Setup(js => js.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
     

        _mockReportScheduleRepository.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.ReportSchedule>()), Times.Once);
        _mockPublisher.Verify(p => p.Publish(It.IsAny<ReportScheduleUpdateEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenReportScheduleDoesNotExist()
    {
        var command = new UpdateReportScheduleCommand
        {
            Id = "non-existing-guid",
            ReportName = "Updated Report",
            ScheduleTime = "2024-12-20T10:00:00",
        };

        _mockReportScheduleRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((Domain.Entities.ReportSchedule)null);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldNotScheduleJob_WhenNodeConfigurationIsNull()
    {
        var command = new UpdateReportScheduleCommand
        {
            Id = "existing-guid",
            ReportName = "Updated Report",
            ScheduleTime = "2024-12-20T10:00:00",
        };

        var existingReportSchedule = new Domain.Entities.ReportSchedule
        {
            ReferenceId = command.Id,
            ReportName = "Original Report",
            IsActive = true
        };

        _mockReportScheduleRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(existingReportSchedule);

        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
            {
                var destSchedule = dest as Domain.Entities.ReportSchedule;
                if (destSchedule != null)
                {
                    destSchedule.ReportName = command.ReportName;
                    destSchedule.ScheduleTime = command.ScheduleTime;
                }
            });
        _mockReportScheduleRepository.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.ReportSchedule>()))
.ReturnsAsync((Domain.Entities.ReportSchedule rs) => rs);


        _mockPublisher.Setup(p => p.Publish(It.IsAny<ReportScheduleUpdateEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockNodeConfigurationRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        _mockClient.Setup(js => js.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
    
        _mockClient.Verify(js => js.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
    }
    [Fact]
    public void Should_Assign_And_Return_All_Properties_Correctly()
    {
        // Arrange
        var command = new UpdateReportScheduleCommand
        {
            Id = "ID123",
            ReportName = "Updated Report",
            ReportType = "Excel",
            CompanyId = "COMP456",
            NodeId = "NODE789",
            NodeName = "Node-X",
            Type = "Drilldown",
            FromDate = "2025-02-01",
            ToDate = "2025-02-28",
            ScheduleTime = "2025-02-27T09:30:00",
            ReportProperties = "{\"pageSize\":\"A4\"}",
            UserProperties = "{\"recipients\":[\"user3\",\"user4\"]}"
        };

        // Assert property values
        command.Id.ShouldBe("ID123");
        command.ReportName.ShouldBe("Updated Report");
        command.ReportType.ShouldBe("Excel");
        command.CompanyId.ShouldBe("COMP456");
        command.NodeId.ShouldBe("NODE789");
        command.NodeName.ShouldBe("Node-X");
        command.Type.ShouldBe("Drilldown");
        command.FromDate.ShouldBe("2025-02-01");
        command.ToDate.ShouldBe("2025-02-28");
        command.ScheduleTime.ShouldBe("2025-02-27T09:30:00");
        command.ReportProperties.ShouldBe("{\"pageSize\":\"A4\"}");
        command.UserProperties.ShouldBe("{\"recipients\":[\"user3\",\"user4\"]}");

        // Assert ToString() output
        command.ToString().ShouldBe("ReportName: Updated Report; Id:ID123;");
    }
    [Fact]
    public void Should_Assign_And_Return_Id_And_BaseResponse_Properties()
    {
        // Arrange
        var response = new UpdateReportScheduleResponse
        {
            Id = "Schedule123",
            Message = "Updated Successfully",
        };

        // Assert
        response.Id.ShouldBe("Schedule123");
        response.Message.ShouldBe("Updated Successfully");
    }
}
