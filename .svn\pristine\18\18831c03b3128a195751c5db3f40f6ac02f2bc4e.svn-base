﻿using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Delete;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetList;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class LogViewerService : BaseService, ILogViewerService
{
    public LogViewerService(IHttpContextAccessor accessor) : base(accessor)
    {

    }
    public async Task<PaginatedResult<LogViewerListVm>> GetPaginatedLogViewerList(GetLogViewerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in LogViewer Paginated List");

        return await Mediator.Send(query);
    }
    public async Task<BaseResponse> CreateAsync(CreateLogViewerCommand createLogViewer)
    {
        Logger.LogDebug($"Create LogViewer '{createLogViewer.Name}'");

        return await Mediator.Send(createLogViewer);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateLogViewerCommand updateLogViewer)
    {
        Logger.LogDebug($"Update LogViewer '{updateLogViewer.Name}'");

        return await Mediator.Send(updateLogViewer);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "LogViewer Reference Id");

        Logger.LogDebug($"Delete LogViewer Details by Id '{id}'");

        return await Mediator.Send(new DeleteLogViewerCommand { Id = id });
    }
    public async Task<List<LogViewerListVm>> GetLogViewerList()
    {
        return await Mediator.Send(new GetLogViewerListQuery());
    }
    public async Task<GetLogViewerDetailVm> GetLogViewerDetail(string id)
    {
        return await Mediator.Send(new GetLogViewerDetailQuery { Id = id });
    }

    public async Task<bool> IsLogViewerNameUnique(string name, string? id)
    {
        return await Mediator.Send(new GetLogViewerNameUniqueQuery { Name = name, Id = id });
    }
}