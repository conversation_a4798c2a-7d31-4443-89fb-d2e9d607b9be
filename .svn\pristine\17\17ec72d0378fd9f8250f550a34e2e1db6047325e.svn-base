﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Delete;
using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class WorkflowServiceFixture : IDisposable
{
    public CreateWorkflowCommand CreateCommand { get; }
    public IFixture Fixture { get; }
    public UpdateWorkflowCommand UpdateCommand { get; }
    public DeleteWorkflowCommand DeleteCommand { get; }
    public GetWorkflowDetailQuery DetailQuery { get; }
    public GetWorkflowPaginatedListQuery PaginatedQuery { get; }
    public GetWorkflowNameUniqueQuery NameUniqueQuery { get; }

    public BaseResponse CreateResponse { get; }
    public BaseResponse UpdateResponse { get; }
    public BaseResponse DeleteResponse { get; }
    public WorkflowDetailVm DetailResponse { get; }
    public List<WorkflowListVm> WorkflowList { get; }
    public List<WorkflowNameVm> NameList { get; }
    public PaginatedResult<WorkflowListVm> PaginatedResponse { get; }

    public WorkflowServiceFixture()
    {
        var fixture = new Fixture();

        Fixture = new Fixture();

        CreateCommand = fixture.Create<CreateWorkflowCommand>();
        UpdateCommand = fixture.Create<UpdateWorkflowCommand>();
        DeleteCommand = fixture.Create<DeleteWorkflowCommand>();
        DetailQuery = fixture.Create<GetWorkflowDetailQuery>();
        PaginatedQuery = fixture.Create<GetWorkflowPaginatedListQuery>();
        NameUniqueQuery = fixture.Create<GetWorkflowNameUniqueQuery>();

        CreateResponse = new BaseResponse { Success = true, Message = "Success" };
        UpdateResponse = new BaseResponse { Success = true, Message = "Success" };
        DeleteResponse = new BaseResponse { Success = true, Message = "Success" };

        DetailResponse = new WorkflowDetailVm
        {
            Name = DetailQuery.Id // Simulate status equals workflowId
        };

        WorkflowList = fixture.Create<List<WorkflowListVm>>();
        NameList = fixture.Create<List<WorkflowNameVm>>();
        PaginatedResponse = fixture.Create<PaginatedResult<WorkflowListVm>>();
    }

    public void Dispose()
    {
        // cleanup
    }
}
