using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleRACMonitorLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "OracleRAC";
    public static string DatabaseId => "DB_123";

    public List<OracleRACMonitorLogs> OracleRACMonitorLogsPaginationList { get; set; }
    public List<OracleRACMonitorLogs> OracleRACMonitorLogsList { get; set; }
    public OracleRACMonitorLogs OracleRACMonitorLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OracleRACMonitorLogsFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<OracleRACMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        OracleRACMonitorLogsPaginationList = _fixture.CreateMany<OracleRACMonitorLogs>(20).ToList();
        OracleRACMonitorLogsList = _fixture.CreateMany<OracleRACMonitorLogs>(5).ToList();
        OracleRACMonitorLogsDto = _fixture.Create<OracleRACMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OracleRACMonitorLogs CreateOracleRACMonitorLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        string databaseId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<OracleRACMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
          
            .Create();
    }

    public OracleRACMonitorLogs CreateOracleRACMonitorLogsWithWhitespace()
    {
        return CreateOracleRACMonitorLogsWithProperties(type: "  OracleRAC  ");
    }

    public List<OracleRACMonitorLogs> CreateMultipleOracleRACMonitorLogsWithSameType(string type, int count)
    {
        var logs = new List<OracleRACMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateOracleRACMonitorLogsWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public OracleRACMonitorLogs CreateOracleRACMonitorLogsWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateOracleRACMonitorLogsWithProperties(infraObjectId: infraObjectId);
    }

    public List<OracleRACMonitorLogs> CreateOracleRACMonitorLogsWithDateRange(DateTime startDate, DateTime endDate, string infraObjectId = null)
    {
        var logs = new List<OracleRACMonitorLogs>();
        var currentDate = startDate;
        
        while (currentDate <= endDate)
        {
            logs.Add(CreateOracleRACMonitorLogsWithProperties(
                infraObjectId: infraObjectId ?? InfraObjectId,
                createdDate: currentDate,
                isActive: true));
            currentDate = currentDate.AddDays(1);
        }
        
        return logs;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
