﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;

public class RollBackBulkImportCommandHandler : IRequestHandler<RollBackBulkImportCommand, RollBackBulkImportResponse>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly BulkImportHelperService _bulkImportHelperService;
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;

    public RollBackBulkImportCommandHandler(IBulkImportOperationGroupRepository bulkImportOperationGroupRepository,
        IBulkImportActionResultRepository bulkImportActionResultRepository,
        BulkImportHelperService bulkImportHelperService)
    {
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
        _bulkImportHelperService = bulkImportHelperService;
    }

    public async Task<RollBackBulkImportResponse> Handle(RollBackBulkImportCommand request,
        CancellationToken cancellationToken)
    {
        var bulkImportOperationGroup = await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(request.GroupId);

        Guard.Against.NullOrDeactive(bulkImportOperationGroup, nameof(Domain.Entities.BulkImportOperationGroup),
            new NotFoundException(nameof(Domain.Entities.BulkImportOperationGroup), request.GroupId));

        try
        {
            var bulkImportActionResults =
                await _bulkImportActionResultRepository.GetByOperationIdAndOperationGroupId(
                    bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId);

            var workflow = bulkImportActionResults
                .Where(x => x.EntityId.IsNotNullOrWhiteSpace() &&
                            (x.EntityType.Equals("SOWorkflow", StringComparison.OrdinalIgnoreCase) ||
                             x.EntityType.Equals("SBWorkflow", StringComparison.OrdinalIgnoreCase)))
                .Select(x => new
                {
                    WorkflowId = x.EntityId,
                    WorkflowName = x.EntityName,
                    OperationId = x.BulkImportOperationId,
                    OperationGroupId = x.BulkImportOperationGroupId,
                    EntityType = x.EntityType
                })
                .ToList();


            var infraObject = bulkImportActionResults
                .Where(x => x.EntityId.IsNotNullOrWhiteSpace() && x.EntityType.ToLower().Equals("infraobject"))
                .Select(x => new
                {
                    InfraObjectId = x.EntityId, InfraObjectName = x.EntityName, OperationId = x.BulkImportOperationId,
                    OperationGroupId = x.BulkImportOperationGroupId
                })
                .ToList();

            var replication = bulkImportActionResults
                .Where(x => x.EntityId.IsNotNullOrWhiteSpace() && x.EntityType.ToLower().Equals("replication"))
                .Select(x => new
                {
                    ReplicationId = x.EntityId, ReplicationName = x.EntityName, OperationId = x.BulkImportOperationId,
                    OperationGroupId = x.BulkImportOperationGroupId
                })
                .ToList();

            var database = bulkImportActionResults
                .Where(x => x.EntityId.IsNotNullOrWhiteSpace() && x.EntityType.ToLower().Equals("database"))
                .Select(x => new
                {
                    DatabaseId = x.EntityId, DatabaseName = x.EntityName, OperationId = x.BulkImportOperationId,
                    OperationGroupId = x.BulkImportOperationGroupId
                })
                .ToList();

            var server = bulkImportActionResults
                .Where(x => x.EntityId.IsNotNullOrWhiteSpace() && x.EntityType.ToLower().Equals("server"))
                .Select(x => new
                {
                    ServerId = x.EntityId, ServerName = x.EntityName, OperationId = x.BulkImportOperationId,
                    OperationGroupId = x.BulkImportOperationGroupId
                })
                .ToList();


            await _bulkImportHelperService.DeleteWorkflow(workflow, cancellationToken);

            await _bulkImportHelperService.DeleteInfraObject(infraObject, cancellationToken);

            await _bulkImportHelperService.DeleteReplication(replication, cancellationToken);

            await _bulkImportHelperService.DeleteDatabase(database, cancellationToken);

            await _bulkImportHelperService.DeleteServer(server, cancellationToken);
        }
        catch (Exception ex)
        {
            return new RollBackBulkImportResponse
            {
                Success = false,
                Message = ex.GetMessage(),
                BulkImportOperationGroupId = bulkImportOperationGroup.ReferenceId
            };
        }

        return new RollBackBulkImportResponse
        {
            Message = "Rollback completed successfully.",
            BulkImportOperationGroupId = bulkImportOperationGroup.ReferenceId
        };
    }
}