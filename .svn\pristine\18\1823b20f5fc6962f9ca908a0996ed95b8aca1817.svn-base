﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Delete;
using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Delete;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Commands;

public class DeleteRoboCopyJobTests
{
    private readonly Mock<IRoboCopyJobRepository> _mockRoboCopyJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteRoboCopyJobCommandHandler _handler;

    public DeleteRoboCopyJobTests()
    {
        _mockRoboCopyJobRepository = new Mock<IRoboCopyJobRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();
        _handler = new DeleteRoboCopyJobCommandHandler(
            _mockRoboCopyJobRepository.Object,
            _mockMapper.Object,
            _mockPublisher.Object
        );
    }

    [Fact]
    public async Task Handle_ValidRequest_ReturnsSuccessResponse()
    {
        var command = new DeleteRoboCopyJobCommand { Id = Guid.NewGuid().ToString() };
        var roboCopyJob = new Domain.Entities.RoboCopyJob
        {
            Id = 1,
            ReplicationName = "TestReplication",
            IsActive = true
        };

        _mockRoboCopyJobRepository
            .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(roboCopyJob);
        _mockRoboCopyJobRepository
            .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()))
            .ReturnsAsync(roboCopyJob); // ✅


        _mockPublisher
            .Setup(pub => pub.Publish(It.IsAny<RoboCopyJobDeletedEvent>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var expectedMessage = $"{nameof(Domain.Entities.RoboCopyJob)} with reference {roboCopyJob.ReplicationName} has been deleted.";

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.NotNull(result);
        Assert.False(result.IsActive);

        _mockRoboCopyJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()), Times.Once);
        _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RoboCopyJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_InvalidGuid_ThrowsInvalidArgumentException()
    {
        var command = new DeleteRoboCopyJobCommand { Id = "invalid-guid" };

        await Assert.ThrowsAsync<InvalidArgumentException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_RoboCopyJobNotFound_ThrowsNotFoundException()
    {
        var command = new DeleteRoboCopyJobCommand { Id = Guid.NewGuid().ToString() };

        _mockRoboCopyJobRepository
            .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.RoboCopyJob)null);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

        _mockRoboCopyJobRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
    [Fact]
    public void DeleteRoboCopyJobCommand_Should_Assign_Id_Correctly()
    {
        // Arrange
        var expectedId = "job-123";

        // Act
        var command = new DeleteRoboCopyJobCommand
        {
            Id = expectedId
        };

        // Assert
        Assert.Equal(expectedId, command.Id);
    }
    [Fact]
    public void DeleteRoboCopyJobResponse_Should_Assign_Properties_Correctly()
    {
        // Arrange
        var response = new DeleteRoboCopyJobResponse
        {
            IsActive = false,
            Message = "RoboCopyJob with reference TestReplication has been deleted.",
        };

        // Assert
        Assert.False(response.IsActive);
        Assert.Equal("RoboCopyJob with reference TestReplication has been deleted.", response.Message);
    }
}
