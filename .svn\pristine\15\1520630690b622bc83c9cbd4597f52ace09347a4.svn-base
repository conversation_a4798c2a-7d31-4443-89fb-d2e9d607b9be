﻿using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUpLog.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class BackupDataControllerShould
    {
        private readonly Mock<ILogger<BackupDataController>> _mockLogger=new();
        private readonly Mock<IMapper> _mockMapper=new();
        private readonly Mock<IDataProvider> _mockDataProvider= new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private  BackupDataController _controller;
        private readonly Fixture _fixture;

        public BackupDataControllerShould()
        {
            _fixture = new Fixture();
            Initialize();
        }
        public void Initialize()
        {
            _controller = new BackupDataController(
                    _mockLogger.Object,
                    _mockPublisher.Object,
                    _mockMapper.Object,
                    _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task GetList_ReturnsJsonResult_WithBackupData()
        {           
            var backupData = new List<BackUpListVm>();
            _mockDataProvider.Setup(dp => dp.BackUp.GetBackUpList()).ReturnsAsync(backupData);
            
            var result = await _controller.GetList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(backupData, jsonResult.Value);
          
        }

        [Fact]
        public async Task GetBackupConfig_ReturnsJsonResult_WithBackupConfig()
        {           
            var backupConfig = new GetByConfigDetailVm();
            _mockDataProvider.Setup(dp => dp.BackUp.GetBackUpByConfig()).ReturnsAsync(backupConfig);
            
            var result = await _controller.GetBackupConfig();

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(backupConfig, jsonResult.Value);
           
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_WithPaginatedData()
        {       
            var query = new GetBackUpLogPaginatedListQuery();
            var paginatedData = new PaginatedResult<BackUpLogListVm>();
            _mockDataProvider.Setup(dp => dp.BackUpLog.GetPaginatedBackUpLogs(query)).ReturnsAsync(paginatedData);

            var result = await _controller.GetPagination(query);

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedData, jsonResult.Value);
        }
		[Fact]
		public async Task ExecuteBackUpCommand_ReturnsJsonResult_WithSuccess()
        {
            var model = new BackUpViewModel();
            var command = new BackUpExecuteCommand();
            var response = new BaseResponse { Success = true, Message = "Success" };

            _mockMapper.Setup(m => m.Map<BackUpExecuteCommand>(model)).Returns(command);
            _mockDataProvider.Setup(dp => dp.BackUp.ExecuteBackUp(command)).ReturnsAsync(response);

            var result = await _controller.ExecuteBackUpCommand(model);

            var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			Assert.NotNull(result);
		}


        [Fact]

        public async Task CreateOrUpdate_WhenCreatingNewBackUp_ReturnsRedirectToAction()
        {
            
            var viewModel = new Fixture().Create<BackUpViewModel>();

            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
    {
        { "id", "22" }
    });

            
            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = JsonConvert.SerializeObject(new { Id = "password" }) };
            var createCommand = new CreateBackUpCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreateBackUpCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.CreateAsync(createCommand)).ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(model);

            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WhenUpdatingBackUp_ReturnsRedirectToAction()
        {
            var viewModel = _fixture.Create<BackUpViewModel>();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
    {
        { "id", "22" }
    });

            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = JsonConvert.SerializeObject(new { ftphostPassword = "password" }) };
            var updateCommand = new UpdateBackUpCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockMapper.Setup(m => m.Map<UpdateBackUpCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.UpdateAsync(updateCommand)).ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(model);

            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task List_ReturnsViewResult()
        {
            // Act
            var result = await _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_ThrowsException_ReturnsView()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<BackUpLogPaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetList_ThrowsException_ReturnsEmptyJson()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BackUp.GetBackUpList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetBackupConfig_ThrowsException_ReturnsEmptyJson()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.BackUp.GetBackUpByConfig())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetBackupConfig();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_ThrowsException_ReturnsEmptyJson()
        {
            // Arrange
            var query = new GetBackUpLogPaginatedListQuery();
            _mockDataProvider.Setup(dp => dp.BackUpLog.GetPaginatedBackUpLogs(query))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task ExecuteBackUpCommand_ValidationException_ReturnsJsonWithError()
        {
            // Arrange
            var model = new BackUpViewModel();
            var command = new BackUpExecuteCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<BackUpExecuteCommand>(model)).Returns(command);
            _mockDataProvider.Setup(dp => dp.BackUp.ExecuteBackUp(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.ExecuteBackUpCommand(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task ExecuteBackUpCommand_GeneralException_ReturnsJsonWithError()
        {
            // Arrange
            var model = new BackUpViewModel();
            var command = new BackUpExecuteCommand();

            _mockMapper.Setup(m => m.Map<BackUpExecuteCommand>(model)).Returns(command);
            _mockDataProvider.Setup(dp => dp.BackUp.ExecuteBackUp(command)).ThrowsAsync(new Exception("General exception"));

            // Act
            var result = await _controller.ExecuteBackUpCommand(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CheckWindowsService_ReturnsJsonResult()
        {
            // Arrange
            var serviceType = "TestService";
            var serviceStatus = new GetWorkflowServiceResponse
            {
                Success = true,
                Message = "Service is running",
                ActiveNodes = new List<string> { "Node1", "Node2" },
                InActiveNodes = new List<string>()
            };
            _mockDataProvider.Setup(dp => dp.WorkflowOperationGroup.CheckWindowsServiceConnection(serviceType))
                             .ReturnsAsync(serviceStatus);

            // Act
            var result = await _controller.CheckWindowsService(serviceType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serviceStatus, jsonResult.Value);
        }

        [Fact]
        public async Task CheckWindowsService_ThrowsException_ReturnsJsonWithError()
        {
            // Arrange
            var serviceType = "TestService";
            _mockDataProvider.Setup(dp => dp.WorkflowOperationGroup.CheckWindowsServiceConnection(serviceType))
                             .ThrowsAsync(new Exception("Service error"));

            // Act
            var result = await _controller.CheckWindowsService(serviceType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatePath_ReturnsRedirectToAction()
        {
            // Arrange
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = JsonConvert.SerializeObject(new { ftphostPassword = "password" }) };
            var createCommand = new CreateBackUpCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreateBackUpCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_PropertiesNull_ReturnsRedirectToAction()
        {
            // Arrange
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = null }; // Null properties
            var createCommand = new CreateBackUpCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreateBackUpCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidationException_ReturnsRedirectToListWithWarning()
        {
            // Arrange
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = JsonConvert.SerializeObject(new { ftphostPassword = "password" }) };
            var createCommand = new CreateBackUpCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateBackUpCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.CreateAsync(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_GeneralException_ReturnsRedirectToListWithWarning()
        {
            // Arrange
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" } // Empty id triggers create path
            });

            var mockHttpRequest = new Mock<HttpRequest>();
            mockHttpRequest.Setup(r => r.Form).Returns(formCollection);

            var mockHttpContext = new Mock<HttpContext>();
            mockHttpContext.Setup(c => c.Request).Returns(mockHttpRequest.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext.Object
            };

            var model = new BackUpViewModel { Properties = JsonConvert.SerializeObject(new { ftphostPassword = "password" }) };
            var createCommand = new CreateBackUpCommand();

            _mockMapper.Setup(m => m.Map<CreateBackUpCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BackUp.CreateAsync(createCommand)).ThrowsAsync(new Exception("General exception"));

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public void Controller_HasAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(BackupDataController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void GetPagination_HasHttpGetAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = methodInfo.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void ExecuteBackUpCommand_HasHttpPostAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("ExecuteBackUpCommand");

            // Act
            var httpPostAttribute = methodInfo.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void ExecuteBackUpCommand_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("ExecuteBackUpCommand");

            // Act
            var antiForgeryAttribute = methodInfo.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiForgeryAttribute);
        }

        [Fact]
        public void ExecuteBackUpCommand_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("ExecuteBackUpCommand");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CheckWindowsService_HasHttpPostAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CheckWindowsService");

            // Act
            var httpPostAttribute = methodInfo.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void CheckWindowsService_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CheckWindowsService");

            // Act
            var antiForgeryAttribute = methodInfo.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiForgeryAttribute);
        }

        [Fact]
        public void CheckWindowsService_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CheckWindowsService");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasHttpPostAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = methodInfo.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CreateOrUpdate");

            // Act
            var antiForgeryAttribute = methodInfo.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiForgeryAttribute);
        }

        [Fact]
        public void CreateOrUpdate_HasAntiXssAttribute()
        {
            // Arrange
            var methodInfo = typeof(BackupDataController).GetMethod("CreateOrUpdate");

            // Act
            var antiXssAttribute = methodInfo.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }
    }
}
