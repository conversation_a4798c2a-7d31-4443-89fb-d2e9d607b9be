﻿namespace ContinuityPatrol.Application.Features.Report.Queries.DriftReport;

public class DriftReportVm
{
    public string ReportGeneratedBy { get; set; }
    public string ReportGeneratedTime { get; set; }
    public List<DriftEventReportVm> DriftEventReportVm { get; set; } = new();
    public List<DriftResourceVm> DriftResourceSummaryVms { get; set; }
}

public class DriftEventReportVm 
{
    public string Id { get; set; }
    public string InfraObjectId { get; set; }
    public string OperationalServiceName { get; set; }
    public string InfraObjectName { get; set; }
    public string EntityType { get; set; }
    public string Entity { get; set; }
    public string EntityName { get; set; }
    public string EntityStatus { get; set; }
    public bool IsConflict { get; set; }
    public string Message { get; set; }
    public bool IsActive { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public string LastModifiedBy { get; set; }
    public DateTime LastModifiedDate { get; set; }
}

public class DriftResourceVm
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public bool IsConflict { get; set; }
}