﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class DynamicSubDashboardFixture
{
    public List<DynamicSubDashboardListVm> ListVm { get; }
    public PaginatedResult<DynamicSubDashboardListVm> PaginatedListVm { get; }
    public DynamicSubDashboardDetailVm DetailVm { get; }
    public CreateDynamicSubDashboardCommand CreateCommand { get; }
    public UpdateDynamicSubDashboardCommand UpdateCommand { get; }
    public GetDynamicSubDashboardPaginatedListQuery PaginatedQuery { get; }
    public BaseResponse BaseResponse { get; }
    public string Id { get; }
    public string Name { get; }

    public DynamicSubDashboardFixture()
    {
        var fixture = new Fixture();
        ListVm = fixture.Create<List<DynamicSubDashboardListVm>>();
        PaginatedListVm = fixture.Create<PaginatedResult<DynamicSubDashboardListVm>>();
        DetailVm = fixture.Create<DynamicSubDashboardDetailVm>();
        CreateCommand = fixture.Create<CreateDynamicSubDashboardCommand>();
        UpdateCommand = fixture.Create<UpdateDynamicSubDashboardCommand>();
        PaginatedQuery = fixture.Create<GetDynamicSubDashboardPaginatedListQuery>();
        BaseResponse = fixture.Create<BaseResponse>();
        Id = fixture.Create<string>();
        Name = fixture.Create<string>();
    }
}