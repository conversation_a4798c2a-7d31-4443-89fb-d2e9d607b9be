using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;
using ContinuityPatrol.Services.Db.Impl.Alert;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Alert;

public class AlertNotificationServiceTests : BaseServiceTestSetup<AlertNotificationService>, IClassFixture<AlertNotificationFixture>
{
    private readonly AlertNotificationFixture _fixture;

    public AlertNotificationServiceTests(AlertNotificationFixture fixture)
    {
        InitializeService(accessor => new AlertNotificationService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetAlertNotificationsList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetAlertNotificationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.AlertNotificationListVm);

        var result = await ServiceUnderTest.GetAlertNotificationsList();

        Assert.Equal(_fixture.AlertNotificationListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetAlertNotificationById_Should_Return_Detail()
    {
        var alertNotificationId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetAlertNotificationDetailQuery>(q => q.Id == alertNotificationId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.AlertNotificationDetailVm);

        var result = await ServiceUnderTest.GetAlertNotificationById(alertNotificationId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.AlertNotificationDetailVm.Id, result.Id);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();
        var alertCode = "CRIT_001";

        MediatorMock.Setup(m => m.Send(It.Is<GetAlertNotificationDetailByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId && q.AlertCode == alertCode), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.AlertNotificationDetailByInfraObjectIdVm);

        var result = await ServiceUnderTest.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        Assert.NotNull(result);
        Assert.Equal(_fixture.AlertNotificationDetailByInfraObjectIdVm.Count, result.Count);
    }

    [Fact]
    public async Task CreateAlertNotification_Should_Return_Success()
    {
        var response = new CreateAlertNotificationResponse
        {
            Message = "Created",
            Success = true,
            AlertNotificationId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateAlertNotificationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAlertNotification(_fixture.CreateAlertNotificationCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAlertNotification_Should_Return_Success()
    {
        var response = new UpdateAlertNotificationResponse
        {
            Message = "Updated",
            Success = true,
            AlertNotificationId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateAlertNotificationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAlertNotification(_fixture.UpdateAlertNotificationCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAlertNotification_Should_Return_Success()
    {
        var alertNotificationId = Guid.NewGuid().ToString();
        var response = new DeleteAlertNotificationResponse
        {
            Message = "Deleted",
            Success = true,
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteAlertNotificationCommand>(c => c.Id == alertNotificationId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAlertNotification(alertNotificationId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetPaginatedAlertNotifications_Should_Return_Result()
    {
        var query = _fixture.GetAlertNotificationPaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedAlertNotificationListVm);

        var result = await ServiceUnderTest.GetPaginatedAlertNotifications(query);

        Assert.Equal(_fixture.AlertNotificationListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetAlertNotificationById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetAlertNotificationById(null!));
    }

    [Fact]
    public async Task GetAlertNotificationById_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetAlertNotificationById(""));
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_NullInfraObjectId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetAlertNotificationByInfraObjectIdAndAlertCode(null!, "ALERT_001"));
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_EmptyInfraObjectId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetAlertNotificationByInfraObjectIdAndAlertCode("", "ALERT_001"));
    }

    [Fact]
    public async Task DeleteAlertNotification_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAlertNotification(null!));
    }

    [Fact]
    public async Task DeleteAlertNotification_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAlertNotification(""));
    }
}