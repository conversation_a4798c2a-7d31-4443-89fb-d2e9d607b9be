{"Version": 1, "WorkspaceRootPath": "D:\\Testcase\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\analyticscontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\analyticscontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\customdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\customdashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\customdashboardcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\customdashboardcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\businessviewnewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\dashboard\\controllers\\businessviewnewcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\analyticscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\analyticscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\dashboard\\controllers\\itresiliencyviewcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\dashboard\\controllers\\itresiliencyviewcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "BusinessViewNewControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\BusinessViewNewControllerShould.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:29:18.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AnalyticsController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\AnalyticsController.cs", "ViewState": "AgIAAG4AAAAAAAAAAAAuwHsAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:26:37.553Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ItResiliencyViewController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\ItResiliencyViewController.cs", "ViewState": "AgIAAAUAAAAAAAAAAIAwwBkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:24:13.635Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "AnalyticsControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\AnalyticsControllerShould.cs", "ViewState": "AgIAANYAAAAAAAAAAAAUwPEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:23:52.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CustomDashboardControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Dashboard\\Controllers\\CustomDashboardControllerShould.cs", "ViewState": "AgIAADQAAAAAAAAAAAAUwEEAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:19:34.505Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CustomDashboardController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Dashboard\\Controllers\\CustomDashboardController.cs", "ViewState": "AgIAABAAAAAAAAAAAAAgwBYAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T13:18:33.272Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}]}]}