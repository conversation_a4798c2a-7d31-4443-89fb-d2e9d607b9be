﻿using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicDashboardMap.Queries.GetDetail;

public class GetDynamicDashboardMapDetailsQueryHandlerTests
{
    private readonly Mock<IDynamicDashboardMapRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetDynamicDashboardMapDetailsQueryHandler _handler;

    public GetDynamicDashboardMapDetailsQueryHandlerTests()
    {
        var dashboard = new Domain.Entities.DynamicDashboardMap
        {
            ReferenceId = "map-001",
            UserName = "Main Dashboard",
            IsActive = true
        };

        _repositoryMock = new Mock<IDynamicDashboardMapRepository>();
        _repositoryMock = DynamicDashboardMapRepositoryMocks.GetDynamicDashboardMapRepository();
        _repositoryMock.Setup(repo => repo.GetByReferenceIdAsync("map-001"))
                       .ReturnsAsync(dashboard);

        _mapperMock = new Mock<IMapper>();

        _handler = new GetDynamicDashboardMapDetailsQueryHandler(_mapperMock.Object, _repositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnDetailVm_WhenDashboardExistsAndIsActive()
    {
        // Arrange
        var expectedVm = new DynamicDashboardMapDetailVm
        {
            Id = 123,
            UserName = "Main Dashboard"
        };

        _mapperMock.Setup(m => m.Map<DynamicDashboardMapDetailVm>(It.IsAny<Domain.Entities.DynamicDashboardMap>()))
                   .Returns(expectedVm);

        var query = new GetDynamicDashboardMapDetailQuery { Id = "map-001" };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(123);
        result.UserName.Should().Be("Main Dashboard");
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenDashboardNotFound()
    {
        // Arrange
        var repo = new Mock<IDynamicDashboardMapRepository>();
        repo.Setup(r => r.GetByReferenceIdAsync("invalid-id")).ReturnsAsync((Domain.Entities.DynamicDashboardMap)null!);

        var handler = new GetDynamicDashboardMapDetailsQueryHandler(_mapperMock.Object, repo.Object);
        var query = new GetDynamicDashboardMapDetailQuery { Id = "invalid-id" };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenDashboardIsInactive()
    {
        // Arrange
        var inactiveDashboard = new Domain.Entities.DynamicDashboardMap
        {
            ReferenceId = "map-002",
            UserName = "Inactive Dashboard",
            IsActive = false
        };

        var repo = new Mock<IDynamicDashboardMapRepository>();
        repo.Setup(r => r.GetByReferenceIdAsync("map-002")).ReturnsAsync(inactiveDashboard);

        var handler = new GetDynamicDashboardMapDetailsQueryHandler(_mapperMock.Object, repo.Object);
        var query = new GetDynamicDashboardMapDetailQuery { Id = "map-002" };

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(query, CancellationToken.None));
    }
}
