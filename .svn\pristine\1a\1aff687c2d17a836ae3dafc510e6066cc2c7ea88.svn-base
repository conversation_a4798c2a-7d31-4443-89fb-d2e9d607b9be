﻿using ContinuityPatrol.Application.Features.Node.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Node.Queries;

public class GetNodeDetailQueryHandlerTests : IClassFixture<NodeFixture>
{
    private readonly NodeFixture _nodeFixture;

    private readonly Mock<INodeRepository> _mockNodeRepository;

    private readonly GetNodeDetailQueryHandler _handler;

    public GetNodeDetailQueryHandlerTests(NodeFixture nodeFixture)
    {
        _nodeFixture = nodeFixture;

        _mockNodeRepository = NodeRepositoryMocks.GetNodeRepository(_nodeFixture.Nodes);

        _handler = new GetNodeDetailQueryHandler(_nodeFixture.Mapper, _mockNodeRepository.Object);

        _nodeFixture.Nodes[0].Properties = "TykoBV6TVSMKf39azFrifN+fmv7YR0sdxrUy+vcZRbE=$xI8Poun9ldLNMFv+fefQohTbMqwdD4Ogd1aCSxUIjkxPB+SNww==";
    }

    [Fact]
    public async Task Handle_Return_NodeDetail_when_valid()
    {
        _nodeFixture.Nodes[0].Id = 1;
        _nodeFixture.Nodes[0].IsActive = true;

        var result = await _handler.Handle(new GetNodeDetailQuery { Id = _nodeFixture.Nodes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<NodeDetailVm>();

        result.Id.ShouldBe(_nodeFixture.Nodes[0].ReferenceId);
        result.Name.ShouldBe(_nodeFixture.Nodes[0].Name);
        result.ServerId.ShouldBe(_nodeFixture.Nodes[0].ServerId);
        result.ServerName.ShouldBe(_nodeFixture.Nodes[0].ServerName);
        result.Type.ShouldBe(_nodeFixture.Nodes[0].Type);
        result.Properties.ShouldBe(_nodeFixture.Nodes[0].Properties);

    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OneTime()
    {
        _nodeFixture.Nodes[0].Id = 1;
        _nodeFixture.Nodes[0].IsActive = true;

        await _handler.Handle(new GetNodeDetailQuery { Id = _nodeFixture.Nodes[0].ReferenceId }, CancellationToken.None);

        _mockNodeRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
    [Fact]
    public void NodeDetailVm_Should_Assign_And_Return_Correct_Values()
    {
        // Arrange
        var nodeDetail = new NodeDetailVm
        {
            Id = "node-123",
            Name = "Test Node",
            ServerId = "server-456",
            ServerName = "Main Server",
            TypeId = "type-789",
            Type = "Primary",
            Properties = "{\"cpu\": \"4\", \"ram\": \"16GB\"}",
            FormVersion = "v1.0"
        };

        // Assert
        nodeDetail.Id.ShouldBe("node-123");
        nodeDetail.Name.ShouldBe("Test Node");
        nodeDetail.ServerId.ShouldBe("server-456");
        nodeDetail.ServerName.ShouldBe("Main Server");
        nodeDetail.TypeId.ShouldBe("type-789");
        nodeDetail.Type.ShouldBe("Primary");
        nodeDetail.Properties.ShouldBe("{\"cpu\": \"4\", \"ram\": \"16GB\"}");
        nodeDetail.FormVersion.ShouldBe("v1.0");
    }

}