﻿using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Create;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Update;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]

public class FIACostController : Controller
{

    private readonly ILogger<CompanyController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public FIACostController(IMapper mapper, ILogger<CompanyController> logger,IDataProvider dataProvider)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [AntiXss]
    [EventCode(EventCodes.FiaCost.List)]
    public IActionResult List()
    {
        return View();
    }

    [HttpGet]
    [EventCode(EventCodes.FiaCost.GetPagination)]
    public async Task<JsonResult> GetPagination(GetFiaCostPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in FIA Cost");

        try
        {
            var paginationList = await _dataProvider.FiaCost.GetPaginatedFiaCosts(query);
            _logger.LogDebug("Successfully retrieved pagination list for FIA Cost");
            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.FiaCost.GetFiaTemplateList)]
    public async Task<JsonResult> GetFiaTemplateList()
    {
        _logger.LogDebug("Entering IsCompanyNameExist method in Company");
        try
        {
            _logger.LogDebug("Returning result for IsCompanyNameExist on company");

            var getTemplateList = await _dataProvider.FiaTemplate.GetFiaTemplateList();

            return Json(new { Success = true, data = getTemplateList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if company name exists for : .", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.FiaCost.CreateOrUpdate)]
    public async Task<JsonResult> CreateOrUpdate(FiaCostViewModel fiaCostModal)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Company");
        try
        {

            var fiaCostId = Request.Form["Id"].ToString();

            //  _logger.LogDebug($"Company Logo '{company.CompanyLogo}' is set in Company");

            if (fiaCostId.IsNullOrWhiteSpace())
            {


                var fiaCommand = _mapper.Map<CreateFiaCostCommand>(fiaCostModal);

                //_logger.LogDebug($"Creating Company '{company.Name}'");

                var result = await _dataProvider.FiaCost.CreateAsync(fiaCommand);

                return Json(new { Success = true, data = result });

            }
            else
            {


                var fiaCommand = _mapper.Map<UpdateFiaCostCommand>(fiaCostModal);

                //  _logger.LogDebug($"Updating Company '{company.Name}'");

                var result = await _dataProvider.FiaCost.UpdateAsync(fiaCommand);

                return Json(new { Success = true, data = result });

            }
            // _logger.LogDebug("CreateOrUpdate operation completed successfully in FIA Cost, returning view.");


        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if company name exists for : .", ex);

            return ex.GetJsonException();
        }
    }

}

