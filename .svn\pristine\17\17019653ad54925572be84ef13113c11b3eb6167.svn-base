using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class IncidentManagementFixture : IDisposable
{
    public List<IncidentManagementListVm> IncidentManagementListVm { get; }
    public IncidentManagementDetailVm IncidentManagementDetailVm { get; }
    public CreateIncidentManagementCommand CreateIncidentManagementCommand { get; }
    public UpdateIncidentManagementCommand UpdateIncidentManagementCommand { get; }

    public IncidentManagementFixture()
    {
        var fixture = new Fixture();

        IncidentManagementListVm = fixture.Create<List<IncidentManagementListVm>>();
        IncidentManagementDetailVm = fixture.Create<IncidentManagementDetailVm>();
        CreateIncidentManagementCommand = fixture.Create<CreateIncidentManagementCommand>();
        UpdateIncidentManagementCommand = fixture.Create<UpdateIncidentManagementCommand>();
    }

    public void Dispose()
    {

    }
}
