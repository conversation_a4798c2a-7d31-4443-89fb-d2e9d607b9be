﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Services.Db.Impl.Report;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Report;

public class ReportServiceTests : BaseServiceTestSetup<ReportService>, IClassFixture<ReportServiceFixture>
{
    private readonly ReportServiceFixture _fixture;

    public ReportServiceTests(ReportServiceFixture fixture)
    {
        InitializeService(accessor => new ReportService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetRtoReportByWorkflowOperationId_Should_Return_Data()
    {
        // Arrange
        var expectedId = Guid.NewGuid().ToString();

        _fixture.RtoQuery.Id = expectedId;

        var expectedResponse = new RTOReports
        {
            //WorkflowOperationId = expectedId,
            Date = DateTime.UtcNow.ToString("yyyy-MM-dd")
        };

        _fixture.RtoResponse = expectedResponse;

        MediatorMock.Setup(m => m.Send(
            It.Is<GetRtoReportQuery>(x => x.Id == expectedId),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await ServiceUnderTest.GetRtoReportByWorkflowOperationId(expectedId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResponse.Date, result.Date);
        Assert.Equal(expectedResponse.Date, result.Date);
    }

    [Fact]
    public async Task GetLicenseReportById_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(_fixture.LicenseQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.LicenseResponse);

        var result = await ServiceUnderTest.GetLicenseReportById(_fixture.LicenseQuery.LicenseId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.LicenseQuery.LicenseId, result.Date);
    }

    [Fact]
    public async Task GetRunbookReportByWorkflowId_Should_Return_Data()
    {
        // Arrange
        var expectedWorkflowId = Guid.NewGuid().ToString();

        var expectedQuery = new GetRunBookReportQuery
        {
            WorkflowId = expectedWorkflowId
        };

        var expectedResponse = new GetRunBookReportVm
        {
            WorkflowId = expectedWorkflowId
            // Add more properties if needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetRunBookReportQuery>(q => q.WorkflowId == expectedWorkflowId),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await ServiceUnderTest.GetRunbookReportByWorkflowId(expectedWorkflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedWorkflowId, result.WorkflowId);
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_Should_Return_Data()
    {
        // Arrange
        var cyberSnapTagName = "TestTag";
        var startDate = "2025-07-01";
        var endDate = "2025-07-22";

        var expectedResponse = new GetCyberSnapsReportVm
        {
            ReportGeneratedTime = cyberSnapTagName // or another expected property
                                                   // Add more fields if needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetCyberSnapsReportQuery>(q =>
                q.CyberSnapTagName == cyberSnapTagName &&
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await ServiceUnderTest.GetCyberSnapsBySnapTagName(cyberSnapTagName, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResponse.ReportGeneratedTime, result.ReportGeneratedTime);
    }

    [Fact]
    public async Task GetInfraObjectSchedulerLogList_Should_Return_Data()
    {
        // Arrange
        var startDate = "2025-07-01";
        var endDate = "2025-07-22";

        var expectedResponse = new GetResiliencyReadinessSchedulerLogReportVm
        {
            ReportGeneratedBy = "Tester",
            ReportGeneratedTime = DateTime.UtcNow.ToString()
            // Add other properties if needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetInfraObjectSchedulerLogsReportQuery>(q =>
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await ServiceUnderTest.GetInfraObjectSchedulerLogList(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResponse.ReportGeneratedBy, result.ReportGeneratedBy);
    }

    [Fact]
    public async Task GetRpoSlaReportByInfraObjectId_Should_ReturnExpectedResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var type = "TypeA";
        var startDate = "2025-07-01";
        var endDate = "2025-07-10";
        var dateOption = "Last7Days";

        var expectedResult = new { Success = true };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetRPOSLAReportQuery>(q =>
                q.InfraObjectId == infraObjectId &&
                q.Type == type &&
                q.ReportStartDate == startDate &&
                q.ReportEndDate == endDate &&
                q.DateOption == dateOption),
            It.IsAny<CancellationToken>()))
        .ReturnsAsync(expectedResult);

        // Act
        var result = await ServiceUnderTest.GetRpoSlaReportByInfraObjectId(infraObjectId, type, startDate, endDate, dateOption);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResult, result);
        MediatorMock.Verify(m => m.Send(It.IsAny<GetRPOSLAReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDrReadyStatusReportByBusinessServiceId_ShouldReturnExpectedResult()
    {
        // Arrange
        var businessServiceId = "sample-service-id";
        var expectedReport = new DrReadyStatusReport { ReportGeneratedBy = "Ready" };

        MediatorMock.Setup(m => m.Send(
                It.Is<DRReadyStatusForDRReadyReportQuery>(q => q.BusinessServiceId == businessServiceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetDrReadyStatusReportByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        MediatorMock.Verify(m => m.Send(It.IsAny<DRReadyStatusForDRReadyReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDrReadyExecutionLogReport_ShouldReturnExpectedResult()
    {
        // Arrange
        var startTime = "2025-07-01";
        var endTime = "2025-07-24";
        var businessServiceId = "test-business-service-id";

        var expectedReport = new DRReadyExecutionReport
        {
            // Add properties based on your class structure
            ReportGeneratedBy = "Test"
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetDRReadyExecutionReportQuery>(q =>
                    q.StartTime == startTime &&
                    q.EndTime == endTime &&
                    q.BusinessServiceId == businessServiceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetDrReadyExecutionLogReport(startTime, endTime, businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        MediatorMock.Verify(m => m.Send(It.IsAny<GetDRReadyExecutionReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetBusinessServiceSummaryReport_ShouldReturnExpectedReport()
    {
        // Arrange
        var expectedReport = new BusinessServiceSummaryReport
        {
            // Add sample properties
            ReportGeneratedBy = "Test",
            Date = "24/07/2025"
        };

        MediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceSummaryReportQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetBusinessServiceSummaryReport();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(It.IsAny<GetBusinessServiceSummaryReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetInfraObjectSummaryReport_ShouldReturnExpectedReport()
    {
        // Arrange
        var businessServiceId = "abc-123";
        var expectedReport = new InfraObjectSummaryReport
        {
            ReportGeneratedBy = "Test",
            Date = "24/07/2025"
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetInfraObjectSummaryReportQuery>(q => q.BusinessServiceId == businessServiceId),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetInfraObjectSummaryReport(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(It.IsAny<GetInfraObjectSummaryReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId_ShouldReturnExpectedResult()
    {
        // Arrange
        string businessServiceId = "b123";
        string infraObjectId = "i456";
        string createdDate = "2025-07-01";
        string lastModifiedDate = "2025-07-15";

        var expectedVm = new GetRpoSlaDeviationReportVm
        {
            ReportGeneratedBy = "Test",
        };

        MediatorMock.Setup(m => m.Send(
                It.Is<GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery>(q =>
                    q.BusinessServiceId == businessServiceId &&
                    q.InfraObjectId == infraObjectId &&
                    q.CreatedDate == createdDate &&
                    q.LastModifiedDate == lastModifiedDate),
        It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await ServiceUnderTest.GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(
            businessServiceId, infraObjectId, createdDate, lastModifiedDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedVm.ReportGeneratedBy, result.ReportGeneratedBy);

        MediatorMock.Verify(m => m.Send(It.IsAny<GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetInfraObjectConfigurationReport_ShouldReturnExpectedResult()
    {
        // Arrange
        string infraObjectId = "infra123";

        var expectedReport = new InfraReport
        {
            ReportGeneratedBy = "Admin",
            Date = "2025-07-24",
            // Add other properties of InfraReport if needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetInfraObjectConfigurationReportQuery>(q => q.InfraObjectId == infraObjectId),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetInfraObjectConfigurationReport(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetInfraObjectConfigurationReportQuery>(q => q.InfraObjectId == infraObjectId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetUserActivityReport_ShouldReturnExpectedResult()
    {
        // Arrange
        var userId = "user123";
        var createDate = "2025-07-01";
        var lastModifiedDate = "2025-07-24";

        var expectedReport = new UserActivityReport
        {
            UserName = "user123",
            Date = "2025-07-15",
            // Add more properties if needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<UserActivityReportQuery>(q =>
                q.UserId == userId &&
                q.CreatedDate == createDate &&
                q.LastModifiedDate == lastModifiedDate),
            It.IsAny<CancellationToken>()
        )).ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetUserActivityReport(userId, createDate, lastModifiedDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.UserName, result.UserName);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(
            It.Is<UserActivityReportQuery>(q =>
                q.UserId == userId &&
                q.CreatedDate == createDate &&
                q.LastModifiedDate == lastModifiedDate),
            It.IsAny<CancellationToken>()
        ), Times.Once);
    }

    [Fact]
    public async Task GetDrDrillReportByWorkflowOperationId_ShouldReturnExpectedResult()
    {
        // Arrange
        var workflowOperationId = Guid.NewGuid().ToString();
        var runMode = "Auto";
        var isCustom = true;

        var expectedReport = new DrDrillReport
        {
            ReportGeneratedBy = "SampleWorkflow",
            Date = "Success"
            // Add more properties as needed
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetWorkflowOperationDrDrillReportQuery>(q =>
                q.Id == workflowOperationId &&
                q.RunMode == runMode &&
                q.IsCustom == isCustom),
            It.IsAny<CancellationToken>())
        ).ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetDrDrillReportByWorkflowOperationId(workflowOperationId, runMode, isCustom);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetWorkflowOperationDrDrillReportQuery>(q =>
                q.Id == workflowOperationId &&
                q.RunMode == runMode &&
                q.IsCustom == isCustom),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetLicenseUtilizationReportByBusinessServiceId_ShouldReturnExpectedReport()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();

        var expectedReport = new LicenseReportByBusinessServiceReport
        {
            ReportGeneratedBy = "TestService",
            Date = "24/07/2025"
            // Add other relevant mock data here
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<GetLicenseReportByBusinessServiceQuery>(q =>
                q.BusinessServiceId == businessServiceId),
            It.IsAny<CancellationToken>())
        ).ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetLicenseUtilizationReportByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetLicenseReportByBusinessServiceQuery>(q =>
                q.BusinessServiceId == businessServiceId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAirGapReport_ShouldReturnExpectedReport()
    {
        // Arrange
        var startDate = "2025-07-01";
        var endDate = "2025-07-10";
        var airGapId = Guid.NewGuid().ToString();

        var expectedReport = new AirGapLogReportVm
        {
            ReportGenerated = "Test Report"
            // Add any other properties of AirGapLogReportVm as needed
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetAirGapQuery>(q =>
                    q.Id == airGapId &&
                    q.StartDate == startDate &&
                    q.EndDate == endDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetAirGapReport(startDate, endDate, airGapId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGenerated, result.ReportGenerated);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetAirGapQuery>(q =>
                q.Id == airGapId &&
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetBulkImportReport_ShouldReturnExpectedReport()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();

        var expectedReport = new GetBulkImportReportVm
        {
            ReportGeneratedBy = "Completed",
            Date = "24/07/2025"
            // Add more properties if needed
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetBulkImportReportListQuery>(q => q.Id == operationId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetBulkImportReport(operationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, result.Date);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetBulkImportReportListQuery>(q => q.Id == operationId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDriftReport_ShouldReturnExpectedDriftReport()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";
        var infraId = Guid.NewGuid().ToString();
        var driftStatusId = "Drifted";

        var expectedReport = new DriftReportVm
        {
            ReportGeneratedBy = infraId,
            ReportGeneratedTime = startDate
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetDriftReportQuery>(q =>
                    q.Id == infraId &&
                    q.StartDate == startDate &&
                    q.EndDate == endDate &&
                    q.DriftStatus == driftStatusId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await ServiceUnderTest.GetDriftReport(startDate, endDate, infraId, driftStatusId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedReport.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedReport.ReportGeneratedTime, result.ReportGeneratedTime);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetDriftReportQuery>(q =>
                q.Id == infraId &&
                q.StartDate == startDate &&
                q.EndDate == endDate &&
                q.DriftStatus == driftStatusId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDriftReportInfraId_ShouldReturnExpectedReportList()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";

        var expectedList = new List<DriftEventReportVm>
        {
            new DriftEventReportVm { InfraObjectId = "infra-001", Entity = "Modified", Message = "2024-03-01" },
            new DriftEventReportVm { InfraObjectId = "infra-002", Entity = "Deleted", Message = "2024-06-15" }
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetDriftReportInfraObjectQuery>(q =>
                    q.StartDate == startDate &&
                    q.EndDate == endDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await ServiceUnderTest.GetDriftReportInfraId(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedList.Count, result.Count);
        Assert.Equal(expectedList[0].InfraObjectId, result[0].InfraObjectId);
        Assert.Equal(expectedList[1].Entity, result[1].Entity);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetDriftReportInfraObjectQuery>(q =>
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDriftReportStatus_ShouldReturnExpectedReportList()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";
        var infraId = "infra-001";

        var expectedResult = new List<DriftEventReportVm>
        {
            new DriftEventReportVm { InfraObjectId = infraId, Entity = "Added", Message = "2024-03-10" },
            new DriftEventReportVm { InfraObjectId = infraId, Entity = "Removed", Message = "2024-07-20" }
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetDriftReportStatusQuery>(q =>
                    q.Id == infraId &&
                    q.StartDate == startDate &&
                    q.EndDate == endDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await ServiceUnderTest.GetDriftReportStatus(startDate, endDate, infraId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResult.Count, result.Count);
        Assert.Equal(expectedResult[0].Entity, result[0].Entity);
        Assert.Equal(expectedResult[1].Message, result[1].Message);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetDriftReportStatusQuery>(q =>
                q.Id == infraId &&
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAirGapList_ShouldReturnExpectedList()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";

        var expectedList = new List<GetAirGapListVm>
        {
            new GetAirGapListVm { AirGapId = "gap-001", AirGapName = "Gap Report 1" },
            new GetAirGapListVm { AirGapId = "gap-002", AirGapName = "Gap Report 2" }
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetAirGapListQuery>(q =>
                    q.StartDate == startDate &&
                    q.EndDate == endDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await ServiceUnderTest.GetAirGapList(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedList.Count, result.Count);
        Assert.Equal(expectedList[0].AirGapName, result[0].AirGapName);
        Assert.Equal(expectedList[1].AirGapId, result[1].AirGapId);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetAirGapListQuery>(q =>
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetCyberSnapsList_ShouldReturnExpectedList()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-12-31";

        var expectedList = new List<GetCyberSnapsListVm>
        {
            new GetCyberSnapsListVm { Id = "cs-001", Name = "Cyber Snap 1" },
            new GetCyberSnapsListVm { Id = "cs-002", Name = "Cyber Snap 2" }
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetCyberSnapsListQuery>(q =>
                    q.StartDate == startDate &&
                    q.EndDate == endDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await ServiceUnderTest.GetCyberSnapsList(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedList.Count, result.Count);
        Assert.Equal(expectedList[0].Id, result[0].Id);
        Assert.Equal(expectedList[1].Name, result[1].Name);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetCyberSnapsListQuery>(q =>
                q.StartDate == startDate &&
                q.EndDate == endDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetScheduleWorkflowActionResultReport_ShouldReturnExpectedResult()
    {
        // Arrange
        var workflowId = "workflow-123";
        var infraReferenceId = "infra-456";

        var expectedResult = new SchedulerWorkflowActionResultsVm
        {
            ReportGeneratedBy = "Test Workflow",
            ReportGeneratedTime = "Completed"
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetSchedulerWorkflowActionResultsQuery>(q =>
                    q.WorkflowId == workflowId &&
                    q.InfraObjectId == infraReferenceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await ServiceUnderTest.GetScheduleWorkflowActionResultReport(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResult.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedResult.ReportGeneratedTime, result.ReportGeneratedTime);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetSchedulerWorkflowActionResultsQuery>(q =>
                q.WorkflowId == workflowId &&
                q.InfraObjectId == infraReferenceId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetCGExecutionReport_ShouldReturnExpectedResult()
    {
        // Arrange
        var workflowOperationId = "operation-123";

        var expectedResult = new CGExecutionReportResultVm
        {
            ReportGeneratedBy = "Test Execution Report",
            ReportGeneratedTime = "Success"
        };

        MediatorMock
            .Setup(m => m.Send(
                It.Is<GetCGExecutionReportQuery>(q =>
                    q.WorkflowOperationId == workflowOperationId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await ServiceUnderTest.GetCGExecutionReport(workflowOperationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedResult.ReportGeneratedBy, result.ReportGeneratedBy);
        Assert.Equal(expectedResult.ReportGeneratedTime, result.ReportGeneratedTime);

        MediatorMock.Verify(m => m.Send(
            It.Is<GetCGExecutionReportQuery>(q =>
                q.WorkflowOperationId == workflowOperationId),
            It.IsAny<CancellationToken>()), Times.Once);
    }
}
