﻿using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FastCopyMonitor.Queries
{
    public class GetFastCopyMonitorByDataSyncIdsQueryHandlerTests
    {
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IFastCopyMonitorRepository> _repositoryMock;
        private readonly GetFastCopyMonitorByDataSyncIdsQueryHandler _handler;

        public GetFastCopyMonitorByDataSyncIdsQueryHandlerTests()
        {
            var fastCopyMonitors = new List<Domain.Entities.FastCopyMonitor>
            {
                new Domain.Entities.FastCopyMonitor { Id = 1, DataSyncJobId = "job-001", SourceIP = "************" },
                new Domain.Entities.FastCopyMonitor { Id = 2, DataSyncJobId = "job-002", SourceIP = "************" }
            };

            _repositoryMock = FastCopyMonitorRepositoryMocks
                .GetFastCopyMonitorByDataSyncIdsRepositoryMocks(fastCopyMonitors);

            _mapperMock = new Mock<IMapper>();

            // Setup mapper to map domain → viewmodel
            _mapperMock.Setup(m => m.Map<List<FatCopyMonitorListVm>>(It.IsAny<List<Domain.Entities.FastCopyMonitor>>()))
                .Returns(new List<FatCopyMonitorListVm>
                {
                    new FatCopyMonitorListVm { DataSyncJobId = "job-001", SourceIP = "************" },
                    new FatCopyMonitorListVm { DataSyncJobId = "job-002", SourceIP = "************" }
                });

            _handler = new GetFastCopyMonitorByDataSyncIdsQueryHandler(
                _repositoryMock.Object,
                _mapperMock.Object);
        }

        [Fact(DisplayName = "Handle should return FastCopyMonitor list by DataSyncJobIds")]
        public async Task Handle_Should_Return_Expected_List()
        {
            // Arrange
            var query = new GetFastCopyMonitorByDataSyncIdsQuery
            {
                DataSyncJobIds = new List<string> { "job-001", "job-002" }
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("job-001", result[0].DataSyncJobId);
            Assert.Equal("************", result[0].SourceIP);
        }

        [Fact(DisplayName = "Handle should return empty list when no matching DataSyncJobIds found")]
        public async Task Handle_Should_Return_Empty_List_When_Repository_Returns_Empty()
        {
            // Arrange
            var emptyRepoMock = new Mock<IFastCopyMonitorRepository>();
            var emptyMapperMock = new Mock<IMapper>();

            // Setup repository to return empty list
            emptyRepoMock.Setup(repo =>
                repo.GetByDataSyncJobIds(It.IsAny<List<string>>()))
                .ReturnsAsync(new List<Domain.Entities.FastCopyMonitor>());

            // Setup mapper to return empty list
            emptyMapperMock.Setup(mapper =>
                mapper.Map<List<FatCopyMonitorListVm>>(It.IsAny<List<Domain.Entities.FastCopyMonitor>>()))
                .Returns(new List<FatCopyMonitorListVm>());

            var handler = new GetFastCopyMonitorByDataSyncIdsQueryHandler(
                emptyRepoMock.Object,
                emptyMapperMock.Object);

            var query = new GetFastCopyMonitorByDataSyncIdsQuery
            {
                DataSyncJobIds = new List<string> { "non-existent-job" }
            };

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            emptyRepoMock.Verify(repo => repo.GetByDataSyncJobIds(It.IsAny<List<string>>()), Times.Once);
            emptyMapperMock.Verify(mapper => mapper.Map<List<FatCopyMonitorListVm>>(It.IsAny<List<Domain.Entities.FastCopyMonitor>>()), Times.Once);
        }
    }
}
