﻿using ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.IncidentManagement.Queries;

public class GetIncidentManagementDetailQueryHandlerTests : IClassFixture<IncidentManagementFixture>
{
    private readonly IncidentManagementFixture _incidentManagementFixture;
    private readonly Mock<IIncidentManagementRepository> _mockIncidentManagementRepository;
    private readonly GetIncidentManagementDetailsQueryHandler _handler;
    private readonly GetIncidentManagementDetailsQueryHandler _invalidHandler;

    public GetIncidentManagementDetailQueryHandlerTests(IncidentManagementFixture incidentManagementFixture)
    {
        _incidentManagementFixture = incidentManagementFixture;

        _mockIncidentManagementRepository = IncidentManagementRepositoryMocks.GetIncidentManagementRepository(_incidentManagementFixture.IncidentManagements);

        _handler = new GetIncidentManagementDetailsQueryHandler(_incidentManagementFixture.Mapper, _mockIncidentManagementRepository.Object);

        var mockInvalidIncidentManagementRepository = IncidentManagementRepositoryMocks.GetIncidentManagementRepository(new List<Domain.Entities.IncidentManagement>());

        _invalidHandler = new GetIncidentManagementDetailsQueryHandler(_incidentManagementFixture.Mapper, mockInvalidIncidentManagementRepository.Object);

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
    }

    [Fact]
    public async Task Handle_Return_IncidentManagementDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<IncidentManagementDetailVm>();
        result.Id.ShouldBe(_incidentManagementFixture.IncidentManagements[0].ReferenceId);
        result.IncidentName.ShouldNotBeEmpty();
        result.Status.ShouldBeGreaterThanOrEqualTo(0);
        result.InfraId.ShouldNotBeEmpty();
        result.InfraComponentId.ShouldNotBeEmpty();
        result.InfraComponentType.ShouldNotBeEmpty();
        result.SourceId.ShouldNotBeEmpty();
        result.SourceTypId.ShouldNotBeEmpty();
        result.IncidentCode.ShouldNotBeEmpty();
        result.IncidentComment.ShouldNotBeEmpty();
        result.AppProcess.ShouldNotBeEmpty();
        result.JobName.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidIncidentManagementId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _invalidHandler.Handle(new GetIncidentManagementDetailQuery { Id = Guid.NewGuid().ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("IncidentManagement");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OneTime_When_ValidId()
    {
        await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        _mockIncidentManagementRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_CorrectIncidentTime_When_ValidId()
    {
        var result = await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        result.IncidentTime.ShouldBe(_incidentManagementFixture.IncidentManagements[0].IncidentTime);
        result.IncidentRecoveryTime.ShouldBe(_incidentManagementFixture.IncidentManagements[0].IncidentRecoveryTime);
    }

    [Fact]
    public async Task Handle_Return_CorrectFlags_When_ValidId()
    {
        var result = await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        result.Flag1.ShouldBe(_incidentManagementFixture.IncidentManagements[0].Flag1);
        result.Flag2.ShouldBe(_incidentManagementFixture.IncidentManagements[0].Flag2);
        result.Flag3.ShouldBe(_incidentManagementFixture.IncidentManagements[0].Flag3);
    }

    [Fact]
    public async Task Handle_Return_CorrectId_When_ValidId()
    {
        var result = await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<IncidentManagementDetailVm>();
        result.Id.ShouldBe(_incidentManagementFixture.IncidentManagements[0].ReferenceId);
        result.Id.ShouldNotBeNullOrEmpty();
        result.Id.ShouldBe("5287bf71-be04-4c55-97e8-a65b7ff17114");
    }

    [Fact]
    public async Task Handle_Return_IdAsString_When_ValidId()
    {
        var result = await _handler.Handle(new GetIncidentManagementDetailQuery { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        result.Id.ShouldBeOfType<string>();
        result.Id.Length.ShouldBeGreaterThan(0);
    }


}