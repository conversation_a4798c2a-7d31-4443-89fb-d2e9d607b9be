using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.InfraObjectInfo.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectInfo.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraObjectInfo.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObjectInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObjectInfo.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectInfoModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class InfraObjectInfoControllerTests : IClassFixture<InfraObjectInfoFixture>
{
    private readonly InfraObjectInfoFixture _infraObjectInfoFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly InfraObjectInfoController _controller;

    public InfraObjectInfoControllerTests(InfraObjectInfoFixture infraObjectInfoFixture)
    {
        _infraObjectInfoFixture = infraObjectInfoFixture;

        var testBuilder = new ControllerTestBuilder<InfraObjectInfoController>();
        _controller = testBuilder.CreateController(
            _ => new InfraObjectInfoController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetInfraObjectInfos_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_infraObjectInfoFixture.InfraObjectInfoListVm);

        // Act
        var result = await _controller.GetInfraObjectInfos();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectInfos = Assert.IsAssignableFrom<List<InfraObjectInfoListVm>>(okResult.Value);
        Assert.Equal(3, infraObjectInfos.Count);
    }

    [Fact]
    public async Task GetInfraObjectInfoById_ReturnsInfraObjectInfo_WhenIdIsValid()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraObjectInfoDetailQuery>(q => q.Id == infraObjectInfoId), default))
            .ReturnsAsync(_infraObjectInfoFixture.InfraObjectInfoDetailVm);

        // Act
        var result = await _controller.GetInfraObjectInfoById(infraObjectInfoId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetInfraObjectInfoById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetInfraObjectInfoById("invalid-guid"));
    }

    [Fact]
    public async Task CreateInfraObjectInfo_Returns201Created()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;
        var expectedMessage = $"InfraObjectInfo has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraObjectInfoResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateInfraObjectInfo(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateInfraObjectInfoResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_ReturnsOk()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;
        var expectedMessage = $"InfraObjectInfo has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraObjectInfoResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateInfraObjectInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateInfraObjectInfoResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "InfraObjectInfo has been deleted successfully!.";
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default))
            .ReturnsAsync(new DeleteInfraObjectInfoResponse
            {
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteInfraObjectInfo(infraObjectInfoId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteInfraObjectInfoResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteInfraObjectInfo("invalid-guid"));
    }

    [Fact]
    public async Task GetInfraObjectInfos_ReturnsEmptyList_WhenNoData()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<InfraObjectInfoListVm>());

        // Act
        var result = await _controller.GetInfraObjectInfos();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectInfos = Assert.IsAssignableFrom<List<InfraObjectInfoListVm>>(okResult.Value);
        Assert.Empty(infraObjectInfos);
    }

    [Fact]
    public async Task CreateInfraObjectInfo_CallsMediatorOnce()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraObjectInfoResponse
            {
                Message = "Created",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateInfraObjectInfo(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_CallsMediatorOnce()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraObjectInfoResponse
            {
                Message = "Updated",
                Id = command.Id
            });

        // Act
        await _controller.UpdateInfraObjectInfo(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_CallsMediatorOnce()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default))
            .ReturnsAsync(new DeleteInfraObjectInfoResponse
            {
                Message = "Deleted"
            });

        // Act
        await _controller.DeleteInfraObjectInfo(infraObjectInfoId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default), Times.Once);
    }

    [Fact]
    public async Task CreateInfraObjectInfo_WithDuplicateData_HandlesCorrectly()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;
        var validationResult = new ValidationResult();
        validationResult.Errors.Add(new ValidationFailure("Property", "Duplicate entry"));
        var validationException = new ValidationException(validationResult);

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(validationException);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() =>
            _controller.CreateInfraObjectInfo(command));
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;
        command.Id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("InfraObjectInfo",command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateInfraObjectInfo(command));
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default))
            .ThrowsAsync(new NotFoundException("InfraObjectInfo",infraObjectInfoId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteInfraObjectInfo(infraObjectInfoId));
    }

    [Fact]
    public async Task GetInfraObjectInfoById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraObjectInfoDetailQuery>(q => q.Id == infraObjectInfoId), default))
            .ThrowsAsync(new NotFoundException("InfraObjectInfo",infraObjectInfoId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetInfraObjectInfoById(infraObjectInfoId));
    }

    [Fact]
    public async Task CreateInfraObjectInfo_ReturnsCreatedAtAction_WithCorrectActionName()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;
        var responseId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraObjectInfoResponse
            {
                Message = "Created",
                Id = responseId
            });

        // Act
        var result = await _controller.CreateInfraObjectInfo(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        Assert.Equal("CreateInfraObjectInfo", createdResult.ActionName);
    }

    [Fact]
    public async Task GetInfraObjectInfos_WithMultipleItems_ReturnsCorrectCount()
    {
        // Arrange
        var expectedList = _infraObjectInfoFixture.InfraObjectInfoListVm;
        
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetInfraObjectInfos();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectInfos = Assert.IsAssignableFrom<List<InfraObjectInfoListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, infraObjectInfos.Count);
        Assert.Equal(expectedList.First().Id, infraObjectInfos.First().Id);
    }

    [Fact]
    public async Task GetInfraObjectInfoById_WithValidId_ReturnsCorrectData()
    {
        // Arrange
        var infraObjectInfoId = _infraObjectInfoFixture.InfraObjectInfoDetailVm.Id;
        var expectedDetail = _infraObjectInfoFixture.InfraObjectInfoDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraObjectInfoDetailQuery>(q => q.Id == infraObjectInfoId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetInfraObjectInfoById(infraObjectInfoId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var detail = Assert.IsType<InfraObjectInfoDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Id, detail.Id);
        Assert.Equal(expectedDetail.InfraObjectName, detail.InfraObjectName);
    }

    [Fact]
    public async Task CreateInfraObjectInfo_WithValidCommand_ReturnsSuccessResponse()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;
        var expectedId = Guid.NewGuid().ToString();
        var expectedMessage = "InfraObjectInfo created successfully";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraObjectInfoResponse
            {
                Message = expectedMessage,
                Id = expectedId
            });

        // Act
        var result = await _controller.CreateInfraObjectInfo(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateInfraObjectInfoResponse>(createdResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(expectedId, response.Id);
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_WithValidCommand_ReturnsSuccessResponse()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;
        var expectedMessage = "InfraObjectInfo updated successfully";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraObjectInfoResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateInfraObjectInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateInfraObjectInfoResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_WithValidId_ReturnsSuccessResponse()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();
        var expectedMessage = "InfraObjectInfo deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default))
            .ReturnsAsync(new DeleteInfraObjectInfoResponse
            {
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteInfraObjectInfo(infraObjectInfoId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteInfraObjectInfoResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetInfraObjectInfos_VerifiesMediatorCall()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<InfraObjectInfoListVm>());

        // Act
        await _controller.GetInfraObjectInfos();

        // Assert
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetInfraObjectInfoById_VerifiesMediatorCall()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraObjectInfoDetailQuery>(q => q.Id == infraObjectInfoId), default))
            .ReturnsAsync(_infraObjectInfoFixture.InfraObjectInfoDetailVm);

        // Act
        await _controller.GetInfraObjectInfoById(infraObjectInfoId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<GetInfraObjectInfoDetailQuery>(q => q.Id == infraObjectInfoId), default), Times.Once);
    }

    [Fact]
    public async Task CreateInfraObjectInfo_WithEmptyInfraObjectName_ThrowsValidationException()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;
        command.InfraObjectName = "";

        var validationResult = new ValidationResult();
        validationResult.Errors.Add(new ValidationFailure("InfraObjectName", "InfraObjectName is required"));
        var validationException = new ValidationException(validationResult);

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(validationException);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() =>
            _controller.CreateInfraObjectInfo(command));
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_WithEmptyInfraObjectName_ThrowsValidationException()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;
        command.InfraObjectName = "";

        var validationResult = new ValidationResult();
        validationResult.Errors.Add(new ValidationFailure("InfraObjectName", "InfraObjectName is required"));
        var validationException = new ValidationException(validationResult);

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(validationException);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() =>
            _controller.UpdateInfraObjectInfo(command));
    }

    [Fact]
    public async Task GetInfraObjectInfos_HandlesEmptyGuidCorrectly()
    {
        // Arrange
        var emptyList = new List<InfraObjectInfoListVm>();
        
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraObjectInfoListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetInfraObjectInfos();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectInfos = Assert.IsAssignableFrom<List<InfraObjectInfoListVm>>(okResult.Value);
        Assert.Empty(infraObjectInfos);
    }

    [Fact]
    public async Task DeleteInfraObjectInfo_ClearsCacheAfterDeletion()
    {
        // Arrange
        var infraObjectInfoId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraObjectInfoCommand>(c => c.Id == infraObjectInfoId), default))
            .ReturnsAsync(new DeleteInfraObjectInfoResponse
            {
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteInfraObjectInfo(infraObjectInfoId);

        // Assert
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        _controller.ClearDataCache();
        Assert.True(true);
    }

    [Fact]
    public async Task CreateInfraObjectInfo_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _infraObjectInfoFixture.CreateInfraObjectInfoCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraObjectInfoResponse
            {
                Message = "Created successfully",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateInfraObjectInfo(command);

        // Assert
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateInfraObjectInfo_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _infraObjectInfoFixture.UpdateInfraObjectInfoCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraObjectInfoResponse
            {
                Message = "Updated successfully",
                Id = command.Id
            });

        // Act
        await _controller.UpdateInfraObjectInfo(command);

        // Assert
        Assert.True(true);
    }
}



