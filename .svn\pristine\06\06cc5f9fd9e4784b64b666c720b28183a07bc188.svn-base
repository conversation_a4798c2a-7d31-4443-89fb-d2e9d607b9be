﻿using ContinuityPatrol.Application.Features.Job.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;


public class GetJobDetailQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _jobFixture;

    private readonly Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobDetailQueryHandler _handler;

    public GetJobDetailQueryHandlerTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetJobRepository(_jobFixture.Jobs);

        _handler = new GetJobDetailQueryHandler(_jobFixture.Mapper, _mockJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_JobDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetJobDetailQuery { Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<JobDetailVm>();
        result.Id.ShouldBe(_jobFixture.Jobs[0].ReferenceId);
        result.Name.ShouldBe(_jobFixture.Jobs[0].Name);
        result.InfraObjectProperties.ShouldBe(_jobFixture.Jobs[0].InfraObjectProperties);
        result.TemplateId.ShouldBe(_jobFixture.Jobs[0].TemplateId);
        result.TemplateName.ShouldBe(_jobFixture.Jobs[0].TemplateName);
        result.NodeId.ShouldBe(_jobFixture.Jobs[0].NodeId);
        result.NodeName.ShouldBe(_jobFixture.Jobs[0].NodeName);
        result.Status.ShouldBe(_jobFixture.Jobs[0].Status);
        result.CronExpression.ShouldBe(_jobFixture.Jobs[0].CronExpression);
        result.State.ShouldBe(_jobFixture.Jobs[0].State);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSiteId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetJobDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }
    [Fact]
    public void JobDetailVm_AssignProperties_ShouldSetCorrectly()
    {
        // Arrange & Act
        var vm = new JobDetailVm
        {
            Id = "job-001",
            Name = "DetailJob",
            InfraObjectProperties = "{\"os\":\"linux\"}",
            TemplateId = "tpl-001",
            TemplateName = "LinuxTemplate",
            SolutionType = "Backup",
            SolutionTypeId = "sol-123",
            Type = "Scheduled",
            NodeId = "node-001",
            NodeName = "NodeX",
            Status = "Active",
            CronExpression = "0 18 * * *",
            ScheduleTime = "18:00",
            ScheduleType = 1,
            IsSchedule = 1,
            State = "Running",
            GroupPolicyId = "gp-001",
            GroupPolicyName = "GP1",
            ExecutionPolicy = "Parallel",
            LastExecutionTime = "2025-07-22T18:00:00",
            ExceptionMessage = "None"
        };

        // Assert
        vm.Id.Should().Be("job-001");
        vm.Name.Should().Be("DetailJob");
        vm.InfraObjectProperties.Should().Be("{\"os\":\"linux\"}");
        vm.TemplateId.Should().Be("tpl-001");
        vm.TemplateName.Should().Be("LinuxTemplate");
        vm.SolutionType.Should().Be("Backup");
        vm.SolutionTypeId.Should().Be("sol-123");
        vm.Type.Should().Be("Scheduled");
        vm.NodeId.Should().Be("node-001");
        vm.NodeName.Should().Be("NodeX");
        vm.Status.Should().Be("Active");
        vm.CronExpression.Should().Be("0 18 * * *");
        vm.ScheduleTime.Should().Be("18:00");
        vm.ScheduleType.Should().Be(1);
        vm.IsSchedule.Should().Be(1);
        vm.State.Should().Be("Running");
        vm.GroupPolicyId.Should().Be("gp-001");
        vm.GroupPolicyName.Should().Be("GP1");
        vm.ExecutionPolicy.Should().Be("Parallel");
        vm.LastExecutionTime.Should().Be("2025-07-22T18:00:00");
        vm.ExceptionMessage.Should().Be("None");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetJobDetailQuery { Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}