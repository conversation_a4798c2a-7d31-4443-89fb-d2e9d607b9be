﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetBusinessViewPaginatedList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDatalagByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDcMappingList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetImpactAvailabilityByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetNames;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewEntitiesEvent;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationCyberSecurity;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationFailedDrill;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DashboardViewController : CommonBaseController
{
  
    [HttpGet("by/{infraObjectId}")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<ItViewByInfraObjectIdVm>> GetItViewByInfraObjectId(
       string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "ITView InfraObjectId");

        Logger.LogDebug($"Get ITView Detail by InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetItViewByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }


    [Route("names"), HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DashboardViewNameVm>>> GetDashboardNames()
    {
        Logger.LogDebug("Get All Dashboard Names");

        ClearDataCache();

        return Ok(await Mediator.Send(new GetDashboardNameQuery()));
    }

    [HttpGet("{id}", Name = "DataLagStatus")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DashboardViewDetailVm>> GetDashboardViewById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardView Id");

        Logger.LogDebug($"Get DashboardView Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDashboardViewDetailQuery { Id = id }));
    }

    [Route("business-views"), HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<BusinessViewPaginatedList>>> GetBusinessViews()
    {
        Logger.LogDebug("Get OperationalView List");

        return Ok(await Mediator.Send(new GetBusinessViewPaginatedListQuery()));
    }

    [Route("businessServiceId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DashboardViewByBusinessServiceIdVm>>>
       GetDashboardViewListByBusinessServiceId(string businessServiceId)
    {
        Logger.LogDebug($"Get DashboardViewList By OperationalServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetDashboardViewByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId }));
    }

    [Route("businessFunctionId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DashboardViewByBusinessFunctionIdVm>>>
        GetDashboardViewListByBusinessFunctionId(string businessFunction)
    {
        Logger.LogDebug($"Get DashboardViewList By OperationalFunctionId '{businessFunction}'");

        return Ok(await Mediator.Send(new GetDashboardViewByBusinessFunctionIdQuery
        { BusinessFunctionId = businessFunction }));
    }

    [Route("infraObjectId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetDashboardViewByInfraObjectIdVm>> GetDashboardViewListByInfraObjectId(
        string infraObjectId)
    {
        Logger.LogDebug($"Get DashboardViewList Detail By InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetDashboardViewByInfraObjectIdQuery
        { InfraObjectId = infraObjectId }));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DashboardViewListVm>>> GetDashboardViews()
    {
        Logger.LogDebug("Get All DashboardViews");

        return Ok(await Mediator.Send(new GetDashboardViewListQuery()));
    }

    [Route("dcmapping")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<GetDcMappingListVm>>> GetDcMappingDetails(string? siteId)
    {
        Logger.LogDebug($"Get DcMappings by siteId '{siteId}'");

        return Ok(await Mediator.Send(new GetDcMappingListQuery { SiteId= siteId }));
    }
   
    [Route("business-service-topology")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<GetServiceTopologyListVm>>> GetBusinessServiceTopologyByBusinessServiceId(string? businessServiceId)
    {
        Logger.LogDebug($"Get OperationalService Topology By OperationalServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetServiceTopologyListQuery { BusinessServiceId = businessServiceId }));
    }


    [HttpGet]
    [Route("lastList")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DataLagStatusbyLast7DaysVm>>> GetDatalagStatusByLast7DaysList()
    {
        Logger.LogDebug("Get All Datalag Status");

        return Ok(await Mediator.Send(new GetDataLagStatusbyLast7DaysQuery()));
    }

    [Route("It-View")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<GetItViewListVm>>> GetItViewList()
    {
       Logger.LogDebug($"Get It-View List");

        return Ok(await Mediator.Send(new GetItViewListQuery()));
    }

    [Route("it-View-businessServiceId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<ItViewByBusinessServiceIdVm>>> GetItViewByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "ITView BusinessServiceId");
        Logger.LogDebug($"Get It-View detail by businessServiceId {businessServiceId}");

        return Ok(await Mediator.Send(new GetItViewByBusinessServiceIdQuery{BusinessServiceId = businessServiceId}));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateDashboardViewResponse>> CreateDashboardView([FromBody] CreateDashboardViewCommand createDashboardViewCommand)
    {
        Logger.LogDebug($"Create Dashboard View '{createDashboardViewCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDashboardView), await Mediator.Send(createDashboardViewCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateDashboardViewResponse>> UpdateDashboardView([FromBody] UpdateDashboardViewCommand updateDashboardViewCommand)
    {
        Logger.LogDebug($"Update Dashboard View'{updateDashboardViewCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDashboardViewCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteDashboardViewResponse>> DeleteDashboardView(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardView Id");

        Logger.LogDebug($"Delete DashboardView Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDashboardViewCommand { BusinessViewId = id }));
    }

    [HttpGet("by/monitorId")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetByEntityIdVm>> GetMonitorServiceStatusByIdAndType(string monitorId,string type)
    {
        Guard.Against.InvalidGuidOrEmpty(monitorId, "MonitorServiceStatus MonitorId");

        Logger.LogDebug($"Get MonitorServiceStatus Detail by EntityId '{monitorId}' and '{type}");

        return Ok(await Mediator.Send(new GetByEntityIdQuery { EntityId = monitorId, Type = type }));
    }

    [HttpGet("datacenter")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<SitePropertiesByBusinessServiceIdVm>> GetSitePropertiesByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "DataCenter BusinessServiceId");

        Logger.LogDebug($"Get DataCenter Details By BusinessServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetSitePropertiesByBusinessServiceIdQuery { BusinessServiceId = businessServiceId }));
    }

    [HttpGet("resilience-health-status")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<ResilienceHealthStatusDetailVm>> GetResilienceHealthStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get Resilience Health Status By InfraObject Id '{infraObjectId}'");

        return Ok(await Mediator.Send(new ResilienceHealthStatusDetailQuery { InfraObjectId = infraObjectId }));
    }


    #region Analytics
    [HttpGet("component-failure-analytics")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<ComponentFailureAnalyticsDetailVm>> GetComponentFailureAnalytics()
    {
        Logger.LogDebug($"Get Analytics Details for component Failure");

        return Ok(await Mediator.Send(new ComponentFailureAnalyticsDetailQuery()));
    }

    [HttpGet("workflow-analytics")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetWorkflowAnalyticsDetailVm>> GetWorkflowAnalytics()
    {
        Logger.LogDebug($"Get Analytics Details for workflow analytics");

        return Ok(await Mediator.Send(new GetWorkflowAnalyticsQuery()));
    }

    [HttpGet("drill-analytic")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DrillAnalyticsDetailVm>> GetDrillAnalytic()
    {
        Logger.LogDebug("$Get Operational Readiness for drill analytic");

        return Ok(await Mediator.Send(new GetOperationalReadinessQuery()));
    }

    [HttpGet("sla-breach")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetSlaBreachListVm>> GetSlaBreach()
    {
        Logger.LogDebug($"Get Analytics Details for Sla Breach");

        return Ok(await Mediator.Send(new GetSlaBreachListQuery()));
    }

    [HttpGet("operational-availability-analytics")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetOperationalAvailabilityAnalyticsDetailVm>> GetOperationalAvailabilityAnalytics()
    {
        Logger.LogDebug($"Get Analytics Details for Operational Availability");

        return Ok(await Mediator.Send(new GetOperationalAvailabilityAnalyticsQuery()));
    }

    [HttpGet("operational-service-health-summary")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<OperationalHealthSummaryDetailVm>>> GetOperationalServiceHealthSummary()
    {
        Logger.LogDebug("Get Analytics Operational Service Health Summary");
        return Ok(await Mediator.Send(new OperationalHealthSummaryQuery()));
    }
    [HttpGet("business-impact-analysis")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<BusinessImpactAnalysisVm>>> GetBusinessImpactAnalysis()
    {
        Logger.LogDebug("Get Business Impact Analysis details");
        return Ok(await Mediator.Send(new GetBusinessImpactAnalysisQuery()));
    }


    #endregion


    #region OneView

    [HttpGet("one-view-sites")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<SiteCountListVm>> GetSiteCountList()
    {
        Logger.LogDebug($"Get Site Details for one-view");

        return Ok(await Mediator.Send(new GetSiteCountListQuery()));
    }

    [HttpGet]
    [Route("verified-workflow")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<VerifyWorkflowDetailVm>> GetVerifiedWorkflowList()
    {
        Logger.LogDebug("Get All Verified Workflow");

        return Ok(await Mediator.Send(new GetVerifyWorkflowDetailQuery()));
    }

    [HttpGet("dcmapping-sites")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<GetDcMappingSitesVm>> GetDcMappingSiteDetails()
    {
        Logger.LogDebug($"Get Site Details for DCMapping");

        return Ok(await Mediator.Send(new GetDcMappingSitesQuery()));
    }

    [HttpGet("totalsites-for-oneview")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<TotalSiteDetailForOneViewListVm>> GetTotalSiteDetailsForOneView(string? siteId,string? category)
    {
        var sites = siteId.IsNotNullOrWhiteSpace() 
            ? JsonConvert.DeserializeObject<List<string>>(siteId!)
          : new List<string>();

        Logger.LogDebug($" Get Total Site Details For OneView");

        return Ok(await Mediator.Send(new GetTotalSiteDetailForOneViewQuery{SiteIds= sites, CategoryType=category }));
    }

    [HttpGet]
    [Route("rto-rpo-breach")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<BreachDetailVm>> GetBreachDetails()
    {
        Logger.LogDebug("Get RPO and RTO Breach Detail.");

        return Ok(await Mediator.Send(new GetBreachDetailQuery()));
    }
    [HttpGet]
    [Route("last-drill")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<LastDrillDetailVm>> GetLastDrillDetails()
    {
        Logger.LogDebug("Get Last Drill Detail.");

        return Ok(await Mediator.Send(new LastDrillDetailQuery()));
    }



    [HttpGet]
    [Route("one-View-entity-event")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<OneViewEntitiesEventView>>> GetOneViewEntityEventList()
    {
        Logger.LogDebug("Get  OneView Entity Event list.");

        return Ok(await Mediator.Send(new GetOneViewEntitiesEventQuery()));
    }



    [HttpGet]
    [Route("one-View-Riskmitigation-CyberSecurity")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<OneViewRiskMitigationCyberSecurityView>>> GetOneViewRiskmitigationCyberSecurityList()
    {
        Logger.LogDebug("Get  OneView Riskmitigation CyberSecurity List.");

        return Ok(await Mediator.Send(new GetOneViewRiskMitigationCyberSecurityQuery()));
    }

    [HttpGet]
    [Route("one-View-Riskmitigation-Failed-Drill")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<OneViewRiskMitigationFailedDrillView>>> GetOneViewRiskmitigationFailedDrillList()
    {
        Logger.LogDebug("Get OneView Riskmitigation Failed Drill List.");

        return Ok(await Mediator.Send(new GetOneViewRiskMitigationFailedDrillQuery()));
    }
    #endregion

    [HttpGet]
    [Route("Imapct-Avialability")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DashboardImpactAvailabilityDetailVm>> GetDashBoardImpactAvailabilityByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "Impact-Availability BusinessServiceId");
        Logger.LogDebug($"Get Impact Availability y businessServiceId { businessServiceId}.");

        return Ok(await Mediator.Send(new GetImpactAvailabilityByBusinessServiceIdQuery
        {
            BusinessServiceId = businessServiceId
        }));
    }

    [HttpGet]
    [Route("Datalag-by-businessServiceId")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DatalagByBusinessServiceIdVm>> GetDatalagDetailByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "Datalag BusinessServiceId");
        Logger.LogDebug($"Get Impact Availability by businessServiceId {businessServiceId}.");

        return Ok(await Mediator.Send(new GetDataLagDetailByBusinessServiceIdQuery
        {
            BusinessServiceId = businessServiceId
        }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllBusinessViewNameCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}