﻿using ContinuityPatrol.Application.Features.FiaTemplate.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaTemplate.Events;

public class DeleteFiaTemplateEventTests : IClassFixture<FiaTemplateFixture>, IClassFixture<UserActivityFixture>
{
    private readonly FiaTemplateFixture _fiaTemplateFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly FiaTemplateDeletedEventHandler _handler;

    public DeleteFiaTemplateEventTests(FiaTemplateFixture fiaTemplateFixture, UserActivityFixture userActivityFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockFiaTemplateEventLogger = new Mock<ILogger<FiaTemplateDeletedEventHandler>>();

        _mockUserActivityRepository = FiaTemplateRepositoryMocks.CreateFiaTemplateEventRepository(_userActivityFixture.UserActivities);

        _handler = new FiaTemplateDeletedEventHandler(mockLoggedInUserService.Object, mockFiaTemplateEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteFiaTemplateEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_fiaTemplateFixture.FiaTemplateDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_fiaTemplateFixture.FiaTemplateDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}