﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowExecutionEventLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetPaginatedList;

public class GetWorkflowExecutionEventLogPaginatedListQueryHandler : IRequestHandler<
    GetWorkflowExecutionEventLogPaginatedListQuery, PaginatedResult<WorkflowExecutionEventLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowExecutionEventLogRepository _workflowExecutionEventLogRepository;

    public GetWorkflowExecutionEventLogPaginatedListQueryHandler(IMapper mapper,
        IWorkflowExecutionEventLogRepository workflowExecutionEventLogRepository)
    {
        _mapper = mapper;
        _workflowExecutionEventLogRepository = workflowExecutionEventLogRepository;
    }

    public async Task<PaginatedResult<WorkflowExecutionEventLogListVm>> Handle(
        GetWorkflowExecutionEventLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new WorkflowExecutionEventLogFilterSpecification(request.SearchString);

        var queryable = await _workflowExecutionEventLogRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var workflowExecutionEventLogList = _mapper.Map<PaginatedResult<WorkflowExecutionEventLogListVm>>(queryable);
        
        return workflowExecutionEventLogList;
    }
}