using ContinuityPatrol.Application.Features.LoadBalancer.Events.PaginatedView;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Enums;
namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Events;

public class LoadBalancerPaginatedEventHandlerTests
{
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<LoadBalancerPaginatedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly LoadBalancerPaginatedEventHandler _handler;

    public LoadBalancerPaginatedEventHandlerTests()
    {
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<LoadBalancerPaginatedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();

        _mockUserService.SetupGet(x => x.UserId).Returns("user-123");
        _mockUserService.SetupGet(x => x.LoginName).Returns("testuser");
        _mockUserService.SetupGet(x => x.CompanyId).Returns("company-123");
        _mockUserService.SetupGet(x => x.RequestedUrl).Returns("/api/loadbalancer");
        _mockUserService.SetupGet(x => x.IpAddress).Returns("********");

        _handler = new LoadBalancerPaginatedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldLogAndRecordUserActivity_WhenPaginatedViewEventOccurs()
    {
        // Arrange
        var paginatedEvent = new LoadBalancerPaginatedEvent();

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == "user-123" &&
            ua.LoginName == "testuser" &&
            ua.CompanyId == "company-123" &&
            ua.RequestUrl == "/api/loadbalancer" &&
            ua.HostAddress == "********" &&
            ua.Entity == Modules.LoadBalancer.ToString() &&
            ua.Action == $"{ActivityType.View} {Modules.LoadBalancer}" &&
            ua.ActivityType == ActivityType.View.ToString() &&
            ua.ActivityDetails == "Load Balancer viewed"
        )), Times.Once);

    
    }
}