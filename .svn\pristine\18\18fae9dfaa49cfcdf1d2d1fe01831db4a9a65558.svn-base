using ContinuityPatrol.Application.Features.SolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.SolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.SolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SolutionMappingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ISolutionMappingService
{
    Task<List<SolutionMappingListVm>> GetSolutionMappingList();
    Task<BaseResponse> CreateAsync(CreateSolutionMappingCommand createSolutionMappingCommand);
    Task<BaseResponse> UpdateAsync(UpdateSolutionMappingCommand updateSolutionMappingCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<SolutionMappingDetailVm> GetByReferenceId(string id);
    #region NameExist
 Task<bool> IsSolutionMappingNameExist(string name, string id);
   #endregion
    #region Paginated
 Task<PaginatedResult<SolutionMappingListVm>> GetPaginatedSolutionMappings(GetSolutionMappingPaginatedListQuery query);
    #endregion
}
