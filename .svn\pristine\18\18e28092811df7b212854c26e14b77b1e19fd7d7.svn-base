﻿using ContinuityPatrol.Application.Features.Job.Queries.GetNames;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobStatus;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage
{
    public class ReplicationJobService : IReplicationJobService
    {
        private readonly IBaseClient _client;

        public ReplicationJobService(IBaseClient client)
        {
            _client = client;
        }

        public async Task<BaseResponse> CreateReplicationJob(CreateReplicationJobCommand createReplicationJobCommand)
        {
            var request = new RestRequest("api/v6/replicationjobs", Method.Post);

            request.AddJsonBody(createReplicationJobCommand);

            return await _client.Post<BaseResponse>(request);
        }

        public async Task<BaseResponse> DeleteReplicationJob(string id)
        {
            var request = new RestRequest($"api/v6/replicationjobs/{id}", Method.Delete);

            return await _client.Delete<BaseResponse>(request);
        }

        public async Task<PaginatedResult<ReplicationJobListVm>> GetPaginated(GetReplicationJobPaginatedListQuery query)
        {
            var request = new RestRequest("api/v6/rsyncoptions/paginated-list");

            return await _client.Get<PaginatedResult<ReplicationJobListVm>>(request);
        }

        public async Task<ReplicationJobListVm> GetReplicationJobById(string id)
        {
            var request = new RestRequest($"api/v6/replicationjobs/{id}", Method.Get);

            return await _client.Get<ReplicationJobListVm>(request);
        }

        public async Task<List<ReplicationJobListVm>> GetReplicationJobList()
        {
            var request = new RestRequest($"api/v6/replicationjobs");

            return await _client.Get<List<ReplicationJobListVm>>(request);
        }

        public async Task<bool> IsReplicationJobNameExist(string name, string id)
        {
            var request = new RestRequest($"api/v6/replicationjobs/name-exist?Name={name}&id={id}");

            return await _client.Get<bool>(request);
        }

        public async Task<BaseResponse> UpdateReplicationJob(UpdateReplicationJobCommand updateReplicationJobCommand)
        {
            var request = new RestRequest("api/v6/replicationjobs", Method.Put);

            request.AddJsonBody(updateReplicationJobCommand);

            return await _client.Put<BaseResponse>(request);
        }

        public async  Task<BaseResponse> UpdateReplicationJobState(UpdateReplicationJobStateCommand updateReplicationJobStateCommand)
        {
            var request = new RestRequest("api/v6/replicationjobs", Method.Put);

            request.AddJsonBody(updateReplicationJobStateCommand);

            return await _client.Put<BaseResponse>(request);
        }

        public async Task<BaseResponse> UpdateReplicationJobStatus(UpdateReplicationJobStatusCommand updateReplicationJobStatus)
        {
            var request = new RestRequest("api/v6/replicationjobs", Method.Put);

            request.AddJsonBody(updateReplicationJobStatus);

            return await _client.Put<BaseResponse>(request);
        }
    }
}
