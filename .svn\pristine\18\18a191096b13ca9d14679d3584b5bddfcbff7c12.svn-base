﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowIdAndVersion;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class WorkflowHistoryServiceFixture : IDisposable
{
    public List<WorkflowHistoryListVm> WorkflowHistoryList { get; }
    public List<WorkflowHistoryByWorkflowIdVm> WorkflowHistoryByWorkflowIdList { get; }
    public WorkflowHistoryByWorkflowIdAndVersionVm WorkflowHistoryByWorkflowIdAndVersionVm { get; }
    public GetWorkflowHistoryPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<WorkflowHistoryListVm> PaginatedResult { get; }

    public WorkflowHistoryServiceFixture()
    {
        var fixture = new Fixture();

        WorkflowHistoryList = fixture.Create<List<WorkflowHistoryListVm>>();
        WorkflowHistoryByWorkflowIdList = fixture.Create<List<WorkflowHistoryByWorkflowIdVm>>();
        WorkflowHistoryByWorkflowIdAndVersionVm = fixture.Create<WorkflowHistoryByWorkflowIdAndVersionVm>();
        PaginatedQuery = fixture.Create<GetWorkflowHistoryPaginatedListQuery>();
        PaginatedResult = fixture.Create<PaginatedResult<WorkflowHistoryListVm>>();
    }

    public void Dispose()
    {
        // Clean-up if needed
    }
}
