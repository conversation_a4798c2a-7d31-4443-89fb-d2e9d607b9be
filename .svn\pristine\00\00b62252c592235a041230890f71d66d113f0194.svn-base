using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetMYSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MysqlMonitorStatusControllerTests : IClassFixture<MysqlMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MysqlMonitorStatusController _controller;
    private readonly MysqlMonitorStatusFixture _fixture;

    public MysqlMonitorStatusControllerTests(MysqlMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MysqlMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new MysqlMonitorStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMysqlMonitorStatus_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MysqlMonitorStatusListVm);

        var result = await _controller.GetAllMysqlMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mysqlMonitorStatusList = Assert.IsAssignableFrom<List<MYSQLMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.MysqlMonitorStatusListVm.Count, mysqlMonitorStatusList.Count);
    }

    [Fact]
    public async Task GetAllMysqlMonitorStatus_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MYSQLMonitorStatusListVm>());

        var result = await _controller.GetAllMysqlMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mysqlMonitorStatusList = Assert.IsAssignableFrom<List<MYSQLMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(mysqlMonitorStatusList);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusById_Should_Return_Detail()
    {
        var mysqlMonitorStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMYSQLMonitorStatusDetailQuery>(q => q.Id == mysqlMonitorStatusId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MysqlMonitorStatusDetailVm);

        var result = await _controller.GetMysqlMonitorStatusById(mysqlMonitorStatusId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mysqlMonitorStatusDetail = Assert.IsAssignableFrom<MYSQLMonitorStatusDetailVm>(okResult.Value);
        Assert.NotNull(mysqlMonitorStatusDetail);
        Assert.Equal(_fixture.MysqlMonitorStatusDetailVm.Id, mysqlMonitorStatusDetail.Id);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMysqlMonitorStatusById(null!));
    }

    [Fact]
    public async Task GetMysqlMonitorStatusById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMysqlMonitorStatusById(""));
    }

    [Fact]
    public async Task GetMysqlMonitorStatusById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMysqlMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMysqlMonitorStatus_Should_Return_Result()
    {
        var query = _fixture.GetMysqlMonitorStatusPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMysqlMonitorStatusListVm);

        var result = await _controller.GetPaginatedMysqlMonitorStatus(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<MYSQLMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMysqlMonitorStatusListVm, paginatedResult);
    }

    [Fact]
    public async Task CreateMysqlMonitorStatus_Should_Return_Success()
    {
        var response = new CreateMYSQLMonitorStatusResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMysqlMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMysqlMonitorStatus(_fixture.CreateMysqlMonitorStatusCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMYSQLMonitorStatusResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMysqlMonitorStatus_Should_Return_Success()
    {
        var response = new UpdateMYSQLMonitorStatusResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMysqlMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMysqlMonitorStatus(_fixture.UpdateMysqlMonitorStatusCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMYSQLMonitorStatusResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMYSQLMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MysqlMonitorStatusDetailByTypeVm);

        var result = await _controller.GetMysqlMonitorStatusByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<List<MYSQLMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMysqlMonitorStatusByType(null!));
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMysqlMonitorStatusByType(""));
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMYSQLMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-result");

        var result = await _controller.GetMysqlMonitorStatusByInfraObjectId(infraObjectId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectResult = Assert.IsAssignableFrom<string>(okResult.Value);
        Assert.Equal("test-result", infraObjectResult);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMysqlMonitorStatusByInfraObjectId(null!));
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectId_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMysqlMonitorStatusByInfraObjectId(""));
    }

    [Fact]
    public async Task GetAllMysqlMonitorStatus_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MysqlMonitorStatusListVm);

        await _controller.GetAllMysqlMonitorStatus();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMysqlMonitorStatus_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMysqlMonitorStatus());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMysqlMonitorStatus_VerifiesQueryType()
    {
        GetMYSQLMonitorStatusListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMYSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MYSQLMonitorStatusListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMYSQLMonitorStatusListQuery;
            })
            .ReturnsAsync(_fixture.MysqlMonitorStatusListVm);

        await _controller.GetAllMysqlMonitorStatus();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMYSQLMonitorStatusListQuery>(capturedQuery);
    }
}
