using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Delete;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormType.Queries.GetList;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNameUnique;
using ContinuityPatrol.Services.Db.Impl.Admin;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Admin;

public class FormTypeServiceTests : BaseServiceTestSetup<FormTypeService>, IClassFixture<FormTypeFixture>
{
    private readonly FormTypeFixture _fixture;

    public FormTypeServiceTests(FormTypeFixture fixture)
    {
        InitializeService(accessor => new FormTypeService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetFormTypeNames_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FormTypeNameVm);

        var result = await ServiceUnderTest.GetFormTypeNames();

        Assert.Equal(_fixture.FormTypeNameVm.Count, result.Count);
    }

    [Fact]
    public async Task GetFormTypeList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FormTypeListVm);

        var result = await ServiceUnderTest.GetFormTypeList();

        Assert.Equal(_fixture.FormTypeListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var formTypeId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FormTypeDetailVm);

        var result = await ServiceUnderTest.GetByReferenceId(formTypeId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.FormTypeDetailVm.Id, result.Id);
    }

    [Theory]
    [InlineData("FormTypeName1", null)]
    [InlineData("AnotherFormType", "some-guid")]
    public async Task IsFormTypeNameExist_Should_Return_True(string formTypeName, string id)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetFormTypeNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsFormTypeNameExist(formTypeName, id);

        Assert.True(result);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateFormTypeResponse
        {
            Message = "Created",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateFormTypeCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateFormTypeCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateFormTypeResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateFormTypeCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateFormTypeCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var formTypeId = Guid.NewGuid().ToString();
        var response = new DeleteFormTypeResponse
        {
            Message = "Deleted",
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.IsAny<DeleteFormTypeCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(formTypeId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetPaginatedFormTypes_Should_Return_Result()
    {
        var query = _fixture.GetFormTypePaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedFormTypeListVm);

        var result = await ServiceUnderTest.GetPaginatedFormTypes(query);

        Assert.Equal(_fixture.FormTypeListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task IsFormTypeNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.IsFormTypeNameExist(null!, null));
    }

    [Fact]
    public async Task IsFormTypeNameExist_EmptyName_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.IsFormTypeNameExist("", null));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }

    [Fact]
    public async Task GetByReferenceId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetByReferenceId(null!));
    }

    [Fact]
    public async Task GetByReferenceId_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetByReferenceId(""));
    }
}
