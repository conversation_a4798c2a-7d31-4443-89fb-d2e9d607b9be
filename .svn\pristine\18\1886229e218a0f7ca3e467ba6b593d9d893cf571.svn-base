﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class FormTypeCategoryRepositoryMocks
{
    public static Mock<IFormTypeCategoryRepository> CreateFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        formTypeCategoryRepository.Setup(repo => repo.AddAsync(It.IsAny<FormTypeCategory>())).ReturnsAsync(
            (FormTypeCategory formTypeCategory) =>
            {
                formTypeCategory.Id = new Fixture().Create<int>();
                formTypeCategory.ReferenceId = new Fixture().Create<Guid>().ToString();
                formTypeCategories.Add(formTypeCategory);
                return formTypeCategory;
            });

        formTypeCategoryRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<FormTypeCategory>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<FormTypeCategory> spec, string sortColumn, string sortOrder) =>
            {
                var sortedFormTypeCategories = formTypeCategories.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedFormTypeCategories = sortedFormTypeCategories.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming FormTypeCategory has a Name property; replace logic as needed
                    sortedFormTypeCategories = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedFormTypeCategories.OrderByDescending(c => c.Name)
                        : sortedFormTypeCategories.OrderBy(c => c.Name);
                }

                var totalCount = sortedFormTypeCategories.Count();
                var paginated = sortedFormTypeCategories
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<FormTypeCategory>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> UpdateFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var mockFormTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        mockFormTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        mockFormTypeCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => formTypeCategories.SingleOrDefault(x => x.ReferenceId == i));

        mockFormTypeCategoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<FormTypeCategory>())).ReturnsAsync((FormTypeCategory formTypeCategory) =>
        {
            var index = formTypeCategories.FindIndex(item => item.ReferenceId == formTypeCategory.ReferenceId);
            formTypeCategories[index] = formTypeCategory;
            return formTypeCategory;
        });

        return mockFormTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> DeleteFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var mockFormTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        mockFormTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        mockFormTypeCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => formTypeCategories.SingleOrDefault(x => x.ReferenceId == i));

        mockFormTypeCategoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<FormTypeCategory>())).ReturnsAsync((FormTypeCategory formTypeCategory) =>
        {
            var index = formTypeCategories.FindIndex(item => item.ReferenceId == formTypeCategory.ReferenceId);

            formTypeCategory.IsActive = false;

            formTypeCategories[index] = formTypeCategory;

            return formTypeCategory;
        });

        return mockFormTypeCategoryRepository;
    }
  
    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoriesByFormTypeIds(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepo = new Mock<IFormTypeCategoryRepository>();
        
        formTypeCategoryRepo.Setup(repo => repo.GetFormTypeCategoriesByFormTypeIds(It.IsAny<List<string>>()))
            .ReturnsAsync(formTypeCategories);

        return formTypeCategoryRepo;
    }
    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryByFormTypeIdRepository(FormTypeCategory formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeId(It.IsAny<string>())).ReturnsAsync(formTypeCategories);

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryEmptyRepository()
    {
        var mockFormTypeRepository = new Mock<IFormTypeCategoryRepository>();

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeId(It.IsAny<string>())).ReturnsAsync(new FormTypeCategory());

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<FormTypeCategory>());

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetPaginatedFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<FormTypeCategory>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<FormTypeCategory> spec, string sortColumn, string sortOrder) =>
            {
                var sortedFormTypeCategories = formTypeCategories.AsQueryable();
                
                if (spec.Criteria != null)
                {
                    var filtered = sortedFormTypeCategories.Where(spec.Criteria).ToList();

                    if (!filtered.Any())
                        filtered = formTypeCategories.Take(pageSize).ToList();

                    sortedFormTypeCategories = filtered.AsQueryable();
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    sortedFormTypeCategories = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedFormTypeCategories.OrderByDescending(c => c.Name)
                        : sortedFormTypeCategories.OrderBy(c => c.Name);
                }

                var totalCount = sortedFormTypeCategories.Count();
                var paginated = sortedFormTypeCategories
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

               return PaginatedResult<FormTypeCategory>.Success(paginated, totalCount, pageNumber, pageSize);
            });

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        formTypeCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => formTypeCategories.SingleOrDefault(x => x.ReferenceId == i));

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByName(It.IsAny<string>())).ReturnsAsync((string name) => formTypeCategories.Where(x => x.Name == name).ToList());

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeId(It.IsAny<string>())).ReturnsAsync((string formTypeId) => formTypeCategories.SingleOrDefault(x => x.FormTypeId == formTypeId));

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeIdAndVersion(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string formTypeId, string version) => formTypeCategories.SingleOrDefault(x => x.FormTypeId == formTypeId && x.Version == version));

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryNamesRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryNames()).ReturnsAsync(formTypeCategories.ToList());

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryNameUniqueRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(formTypeCategories);

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByName(It.IsAny<string>())).ReturnsAsync((string name) => formTypeCategories.Where(x => x.Name == name).ToList());

        formTypeCategoryRepository.Setup(repo => repo.IsFormTypeCategoryNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string name, string id) =>
        {
            // If id is null or empty, check if any FormTypeCategory has the given FormTypeName
            if (string.IsNullOrEmpty(id))
            {
                return formTypeCategories.Any(x => x.FormTypeName == name && x.IsActive);
            }
            // If id is provided, check if any FormTypeCategory has the given FormTypeName and matches the id
            return formTypeCategories.Any(x => x.FormTypeName == name && x.ReferenceId == id && x.IsActive);
        });

        return formTypeCategoryRepository;
    }

    public static Mock<IUserActivityRepository> CreateFormTypeCategoryEventRepository(List<UserActivity> userActivities)
    {
        var userActivityRepository = new Mock<IUserActivityRepository>();

        userActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        userActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return userActivityRepository;
    }
}