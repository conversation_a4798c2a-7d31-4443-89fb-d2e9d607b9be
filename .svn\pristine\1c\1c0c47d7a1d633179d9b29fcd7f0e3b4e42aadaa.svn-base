﻿using ContinuityPatrol.Application.Features.CyberAirGap.Events.CyberResiliencyPagination;
using ContinuityPatrol.Application.UnitTests.Fixtures;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAirGap.Events;

    public class CyberResiliencyPaginatedEventTests : IClassFixture<CyberAirGapFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CyberAirGapFixture _cyberAir;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<CyberResiliencyPaginatedEventHandler>> _mockLogger;
    private readonly CyberResiliencyPaginatedEventHandler _handler;

    public CyberResiliencyPaginatedEventTests(CyberAirGapFixture cyberAir, UserActivityFixture userActivityFixture)
    {
        _cyberAir = cyberAir;
        _userActivityFixture = userActivityFixture;

        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<CyberResiliencyPaginatedEventHandler>>();

        // Setup mock behavior
        _mockLoggedInUserService.Setup(x => x.UserId).Returns("test-user-id");
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("test-login");

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync((Domain.Entities.UserActivity activity) =>
            {
                activity.Id = _userActivityFixture.UserActivities.Count + 1;
                _userActivityFixture.UserActivities.Add(activity);
                return activity;
            });

       
        _handler = new CyberResiliencyPaginatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_LogUserActivity_When_CyberResiliencyPaginatedEventReceived()
    {
        // Arrange
        var paginatedEvent = new CyberResiliencyPaginatedEvent();
        

        // Act
        await _handler.Handle(paginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
    [Fact]
    public async Task Handle_Should_LogUserActivity_When_CyberAirGapPaginatedEventReceived()
    {
        // Arrange
        var cyberAirGapPaginatedEvent = new CyberResiliencyPaginatedEvent();

        // Act
        await _handler.Handle(cyberAirGapPaginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_CreateUserActivity_With_CorrectProperties()
    {
        // Arrange
        var cyberAirGapPaginatedEvent = new CyberResiliencyPaginatedEvent();
        Domain.Entities.UserActivity capturedActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => capturedActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAirGapPaginatedEvent, CancellationToken.None);

        // Assert
        capturedActivity.ShouldNotBeNull();
        capturedActivity.ActivityType.ShouldBe("View");
        capturedActivity.Action.ShouldBe("View CyberResiliency");
        capturedActivity.Entity.ShouldContain("CyberResiliency");
    }

    [Fact]
    public async Task Handle_Should_HandleException_When_RepositoryThrows()
    {
        // Arrange
        var cyberAirGapPaginatedEvent = new CyberResiliencyPaginatedEvent();

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var exception = await Should.ThrowAsync<Exception>(async () =>
            await _handler.Handle(cyberAirGapPaginatedEvent, CancellationToken.None));

        exception.Message.ShouldBe("Database error");
    }
}
    

