using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowViewFixture : IDisposable
{
    public List<WorkflowView> WorkflowViewPaginationList { get; set; }
    public List<WorkflowView> WorkflowViewList { get; set; }
    public WorkflowView WorkflowViewDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowViewFixture()
    {
        var fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        fixture.Customize<WorkflowView>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.IsActive, true)
            .With(x => x.WorkflowName, () => $"Workflow_{Random.Shared.Next(1, 10)}")
            .With(x => x.ProfileId, () => Guid.NewGuid().ToString())
            .With(x => x.ProfileName, () => $"Profile_{Random.Shared.Next(1, 10)}")
            .With(x => x.InfraObjectId, () => $"INFRA_{Random.Shared.Next(1, 10):D3}")
            .With(x => x.InfraObjectName, () => $"Infrastructure_{Random.Shared.Next(1, 10)}")
            .With(x => x.WorkflowType, () => $"Type_{Random.Shared.Next(1, 5)}")
            .With(x => x.IsLock, () => Random.Shared.Next(0, 2) == 1)
            .With(x => x.IsPublish, () => Random.Shared.Next(0, 2) == 1)
            .With(x => x.IsVerify, () => Random.Shared.Next(0, 2) == 1)
            .With(x => x.IsDraft, () => Random.Shared.Next(0, 2) == 1)
            .With(x => x.Version, () => Random.Shared.Next(1, 10).ToString())
            .With(x => x.Properties, () => "{\"test\": \"value\", \"status\": \"active\"}")
            .With(x => x.IsRunning, () => Random.Shared.Next(0, 2) == 1)
            .With(x => x.ProgressBar, () => Random.Shared.Next(0, 100).ToString())
            .With(x => x.BusinessServiceName, () => $"BusinessService_{Random.Shared.Next(1, 5)}")
            .With(x => x.BusinessFunctionName, () => $"BusinessFunction_{Random.Shared.Next(1, 5)}")
            .With(x => x.CreatedDate, () => DateTime.Now.AddDays(-Random.Shared.Next(1, 30)))
            .With(x => x.CreatedBy, () => "TestUser")
            .With(x => x.CreatedByUserName, () => $"User_{Random.Shared.Next(1, 5)}")
            .With(x => x.LastModifiedBy, () => "TestUser")
            .With(x => x.LastModifiedDate, () => DateTime.Now)
            .With(x => x.CompanyId, () => Guid.NewGuid().ToString())
            .With(x => x.LastExecutionDate, () => DateTime.Now.AddDays(-Random.Shared.Next(1, 7)).ToString("yyyy-MM-dd HH:mm:ss")));

        WorkflowViewList = fixture.Create<List<WorkflowView>>();

        WorkflowViewPaginationList = fixture.CreateMany<WorkflowView>(20).ToList();

        // Setup pagination list with realistic data
        WorkflowViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowViewPaginationList.ForEach(x => x.IsActive = true);
        WorkflowViewPaginationList.ForEach(x => x.WorkflowName = $"Workflow_{Random.Shared.Next(1, 10)}");
        WorkflowViewPaginationList.ForEach(x => x.ProfileId = Guid.NewGuid().ToString());
        WorkflowViewPaginationList.ForEach(x => x.ProfileName = $"Profile_{Random.Shared.Next(1, 10)}");
        WorkflowViewPaginationList.ForEach(x => x.InfraObjectId = $"INFRA_{Random.Shared.Next(1, 10):D3}");
        WorkflowViewPaginationList.ForEach(x => x.InfraObjectName = $"Infrastructure_{Random.Shared.Next(1, 10)}");
        WorkflowViewPaginationList.ForEach(x => x.WorkflowType = $"Type_{Random.Shared.Next(1, 5)}");
        WorkflowViewPaginationList.ForEach(x => x.IsLock = Random.Shared.Next(0, 2) == 1);
        WorkflowViewPaginationList.ForEach(x => x.IsPublish = Random.Shared.Next(0, 2) == 1);
        WorkflowViewPaginationList.ForEach(x => x.IsVerify = Random.Shared.Next(0, 2) == 1);
        WorkflowViewPaginationList.ForEach(x => x.IsDraft = Random.Shared.Next(0, 2) == 1);
        WorkflowViewPaginationList.ForEach(x => x.Version = Random.Shared.Next(1, 10).ToString());
        WorkflowViewPaginationList.ForEach(x => x.Properties = "{\"test\": \"value\", \"status\": \"active\"}");
        WorkflowViewPaginationList.ForEach(x => x.IsRunning = Random.Shared.Next(0, 2) == 1);
        WorkflowViewPaginationList.ForEach(x => x.ProgressBar = Random.Shared.Next(0, 100).ToString());
        WorkflowViewPaginationList.ForEach(x => x.BusinessServiceName = $"BusinessService_{Random.Shared.Next(1, 5)}");
        WorkflowViewPaginationList.ForEach(x => x.BusinessFunctionName = $"BusinessFunction_{Random.Shared.Next(1, 5)}");
        WorkflowViewPaginationList.ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-Random.Shared.Next(1, 30)));
        WorkflowViewPaginationList.ForEach(x => x.CreatedBy = "TestUser");
        WorkflowViewPaginationList.ForEach(x => x.CreatedByUserName = $"User_{Random.Shared.Next(1, 5)}");
        WorkflowViewPaginationList.ForEach(x => x.LastModifiedBy = "TestUser");
        WorkflowViewPaginationList.ForEach(x => x.LastModifiedDate = DateTime.Now);
        WorkflowViewPaginationList.ForEach(x => x.CompanyId = Guid.NewGuid().ToString());
        WorkflowViewPaginationList.ForEach(x => x.LastExecutionDate = DateTime.Now.AddDays(-Random.Shared.Next(1, 7)).ToString("yyyy-MM-dd HH:mm:ss"));

        // Setup regular list with realistic data
        WorkflowViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowViewList.ForEach(x => x.IsActive = true);
        WorkflowViewList.ForEach(x => x.WorkflowName = $"Workflow_{Random.Shared.Next(1, 5)}");
        WorkflowViewList.ForEach(x => x.ProfileId = Guid.NewGuid().ToString());
        WorkflowViewList.ForEach(x => x.ProfileName = $"Profile_{Random.Shared.Next(1, 5)}");
        WorkflowViewList.ForEach(x => x.InfraObjectId = $"INFRA_{Random.Shared.Next(1, 5):D3}");
        WorkflowViewList.ForEach(x => x.InfraObjectName = $"Infrastructure_{Random.Shared.Next(1, 5)}");
        WorkflowViewList.ForEach(x => x.WorkflowType = $"Type_{Random.Shared.Next(1, 3)}");
        WorkflowViewList.ForEach(x => x.IsLock = Random.Shared.Next(0, 2) == 1);
        WorkflowViewList.ForEach(x => x.IsPublish = Random.Shared.Next(0, 2) == 1);
        WorkflowViewList.ForEach(x => x.IsVerify = Random.Shared.Next(0, 2) == 1);
        WorkflowViewList.ForEach(x => x.IsDraft = Random.Shared.Next(0, 2) == 1);
        WorkflowViewList.ForEach(x => x.Version = Random.Shared.Next(1, 5).ToString());
        WorkflowViewList.ForEach(x => x.Properties = "{\"test\": \"value\", \"status\": \"active\"}");
        WorkflowViewList.ForEach(x => x.IsRunning = Random.Shared.Next(0, 2) == 1);
        WorkflowViewList.ForEach(x => x.ProgressBar = Random.Shared.Next(0, 100).ToString());
        WorkflowViewList.ForEach(x => x.BusinessServiceName = $"BusinessService_{Random.Shared.Next(1, 3)}");
        WorkflowViewList.ForEach(x => x.BusinessFunctionName = $"BusinessFunction_{Random.Shared.Next(1, 3)}");
        WorkflowViewList.ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-Random.Shared.Next(1, 10)));
        WorkflowViewList.ForEach(x => x.CreatedBy = "TestUser");
        WorkflowViewList.ForEach(x => x.CreatedByUserName = $"User_{Random.Shared.Next(1, 3)}");
        WorkflowViewList.ForEach(x => x.LastModifiedBy = "TestUser");
        WorkflowViewList.ForEach(x => x.LastModifiedDate = DateTime.Now);
        WorkflowViewList.ForEach(x => x.CompanyId = Guid.NewGuid().ToString());
        WorkflowViewList.ForEach(x => x.LastExecutionDate = DateTime.Now.AddDays(-Random.Shared.Next(1, 3)).ToString("yyyy-MM-dd HH:mm:ss"));

        // Setup single DTO with realistic data
        WorkflowViewDto = fixture.Create<WorkflowView>();
        WorkflowViewDto.ReferenceId = Guid.NewGuid().ToString();
        WorkflowViewDto.IsActive = true;
        WorkflowViewDto.WorkflowName = "Test Workflow";
        WorkflowViewDto.ProfileId = Guid.NewGuid().ToString();
        WorkflowViewDto.ProfileName = "Test Profile";
        WorkflowViewDto.InfraObjectId = "INFRA_001";
        WorkflowViewDto.InfraObjectName = "Test Infrastructure";
        WorkflowViewDto.WorkflowType = "Test Type";
        WorkflowViewDto.IsLock = false;
        WorkflowViewDto.IsPublish = true;
        WorkflowViewDto.IsVerify = true;
        WorkflowViewDto.IsDraft = false;
        WorkflowViewDto.Version = "1";
        WorkflowViewDto.Properties = "{\"test\": \"value\", \"status\": \"active\"}";
        WorkflowViewDto.IsRunning = false;
        WorkflowViewDto.ProgressBar = "0";
        WorkflowViewDto.BusinessServiceName = "Test Business Service";
        WorkflowViewDto.BusinessFunctionName = "Test Business Function";
        WorkflowViewDto.CreatedDate = DateTime.Now.AddDays(-1);
        WorkflowViewDto.CreatedBy = "TestUser";
        WorkflowViewDto.CreatedByUserName = "Test User";
        WorkflowViewDto.LastModifiedBy = "TestUser";
        WorkflowViewDto.LastModifiedDate = DateTime.Now;
        WorkflowViewDto.CompanyId = Guid.NewGuid().ToString();
        WorkflowViewDto.LastExecutionDate = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss");
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
