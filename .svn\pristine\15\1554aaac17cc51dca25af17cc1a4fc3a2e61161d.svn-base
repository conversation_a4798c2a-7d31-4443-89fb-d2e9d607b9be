﻿using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;
using FluentAssertions;

namespace ContinuityPatrol.Application.UnitTests.Features.MongoDBMonitorLog.Queries
{
    public class GetMongoDBMonitorLogDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IMongoDbMonitorLogRepository> _repositoryMock;
        private readonly GetMongoDBMonitorLogDetailQueryHandler _handler;

        public GetMongoDBMonitorLogDetailQueryHandlerTests()
        {
            _mapperMock = new Mock<IMapper>();
            _repositoryMock = new Mock<IMongoDbMonitorLogRepository>();
            _handler = new GetMongoDBMonitorLogDetailQueryHandler(_repositoryMock.Object, _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_Should_Return_DetailVm_When_Record_Exists()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG100" };

            var entity = new Domain.Entities.MongoDBMonitorLog
            {
                ReferenceId = "LOG100",
                InfraObjectId = "INFRA001"
            };

            var viewModel = new MongoDBMonitorLogDetailVm
            {
                Id = "LOG100",
                InfraObjectId = "INFRA001"
            };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG100")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MongoDBMonitorLogDetailVm>(entity)).Returns(viewModel);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be("LOG100");
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFound_When_Entity_Is_Null()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG404" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG404")).ReturnsAsync((Domain.Entities.MongoDBMonitorLog)null!);

            // Act
            Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

            // Assert
            await act.Should().ThrowAsync<NotFoundException>()
                .WithMessage("*MSSQLAlwaysOnMonitorLogs*LOG404*");
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFound_When_Mapped_Result_Is_Null()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG500" };

            var entity = new Domain.Entities.MongoDBMonitorLog
            {
                ReferenceId = "LOG500",
                InfraObjectId = "INFRA999"
            };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG500")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MongoDBMonitorLogDetailVm>(entity)).Returns<MongoDBMonitorLogDetailVm>(null!);

            // Act
            Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

            // Assert
            await act.Should().ThrowAsync<NotFoundException>()
                .WithMessage("*MSSQLAlwaysOnMonitorLogs*LOG500*");
        }

        [Fact]
        public async Task Handle_Should_Call_Repository_Once_With_Correct_Id()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG123" };
            var entity = new Domain.Entities.MongoDBMonitorLog { ReferenceId = "LOG123" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG123")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MongoDBMonitorLogDetailVm>(entity))
                .Returns(new MongoDBMonitorLogDetailVm { Id = "LOG123" });

            // Act
            await _handler.Handle(request, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(r => r.GetByReferenceIdAsync("LOG123"), Times.Once);
        }

        
        [Fact]
        public async Task Handle_Should_Throw_When_Id_Is_Empty()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = string.Empty };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(It.IsAny<string>()))
                .ThrowsAsync(new ArgumentException("Reference ID is required"));

            // Act
            Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

            // Assert
            await act.Should().ThrowAsync<ArgumentException>()
                .WithMessage("Reference ID is required");
        }

        [Fact]
        public async Task Handle_Should_Return_Correct_InfraObjectId_And_WorkflowName()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG300" };
            var entity = new Domain.Entities.MongoDBMonitorLog
            {
                ReferenceId = "LOG300",
                InfraObjectId = "INFRA900",
                WorkflowName = "RestoreTask"
            };

            var viewModel = new MongoDBMonitorLogDetailVm
            {
                Id = "LOG300",
                InfraObjectId = "INFRA900",
                WorkflowName = "RestoreTask"
            };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG300")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MongoDBMonitorLogDetailVm>(entity)).Returns(viewModel);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.InfraObjectId.Should().Be("INFRA900");
            result.WorkflowName.Should().Be("RestoreTask");
        }

        [Fact]
        public async Task Handle_Should_Return_ViewModel_With_Empty_Properties_When_Missing()
        {
            // Arrange
            var request = new GetMongoDBMonitorLogDetailQuery { Id = "LOG500" };
            var entity = new Domain.Entities.MongoDBMonitorLog
            {
                ReferenceId = "LOG500",
                Properties = null
            };

            var viewModel = new MongoDBMonitorLogDetailVm
            {
                Id = "LOG500",
                Properties = null
            };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync("LOG500")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MongoDBMonitorLogDetailVm>(entity)).Returns(viewModel);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Properties.Should().BeNull();
        }

        [Fact]
        public void MongoDBMonitorLogDetailVm_Properties_Should_Set_And_Get_Correctly()
        {
            // Arrange & Act - Test all property setters and getters
            var vm = new MongoDBMonitorLogDetailVm
            {
                Id = "TestId123",
                Type = "TestType",
                InfraObjectId = "TestInfraObjId",
                InfraObjectName = "TestInfraObjName",
                WorkflowId = "TestWorkflowId",
                WorkflowName = "TestWorkflowName",
                Properties = "TestProperties",
                ConfiguredRPO = "TestRPO",
                DataLagValue = "TestDataLag"
            };

            // Assert - Test all property getters
            vm.Id.Should().Be("TestId123");
            vm.Type.Should().Be("TestType");
            vm.InfraObjectId.Should().Be("TestInfraObjId");
            vm.InfraObjectName.Should().Be("TestInfraObjName");
            vm.WorkflowId.Should().Be("TestWorkflowId");
            vm.WorkflowName.Should().Be("TestWorkflowName");
            vm.Properties.Should().Be("TestProperties");
            vm.ConfiguredRPO.Should().Be("TestRPO");
            vm.DataLagValue.Should().Be("TestDataLag");
        }

    }
}
