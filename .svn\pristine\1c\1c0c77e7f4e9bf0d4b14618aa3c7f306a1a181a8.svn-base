using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetDetail;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetList;

using ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class IncidentManagementSummaryControllerTests : IClassFixture<IncidentManagementSummaryFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly IncidentManagementSummarysController _controller;
    private readonly IncidentManagementSummaryFixture _fixture;

    public IncidentManagementSummaryControllerTests(IncidentManagementSummaryFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<IncidentManagementSummarysController>();
        _controller = testBuilder.CreateController(
            _ => new IncidentManagementSummarysController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetIncidentManagementSummarys_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.IncidentManagementSummaryListVm);

        var result = await _controller.GetIncidentManagementSummarys();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var incidentManagementSummaryList = Assert.IsAssignableFrom<List<IncidentManagementSummaryListVm>>(okResult.Value);
        Assert.Equal(_fixture.IncidentManagementSummaryListVm.Count, incidentManagementSummaryList.Count);
    }

    [Fact]
    public async Task GetIncidentManagementSummarys_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IncidentManagementSummaryListVm>());

        var result = await _controller.GetIncidentManagementSummarys();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var incidentManagementSummaryList = Assert.IsAssignableFrom<List<IncidentManagementSummaryListVm>>(okResult.Value);
        Assert.Empty(incidentManagementSummaryList);
    }

    [Fact]
    public async Task GetIncidentManagementSummaryById_Should_Return_Detail()
    {
        var incidentManagementSummaryId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetIncidentManagementSummaryDetailQuery>(q => q.Id == incidentManagementSummaryId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.IncidentManagementSummaryDetailVm);

        var result = await _controller.GetIncidentManagementSummaryById(incidentManagementSummaryId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var incidentManagementSummaryDetail = Assert.IsAssignableFrom<IncidentManagementSummaryDetailVm>(okResult.Value);
        Assert.NotNull(incidentManagementSummaryDetail);
        Assert.Equal(_fixture.IncidentManagementSummaryDetailVm.Id, incidentManagementSummaryDetail.Id);
    }

    [Fact]
    public async Task GetIncidentManagementSummaryById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetIncidentManagementSummaryById(null!));
    }

    [Fact]
    public async Task GetIncidentManagementSummaryById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetIncidentManagementSummaryById(""));
    }

    [Fact]
    public async Task GetIncidentManagementSummaryById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetIncidentManagementSummaryById("invalid-guid"));
    }



    [Fact]
    public async Task CreateIncidentManagementSummary_Should_Return_Success()
    {
        var response = new CreateIncidentManagementSummaryResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateIncidentManagementSummaryCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateIncidentManagementSummary(_fixture.CreateIncidentManagementSummaryCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateIncidentManagementSummaryResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateIncidentManagementSummary_Should_Return_Success()
    {
        var response = new UpdateIncidentManagementSummaryResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateIncidentManagementSummaryCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateIncidentManagementSummary(_fixture.UpdateIncidentManagementSummaryCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateIncidentManagementSummaryResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteIncidentManagementSummary_Should_Call_Mediator()
    {
        var incidentManagementSummaryId = Guid.NewGuid().ToString();
        var response = new DeleteIncidentManagementSummaryResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteIncidentManagementSummaryCommand>(c => c.Id == incidentManagementSummaryId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteIncidentManagementSummary(incidentManagementSummaryId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteIncidentManagementSummaryResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteIncidentManagementSummary_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteIncidentManagementSummary(null!));
    }

    [Fact]
    public async Task DeleteIncidentManagementSummary_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteIncidentManagementSummary(""));
    }

    [Fact]
    public async Task GetIncidentManagementSummarys_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.IncidentManagementSummaryListVm);

        await _controller.GetIncidentManagementSummarys();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetIncidentManagementSummarys_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetIncidentManagementSummarys());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetIncidentManagementSummaries_VerifiesQueryType()
    {
        GetIncidentManagementSummaryListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetIncidentManagementSummaryListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<IncidentManagementSummaryListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetIncidentManagementSummaryListQuery;
            })
            .ReturnsAsync(_fixture.IncidentManagementSummaryListVm);

        await _controller.GetIncidentManagementSummarys();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetIncidentManagementSummaryListQuery>(capturedQuery);
    }
}
