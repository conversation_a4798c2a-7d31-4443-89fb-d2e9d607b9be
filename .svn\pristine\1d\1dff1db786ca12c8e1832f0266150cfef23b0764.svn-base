using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class MenuBuilderFixture
{
    public List<MenuBuilderListVm> MenuBuilderListVm { get; }
    public MenuBuilderDetailVm MenuBuilderDetailVm { get; }
    public PaginatedResult<MenuBuilderListVm> PaginatedMenuBuilderListVm { get; }
    public CreateMenuBuilderCommand CreateMenuBuilderCommand { get; }
    public UpdateMenuBuilderCommand UpdateMenuBuilderCommand { get; }
    public GetMenuBuilderPaginatedListQuery GetMenuBuilderPaginatedListQuery { get; }

    public MenuBuilderFixture()
    {
        var fixture = new Fixture();

        MenuBuilderListVm = fixture.Create<List<MenuBuilderListVm>>();
        MenuBuilderDetailVm = fixture.Create<MenuBuilderDetailVm>();
        PaginatedMenuBuilderListVm = fixture.Create<PaginatedResult<MenuBuilderListVm>>();
        CreateMenuBuilderCommand = fixture.Create<CreateMenuBuilderCommand>();
        UpdateMenuBuilderCommand = fixture.Create<UpdateMenuBuilderCommand>();
        GetMenuBuilderPaginatedListQuery = fixture.Create<GetMenuBuilderPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
