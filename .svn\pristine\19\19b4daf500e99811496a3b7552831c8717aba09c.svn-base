﻿using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Queries;
public class GetBackUpPaginatedListQueryHandlerTests
{
    private readonly Mock<IBackUpRepository> _mockBackUpRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBackUpPaginatedListQueryHandler _handler;

    public GetBackUpPaginatedListQueryHandlerTests()
    {
        _mockBackUpRepository = new Mock<IBackUpRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetBackUpPaginatedListQueryHandler(_mockMapper.Object, _mockBackUpRepository.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedPaginatedResult_WhenQueryIsValid()
    {
        // Arrange
        var query = new GetBackUpPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "backup",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var dummyPaginatedResult = new PaginatedResult<ContinuityPatrol.Domain.Entities.BackUp>
        {
            Data = new List<ContinuityPatrol.Domain.Entities.BackUp>
            {
                new() { ReferenceId = "bk-001" }
            },
            TotalCount = 1,
            PageSize = 10,
            CurrentPage = 1
        };

        var mappedResult = new PaginatedResult<BackUpListVm>
        {
            Data = new List<BackUpListVm>
            {
                new() { Id = "bk-001", HostName = "DailyBackup" }
            },
            TotalCount = 1,
            PageSize = 10,
            CurrentPage = 1
        };

        _mockBackUpRepository.Setup(repo =>
                repo.PaginatedListAllAsync(query.PageNumber, query.PageSize,
                    It.IsAny<BackUpFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(dummyPaginatedResult);

        _mockMapper.Setup(m => m.Map<PaginatedResult<BackUpListVm>>(dummyPaginatedResult))
            .Returns(mappedResult);

        // Act
        var result = await _handler.Handle(query, default);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("bk-001", result.Data[0].Id);
        Assert.Equal("DailyBackup", result.Data[0].HostName);
        Assert.Equal(1, result.TotalCount);

        _mockBackUpRepository.Verify(repo =>
            repo.PaginatedListAllAsync(query.PageNumber, query.PageSize,
                It.IsAny<BackUpFilterSpecification>(), query.SortColumn, query.SortOrder), Times.Once);

        _mockMapper.Verify(m => m.Map<PaginatedResult<BackUpListVm>>(dummyPaginatedResult), Times.Once);
    }

   
}
