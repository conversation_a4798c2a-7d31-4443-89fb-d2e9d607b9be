﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceEvaluationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class BusinessServiceEvaluationService : IBusinessServiceEvaluationService
{
    private readonly IBaseClient _client;

    public BusinessServiceEvaluationService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<BaseResponse> CreateAsync(CreateBusinessServiceEvaluationCommand createBusinessServiceEvaluationCommand)
    {
        var request = new RestRequest("api/v6/businessserviceevaluation", Method.Post);

        request.AddJsonBody(createBusinessServiceEvaluationCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBusinessServiceEvaluationCommand updateBusinessServiceEvaluationCommand)
    {
        var request = new RestRequest("api/v6/businessserviceevaluation", Method.Put);

        request.AddJsonBody(updateBusinessServiceEvaluationCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/businessserviceevaluation/{id}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<List<BusinessServiceEvaluationListVm>> GetBusinessServiceEvaluationList()
    {
        var request = new RestRequest("api/v6/businessserviceevaluation");

        return await _client.GetFromCache<List<BusinessServiceEvaluationListVm>>(request, "GetBusinessServiceEvaluationList");
    }

    public async Task<BusinessServiceEvaluationDetailVm> GetReferenceById(string id)
    {
        var request = new RestRequest($"api/v6/businessserviceevaluation/{id}");

        return await _client.Get<BusinessServiceEvaluationDetailVm>(request);
    }
}