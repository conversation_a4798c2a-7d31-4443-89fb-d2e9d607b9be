﻿
let btnDisable = false, copyOptionsChecked = false;
let copyTextCheckBox = ['#MoveCheckbox18']
let copyTextBoxes = ['#txtLEV']

const roboCopyFunURL = {
    roboCopyExistUrl: "Configuration/RoboCopyOptions/IsRoboCopyNameExist",
    pagination: "/Configuration/RoboCopyOptions/GetPagination"
}

const roboCopyPermission = {
    createPermission : $("#configurationroboCreate").data("create-permission").toLowerCase(),
    deletePermission : $("#configurationroboDelete").data("delete-permission").toLowerCase()
}

if (roboCopyPermission.createPermission == 'false') {
    $("#roboCopyCreate").removeClass('#roboCopyCreate').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', roboCopyPermission.createPermission == 'false');
}


let copyOptionCheckboxes = [
    $('#S-Checkbox1'), $('#MOV-Checkbox2'), $('#MIR-Checkbox3'), $('#FAT-Checkbox4'), $('#LEV-Checkbox5'), $('#E-Checkbox6'),
    $('#A-Checkbox7'), $('#ZB-Checkbox8'), $('#FFT-Checkbox9'), $('#M-Checkbox10'), $('#B-Checkbox11'), $('#CreateCheckbox12'),
    $('#Z-Checkbox13'), $('#CopyAllCheckbox14'), $('#PurgeCheckbox15'), $('#SEC-Checkbox16'), $('#NoCopyCheckbox17'),
];

let parentChildCheckboxes = [
    {
        parent: $('#CopyOptionCheckbox19'),
        children: [
            $('#copychild1'), $('#copychild2'), $('#copychild3'), $('#copychild4'), $('#copychild5'),
        ]
    },
    {
        parent: $('#CopyOptionCheckbox20'),
        children: [
            $('#copychild6'), $('#copychild7'), $('#copychild8'), $('#copychild9'), $('#copychild10'),
        ]
    },
    {
        parent: $('#CopyOptionCheckbox21'),
        children: [
            $('#copychild11'), $('#copychild12'), $('#copychild13'), $('#copychild14'), $('#copychild15'), $('#copychild16')
        ]
    }
];

let retryOptionTextboxCheckboxes = [
    $('#RetryOptionCheckbox1'), $('#RetryOptionCheckbox2')
];
let retryOptionTextboxes = [
    $('#txtRetry1'), $('#txtRetry2')
];

let filterCheckboxes = [
    $('#XJ-Checkbox1'), $('#XX-Checkbox2'), $('#IA-Checkbox3'), $('#IT-Checkbox4'), $('#XO-Checkbox5'),
    $('#IS-Checkbox6'), $('#XN-Checkbox7'), $('#XC-Checkbox8')

];
let filterTextboxCheckbox = [$('#MAX-Checkbox11'), $('#MIN-Checkbox12'), $('#XD-Checkbox13'), $('#XF-Checkbox14')];

let filterTextboxes = [
    $('#txtMax1'), $('#txtMin2'), $('#txtXD3'), $('#txtXF4')
];
let parentChildFilterCheckboxes = [
    {
        parent: $('#IA-Checkbox'),
        children: [
            $('#IA-CheckboX1'), $('#IA-CheckboX2'), $('#IA-CheckboX3'), $('#IA-CheckboX4'), $('#IA-CheckboX5'),
            $('#IA-CheckboX6'), $('#IA-CheckboX7'), $('#IA-CheckboX8'), $('#IA-CheckboX9'), $('#IA-CheckboX10'),

        ]
    },
    {
        parent: $('#XA-Checkbox'),
        children: [
            $('#XA-CheckboX1'), $('#XA-CheckboX2'), $('#XA-CheckboX3'), $('#XA-CheckboX4'), $('#XA-CheckboX5'),
            $('#XA-CheckboX6'), $('#XA-CheckboX7'), $('#XA-CheckboX8'), $('#XA-CheckboX9'), $('#XA-CheckboX10'),
        ]
    },
];
let advancedFilterOptionCheckboxes = [
    $('#RecyclebinCheckbox1'), $('#SystemVoulumeCheckbox2'), $('#RecyclerCheckbox3')
];

let advancedFilterTextboxCheckbox = [$('#MinAgeCheckbox4'), $('#MinLadCheckbox5'), $('#MaxAgeCheckbox6'), $('#MaxLadCheckbox7')
]
let advancedFilterTextBoxes = [
    $('#txtMinAge1'), $('#txtMinLad2'), $('#txtMaxAge3'), $('#txtMaxlad4')
];
const exceptThisSymbols = ["e", "E", "+", "-", "."];
$('#txtRetry1, #txtRetry2').on('keypress keyup paste', async function (event) {
    const value = $(this).val();
    if (!/[+0-9-]/.test(event.key) || exceptThisSymbols.includes(event.key) || event.type === 'paste') {
        event.preventDefault();
    }
    
});
//Checkbox for Copy Option
function areAnyCheckboxesChecked(event) {
    let value = event.target.value;
    let isChecked = event.target.checked;
    let childId = $(event.target).data('childid');

    if (value && isChecked) {
        $('#' + childId).show()
    }
    else {
        $('#' + childId).hide();
        $('#' + childId + ' input[type="checkbox"]').prop('checked', false);
    }
    const checkboxes = [
        '#S-Checkbox1', '#MOV-Checkbox2', '#MIR-Checkbox3', '#FAT-Checkbox4',
        '#LEV-Checkbox5', '#E-Checkbox6', '#ZB-Checkbox8', '#FFT-Checkbox9',
        '#M-Checkbox10', '#B-Checkbox11', '#CreateCheckbox12', '#Z-Checkbox13',
        '#CopyAllCheckbox14', '#PurgeCheckbox15', '#SEC-Checkbox16',
        '#NoCopyCheckbox17', '#MoveCheckbox18', '#A-Checkbox7',
        '#CopyOptionCheckbox19', '#CopyOptionCheckbox20', '#CopyOptionCheckbox21'
    ];

 

    for (const checkbox of checkboxes) {
        if ($(checkbox).is(':checked')) {
            //copyOptionsChecked = true;
            break;
        }
    }

    //if (copyOptionsChecked) {
    //    $("#CopyOptions-error").text("").removeClass('field-validation-error');
    //} else {
    //    $("#CopyOptions-error").text("Please select one checkbox").addClass('field-validation-error');
    //}

    return copyOptionsChecked;
}

//Enable Textbox
function enableTextBox(checkbox, textBox, textBoxError) {
    if ($(checkbox).is(':checked')) {
        if ($(textBox).val()) {
            $(textBox).removeAttr('disabled');
        } else {
            $(textBox).removeAttr('disabled').val('');
        }
    }
    else {
        $(textBox).prop('disabled', 'disabled').val('');
        $(textBoxError).text("").removeClass('field-validation-error');
    }
}
$(function () {
    //Pagination
    let selectedValues = [];
    let dataTable = $('#tblRoboCopy').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": roboCopyFunURL.pagination,
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {

                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return '<span title="' + data + '">' + data + '</span>';

                    }
                },
                {
                    "data": "replicationType", "name": "ReplicationType", "autoWidth": true,
                    "render": function (data, type, row) {

                        return data;
                    }
                },
                {
                    "data": "properties", "name": "copyOptions", "autoWidth": true,
                    "render": function (data, type, row) {

                        let properties = JSON?.parse(row?.properties);                     
                        let copy = properties?.copyOptions ? properties?.copyOptions : "NA";
                        let format = copy.replace(/\/([^%]*)%\d+/g, '/$1').replace(/\^/g, ' ').trim()
                        return '<span title="' + format + '">' + format + '</span>';

                        //return properties?.copyOptions ? properties?.copyOptions : "NA";
                    }
                },               
                {
                    "render": function (data, type, row) {
                        if (roboCopyPermission.createPermission === 'true' && roboCopyPermission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-robocopy='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-robocopy-id="${row.id}" data-robocopy-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (roboCopyPermission.createPermission === 'true' && roboCopyPermission.deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-robocopy='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (roboCopyPermission.createPermission === 'false' && roboCopyPermission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-robocopy-id="${row.id}" data-robocopy-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    //Search Filter
    $('#search-inp').on('keypress input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#name");
        const ReplicationTypeCheckbox = $("#ReplicationType");
        const inputValue = $('#search-inp').val();

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (ReplicationTypeCheckbox.is(':checked')) {
            selectedValues.push(ReplicationTypeCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {
            if (e.target.value && json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $('#txtName').on('keyup', commonDebounce(async function () {
        const value = $(this).val().replace(/\s{2,}/g, ' ');
        $(this).val(value);
        const nameId = $('#txtNameId').val();
        await validateName(value, nameId, IsNameExist);
    }, 400));


    $('#selectReplicationtype').on('change', function () {
        const value = $(this).val();
        validateDropDown(value, 'Select replication type ', 'rcpReplicationTypeError');
    })

    $('input[type="checkbox"]').on('change', areAnyCheckboxesChecked);

    function checkboxShowHide(event) {
        let value = event.target.value;
        let isChecked = event.target.checked;
        let childId = $(event.target).data('childid');
        if (value && isChecked) {
            $('#' + childId).show()
        }
        else {
            $('#' + childId).hide();
            $('#' + childId + ' input[type="checkbox"]').prop('checked', false);
        }
        const checkboxes = [$('#XJ-Checkbox1'), $('#XX-Checkbox2'), $('#IA-Checkbox3'), $('#IT-Checkbox4'), $('#XO-Checkbox5'),
        $('#IS-Checkbox6'), $('#XN-Checkbox7'), $('#XC-Checkbox8'), $('#IA-Checkbox'), $('#XA-Checkbox')]
        let filterOptionsChecked = false;

        for (const checkbox of checkboxes) {
            if ($(checkbox).is(':checked')) {
                //copyOptionsChecked = true;
                break;
            }
        }
        return filterOptionsChecked;
    }
    const exceptThisSymbol = ["e", "E", "+", "-", "."];
    $('#txtMax1, #txtMin2, #txtMinLad2, #txtMaxAge3, #txtMaxlad4, #txtMinAge1').on('keyup keypress paste', function (event) {
        const value = $(this).val();
        console.log(value, 'max');
        if (exceptThisSymbol.includes(event.key) || event.type === 'paste') {
            event.preventDefault();
        }
    })
    $('input[type="checkbox"]').on('change', checkboxShowHide);

    function textBoxChange(errorID) {
        $(errorID).text("").removeClass('field-validation-error');
    }
    //Save
    $(document).on('click', '#SaveFunction', async function (event) {
        let form = $('#CreateForm')
        let name = $('#txtName').val();
        let nameId = $('#txtNameId').val();
        let replicationType = $('#selectReplicationtype').val();
        let isName = await validateName(name, nameId, IsNameExist);
        let isrepType = await validateDropDown(replicationType, 'Select replication type', 'rcpReplicationTypeError');
        let props = {};
        //const copyOptionsChecked = areAnyCheckboxesChecked(event);
        let sanitizeArray = ['txtName', 'txtNameId', 'textWebAddress', 'selectReplicationtype']
        sanitizeContainer(sanitizeArray)
        setTimeout(() => {
            if (isName && isrepType && !btnDisable) {
                btnDisable = true;
                //CopyOption
                let selectedValuesCopyOptions = [];
                copyOptionCheckboxes.forEach(function (checkbox, index) {
                    if (checkbox.is(':checked')) {
                        selectedValuesCopyOptions.push(`${checkbox.val()}%${index}`);
                    }
                });
                copyTextCheckBox.forEach(function (selector, index) {
                    let checkbox = $(selector);
                    if (checkbox.prop('checked')) {

                        let checkboxValue = checkbox.val();
                        let textValue = $(copyTextBoxes[index]).val();
                        let option = `${checkboxValue}:${textValue}`;
                        selectedValuesCopyOptions.push(option);
                    }
                });
                if (selectedValuesCopyOptions.length > 0) {
                    selectedValuesCopyOptions[selectedValuesCopyOptions.length - 1] += '^';
                }
                parentChildCheckboxes.forEach(function (group) {
                    if (group.parent.is(':checked')) {
                        let parentValue = group.parent.val();

                        let childValues = [];
                        group.children.forEach(function (child) {
                            if (child.is(':checked')) {
                                let value = `${parentValue}:${child.val()}`;
                                childValues.push(value);
                            }
                        });
                        if (childValues.length > 0) {
                            childValues[childValues.length - 1] += '^';
                        }

                        selectedValuesCopyOptions.push(...childValues);
                    }
                });

                let concatenatedValues = selectedValuesCopyOptions?.join(',');

                props.copyOptions = concatenatedValues.replace('^,', '^');

                //RetryOptions
                let retryOptionss = [];
                retryOptionTextboxCheckboxes.forEach(function (checkbox, index) {
                    if (checkbox.prop('checked')) {
                        let checkboxValue = checkbox.val();
                        let textBoxValue = retryOptionTextboxes[index].val();
                        let option = `${checkboxValue}:${textBoxValue}`;

                        retryOptionss.push(option);
                    }
                });
                let concatenatedretryValues = retryOptionss.join(',');
                props.retryOptions = concatenatedretryValues;

                //FilterOption
                let selectedValuesFilterOptions = [];
                filterCheckboxes.forEach(function (checkbox, index) {
                    if (checkbox.is(':checked')) {
                        selectedValuesFilterOptions.push(`${checkbox.val()}${index}`);
                    }
                });
                if (selectedValuesFilterOptions.length > 0) {
                    selectedValuesFilterOptions[selectedValuesFilterOptions.length - 1] += '#';
                }
                parentChildFilterCheckboxes.forEach(function (group) {
                    if (group.parent.is(':checked')) {
                        let parentValue = group.parent.val();

                        let childValues = [];
                        group.children.forEach(function (child, index) {
                            if (child.is(':checked')) {
                                let value = `${parentValue}:${child.val()}${index}`;
                                childValues.push(value);
                            }
                        });
                        if (childValues.length > 0) {
                            childValues[childValues.length - 1] += '#';
                        }

                        selectedValuesFilterOptions.push(...childValues);
                    }
                });
                if (selectedValuesFilterOptions.length > 0 && !selectedValuesFilterOptions[selectedValuesFilterOptions.length - 1].endsWith('#')) {
                    selectedValuesFilterOptions[selectedValuesFilterOptions.length - 1] += '#';
                }
                filterTextboxCheckbox.forEach(function (checkbox, index) {
                    if (checkbox.is(':checked')) {
                        let checkboxValue = checkbox.val();
                        let textBoxValue = filterTextboxes[index].val();
                        selectedValuesFilterOptions.push(`${checkboxValue}:${textBoxValue}`);
                    }
                });

                if (selectedValuesFilterOptions.length > 0 && !selectedValuesFilterOptions[selectedValuesFilterOptions.length - 1].endsWith('#')) {
                    selectedValuesFilterOptions[selectedValuesFilterOptions.length - 1] += '#';
                }
                let concatenatedValues1 = selectedValuesFilterOptions.join(',');
                props.filterOptions = concatenatedValues1.replace('#,', '#');

                //AdvancedFilterOption
                let selectedValuesAdvancedOptions = [];
                advancedFilterOptionCheckboxes.forEach(function (checkbox, index) {
                    if (checkbox.is(':checked')) {
                        selectedValuesAdvancedOptions.push(`${checkbox.val()}${index}`);
                    }
                });
                if (selectedValuesAdvancedOptions.length > 0) {
                    selectedValuesAdvancedOptions[selectedValuesAdvancedOptions.length - 1] += '#';
                }

                advancedFilterTextboxCheckbox.forEach(function (checkbox, index) {
                    if (checkbox.is(':checked')) {
                        let checkboxValue = checkbox.val();
                        let textBoxValue = advancedFilterTextBoxes[index].val();
                        selectedValuesAdvancedOptions.push(`${checkboxValue}:${textBoxValue}`);
                    }
                });
                if (selectedValuesAdvancedOptions.length > 0 && !selectedValuesAdvancedOptions[selectedValuesAdvancedOptions.length - 1].endsWith('#')) {
                    selectedValuesAdvancedOptions[selectedValuesAdvancedOptions.length - 1] += '#';
                }
                let concatenatedValues2 = selectedValuesAdvancedOptions.join(',');
                props.advancedFilterOptions = concatenatedValues2.replace('#,', '#');

                //Properties
                let stringifyProps = JSON.stringify(props)
                $('#roboCopyProperties').val(stringifyProps);
                // btnCrudDiasable('SaveFunction');
                form.trigger('submit');

            }
        }, 200)
    })

    //Edit
    $('#tblRoboCopy').on('click', '.edit-button', function () {
        let roboCopydata = $(this).data('robocopy');
        roboCopyEdit(roboCopydata);
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
    })
    //Delete
    $('#tblRoboCopy').on('click', '.delete-button', function () {
        let name = $(this).data("robocopy-name");
        let id = $(this).data("robocopy-id");
        $("#deleteData").text(name);
        $("#textDeleteId").val(id);
    })

    //Update
    function roboCopyEdit(roboCopydata) {

        clearPreviousData()
        clearErrorMessage()
        $('#txtName').val(roboCopydata.name);
        $('#txtNameId').val(roboCopydata.id);
        $('#selectReplicationtype').val(roboCopydata.replicationType).trigger('change');;
        let properties = JSON.parse(roboCopydata?.properties);

        //CopyOption

        let copyoptiondata = properties?.copyOptions ? properties?.copyOptions.split(',') : [];
        copyoptiondata.forEach(function (copyOption) {
            if (copyOption) {
                let parts = copyOption.split('^');
                parts.forEach(part => {
                    if (part.includes('%')) {
                        let [checkboxValue] = part.split('%');
                        copyOptionCheckboxes.forEach(function (checkbox) {
                            if (checkbox.val() === checkboxValue) {
                                checkbox.prop("checked", true);
                            }
                        });
                    }
                });

                parts.forEach(part => {
                    if (part.includes(':')) {
                        let [parentValue, childValue] = part.split(':');
                        childValue = childValue.replace('^', '');
                        parentChildCheckboxes.forEach(function (group) {
                            if (group.parent.val() === parentValue) {
                                group.parent.prop("checked", true);
                                group.children.forEach(function (childCheckbox) {
                                    if (childCheckbox.val() === childValue) {
                                        childCheckbox.prop("checked", true);
                                        $(`#${childCheckbox.attr('id')}`).closest('table').show();
                                    }
                                });
                            }
                        });
                    }
                });

                parts.forEach(part => {
                    if (part.includes(':')) {
                        let [checkboxValue, textValue] = part.split(':');
                        textValue = textValue.replace('^', '');

                        copyTextCheckBox.forEach(function (selector, index) {
                            let checkbox = $(selector);
                            if (checkbox.val() === checkboxValue) {
                                checkbox.prop("checked", true);
                                $(copyTextBoxes[index]).val(textValue).prop('disabled', false);
                            }
                        });
                    }
                });
            }
        });

        //RetryOption
        let retrydata = properties.retryOptions ? properties.retryOptions.split(',') : []
        retryOptionTextboxCheckboxes.forEach(function (checkbox, index) {
            let checkboxValue = checkbox.val();

            let filter = retrydata.find(option => option.startsWith(checkboxValue))
            if (filter) {
                checkbox.prop('checked', true);
                let textBoxValue = filter.split(':')[1].replace('^', '');
                retryOptionTextboxes[index].val(textBoxValue).prop('disabled', false);
            } else {
                checkbox.prop('checked', false);
                retryOptionTextboxes[index].val('');
            }
        })
        //FilterOption
        let filterdata = properties.filterOptions ? properties.filterOptions.split(',') : []
        filterdata.forEach(function (filterOption) {
            if (filterOption) {
                let parts = filterOption.split('#');

                parts.forEach(part => {
                    let checkboxValue = part.replace(/\d/g, '').replace(/#[^#]+$/, '').trim();
                    filterCheckboxes.forEach(function (checkbox) {
                        if (checkbox.val() === checkboxValue) {
                            checkbox.prop("checked", true);
                        }
                    });
                });

                parts.forEach(part => {
                    if (part.includes(':')) {

                        let [parentValue, childValue] = part.split(':');
                        childValue = childValue.replace('#', '');
                        parentChildFilterCheckboxes.forEach(function (group) {

                            if (group.parent.val() === parentValue) {
                                group.parent.prop("checked", true);
                                group.children.forEach(function (childCheckbox) {
                                    let value = childValue.replace(/[\d#]/g, '');
                                    if (childCheckbox.val() === value) {
                                        childCheckbox.prop("checked", true);
                                        $(`#${childCheckbox.attr('id')}`).closest('table').show();
                                    }
                                });
                            }
                        });
                    }
                });

                parts.forEach(part => {
                    if (part.includes(':')) {
                        let [checkboxValue, textValue] = part.split(':');
                        textValue = textValue.replace('#', '');

                        filterTextboxCheckbox.forEach(function (selector, index) {
                            let checkbox = $(selector);
                            if (checkbox.val() === checkboxValue) {
                                checkbox.prop("checked", true);
                                $(filterTextboxes[index]).val(textValue).prop('disabled', false);
                            }
                        });
                    }
                });
            }

        })
        //AdvanceFilterOption
        let advancedfilterdata = properties.advancedFilterOptions ? properties.advancedFilterOptions.split(',') : []
        advancedfilterdata.forEach(function (advanceOption) {

            if (advanceOption) {
                let option = advanceOption.split('#')
                option.forEach(part => {
                    if (part.includes(':')) {
                        let [checkboxValue, textValue] = part.split(':');
                        textValue = textValue.replace('#', '');

                        advancedFilterTextboxCheckbox.forEach(function (selector, index) {
                            let checkbox = $(selector);
                            if (checkbox.val() === checkboxValue) {

                                checkbox.prop("checked", true);
                                $(advancedFilterTextBoxes[index]).val(textValue).prop('disabled', false);
                            }
                        });
                    }
                });
                option.forEach(part => {

                    let checkboxValue = part.replace(/\d/g, '').replace(/#[^#]+$/, '').trim();
                    advancedFilterOptionCheckboxes.forEach(function (checkbox) {

                        if (checkbox.val() === checkboxValue) {
                            checkbox.prop("checked", true);
                        }
                    });
                })
            }

        })

    }
    function clearErrorMessage() {
        const errorElements = ['#rcpNameError', '#rcpReplicationTypeError', '#CopyOptions-error'];
        clearInputFields('CreateForm', errorElements);
    }

    //Clear Previous Data
    function clearPreviousData() {
        $('#txtNameId').val('');
        $('#txtRetry1,#txtRetry2,#txtMax1,#txtMin2,#txtXD3,#txtXF4,#txtMinAge1,#txtMinLad2,#txtMaxAge3,#txtMaxlad4').prop('disabled', false);
       // $('#txtRetry1,#txtRetry2,#txtMax1,#txtMin2,#txtXD3,#txtXF4,#txtMinAge1,#txtMinLad2,#txtMaxAge3,#txtMaxlad4').prop('disabled', 'disabled');
       // $('#txtRetry2').prop('disabled', 'disabled');
       // $('#txtMax1').prop('disabled', 'disabled');
       // $('#txtMin2').prop('disabled', 'disabled');
       // $('#txtXD3').prop('disabled', 'disabled');
       // $('#txtXF4').prop('disabled', 'disabled');
       // $('#txtMinAge1').prop('disabled', 'disabled');
       // $('#txtMinLad2').prop('disabled', 'disabled');
        //$('#txtMaxAge3').prop('disabled', 'disabled');
       // $('#txtMaxlad4').prop('disabled', 'disabled');
        $('#child1,#child2,#child3,#child11,#child12').hide();
       
    }

    $('#roboCopyCreate').on('click', function () {
        clearPreviousData();
        btnCrudEnable('SaveFunction');
        $('#SaveFunction').text('Save');
        clearErrorMessage();
    });


    //Validations
    function validateDropDown(value, errorMessage, errorElement) {

        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function validateName(value, id = null) {

        const errorElement = $('#rcpNameError');

        if (!value) {
            errorElement.text('Enter robocopy options name')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + roboCopyFunURL.roboCopyExistUrl;
        let data = {};
        data.name = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];

        return await CommonValidation(errorElement, validationResults);
    }
    async function IsNameExist(url, data, errorFunc) {
        return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }
})