﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;

public class
    GetDrDrillByBusinessServiceIdQueryHandler : IRequestHandler<GetDrDrillByBusinessServiceIdQuery,
        List<WorkflowOperationDrDrillVm>>
{
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IInfraDashboardViewRepository _infraDashboardViewRepository;
    private readonly IServerViewRepository _serverViewRepository;

    public GetDrDrillByBusinessServiceIdQueryHandler(
        IWorkflowOperationRepository workflowOperationRepository,
        IInfraDashboardViewRepository infraDashboardViewRepository,
        IServerViewRepository serverViewRepository)
    {

        _workflowOperationRepository = workflowOperationRepository;
        _infraDashboardViewRepository = infraDashboardViewRepository;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<WorkflowOperationDrDrillVm>> Handle(GetDrDrillByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var workflowOperation = await _workflowOperationRepository.GetDrillDetailsByBusinessServiceId(request.BusinessServiceId);

        if (workflowOperation.WorkflowOperationDrDrillVm.Count == 0)
            return new List<WorkflowOperationDrDrillVm>();

        var infraDashboard = await _infraDashboardViewRepository.GetInfraObjectViewByInfraObjectIds(workflowOperation.InfraIds);

        var infraServerComponent = infraDashboard
            .Where(x => x.ServerProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.ReferenceId,
                kvp =>
                {
                    var json = JObject.Parse(kvp.ServerProperties);

                    var serverIds = new List<ServerIds>
                    {
                        new()
                        {
                            PrServerId = json.SelectToken("PR.id")?.ToString()
                                         ?? json.SelectToken("PR.Id")?.ToString(),
                            DrServerId = json.SelectToken("DR.id")?.ToString()
                                         ?? json.SelectToken("DR.Id")?.ToString()
                        }
                    };

                    return serverIds
                        .Where(ids => ids.PrServerId != null || ids.DrServerId != null)
                        .GroupBy(ids => new { ids.PrServerId, ids.DrServerId })
                        .Select(group => group.First())
                        .ToList();
                }
            );

        var serverIds = infraServerComponent
            .SelectMany(x => x.Value)
            .SelectMany(id => new[] { id.PrServerId, id.DrServerId })
            .Where(id => id != null)
            .ToList();

        var servers = await _serverViewRepository.GetServerTypeByIds(serverIds);

        var serversById = servers.ToDictionary(server => server.ReferenceId, server => server);

        foreach (var workflowOperationDrill in workflowOperation.WorkflowOperationDrDrillVm)
        {
            var configuredRtO = new HashSet<string>();

            foreach (var groupDrill in workflowOperationDrill.WorkflowOperationGroupDrDrillVms)
            {
                var infraObject = infraDashboard.FirstOrDefault(x => x.ReferenceId == groupDrill.InfraObjectId);

                if (infraObject is null) continue;

                var configuredRtoValue = infraObject?.ConfiguredRTO?.Trim();
                if (configuredRtoValue.IsNotNullOrWhiteSpace())
                {
                    configuredRtO.Add(configuredRtoValue);
                }

                if (infraServerComponent.TryGetValue(groupDrill.InfraObjectId, out var serverIdsList))
                {
                    var prServerIds = serverIdsList.Select(pr => pr.PrServerId).Where(id => id != null).ToList();
                    var drServerIds = serverIdsList.Select(dr => dr.DrServerId).Where(id => id != null).ToList();

                    foreach (var serverId in prServerIds)
                    {
                        var server = serversById.GetValueOrDefault(serverId);
                        if (server != null)
                        {
                            groupDrill.PrIpAddress = server.IpAddress;
                        }
                    }

                    foreach (var serverId in drServerIds)
                    {
                        var server = serversById.GetValueOrDefault(serverId);
                        if (server != null)
                        {
                            groupDrill.DrIpAddress = server.IpAddress;
                        }
                    }
                }
            }

            var configuredRto = configuredRtO.Max();
            workflowOperationDrill.ConfiguredRTO = configuredRto ?? "0";
            workflowOperationDrill.RtoStatus = !workflowOperationDrill.WorkflowOperationGroupDrDrillVms.Any(x => x.Status.Equals("Aborted")) && (workflowOperationDrill.TotalTime <=
                                                TimeSpan.FromMinutes(int.Parse(configuredRto ?? "0")));
        }

        return workflowOperation.WorkflowOperationDrDrillVm;
    }
}