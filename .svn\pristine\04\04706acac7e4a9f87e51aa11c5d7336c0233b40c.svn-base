﻿using AutoFixture;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class PageSolutionMappingServiceFixture
{
    public List<PageSolutionMappingListVm> List { get; }
    public PageSolutionMappingDetailVm Detail { get; }
    public BaseResponse Response { get; }
    public CreatePageSolutionMappingCommand CreateCommand { get; }
    public UpdatePageSolutionMappingCommand UpdateCommand { get; }
    public GetPageSolutionMappingPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<PageSolutionMappingListVm> PaginatedResult { get; }
    public string Id { get; }
    public string Name { get; }

    public PageSolutionMappingServiceFixture()
    {
        var fixture = new Fixture();

        List = fixture.CreateMany<PageSolutionMappingListVm>(3).ToList();
        Detail = fixture.Create<PageSolutionMappingDetailVm>();
        Response = fixture.Create<BaseResponse>();
        CreateCommand = fixture.Create<CreatePageSolutionMappingCommand>();
        UpdateCommand = fixture.Create<UpdatePageSolutionMappingCommand>();
        PaginatedQuery = fixture.Create<GetPageSolutionMappingPaginatedListQuery>();
        PaginatedResult = fixture.Create<PaginatedResult<PageSolutionMappingListVm>>();
        Id = Guid.NewGuid().ToString();
        Name = fixture.Create<string>();
    }
}