using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class CGExecutionReportFixture
{
    public GetCgExecutionPaginatedListQuery GetCgExecutionPaginatedListQuery { get; }
    public PaginatedResult<CgExecutionPaginatedListVm> PaginatedCgExecutionListVm { get; }
    public ResiliencyReadinessWorkflowScheduleLogCommand ResiliencyReadinessWorkflowScheduleLogCommand { get; }

    public CGExecutionReportFixture()
    {
        var fixture = new Fixture();

        GetCgExecutionPaginatedListQuery = fixture.Create<GetCgExecutionPaginatedListQuery>();
        PaginatedCgExecutionListVm = fixture.Create<PaginatedResult<CgExecutionPaginatedListVm>>();
        ResiliencyReadinessWorkflowScheduleLogCommand = fixture.Create<ResiliencyReadinessWorkflowScheduleLogCommand>();
    }

    public void Dispose()
    {

    }
}
