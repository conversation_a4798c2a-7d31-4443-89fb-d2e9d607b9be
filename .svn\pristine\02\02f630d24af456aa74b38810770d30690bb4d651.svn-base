﻿using AutoMapper;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers
{
    public class ServiceAvailabilityControllerTests
    {
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<ServiceAvailabilityController>> _mockLogger = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private ServiceAvailabilityController _controller;

        public ServiceAvailabilityControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new ServiceAvailabilityController(
                _mockDataProvider.Object,
                _mockMapper.Object,
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockLoggedInUserService.Object
            );
        }

        [Fact]
        public async Task List_Returns_ViewResult_With_DashboardBusinessViewModel()
        {
            var mockPaginatedBusinessViews = new PaginatedResult<BusinessViewPaginatedList>
            {
                Data = new List<BusinessViewPaginatedList>(),
                TotalCount = 0
            };

            _mockDataProvider.Setup(x => x.DashboardView.GetBusinessViews())
                .ReturnsAsync(mockPaginatedBusinessViews.Data);

            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
            var model = result.Model as DashboardBusinessViewModel;
            Assert.NotNull(model);
            Assert.Equal(mockPaginatedBusinessViews.Data, model.GetPaginatedBusinessViews);
        }

        [Fact]
        public async Task BusinessServiceOverview_Returns_JsonResult_With_DashboardBusinessViewModel()
        {
            var mockBusinessView = new DashboardBusinessViewModel
            {
                //GetPaginatedBusinessViews = new PaginatedResult<BusinessViewPaginatedList>()
            };
            //_mockDataProvider.Setup(x => x.DashboardView.GetPaginatedBusinessViews(It.IsAny<GetBusinessViewPaginatedListQuery>()))
            //    .ReturnsAsync(mockBusinessView.GetPaginatedBusinessViews);

            var result = await _controller.BusinessServiceOverview() as JsonResult;

            Assert.NotNull(result);
            var jsonData = result.Value as dynamic;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        //[Fact]
        //public async Task GetImpactDetailsByService_Returns_JsonResult_With_ImpactDetails()
        //{
        //    var businessServiceId = "testServiceId";
        //    var mockImpactDetails = new ImpactDetailVm();
        //    _mockDataProvider.Setup(x => x.HeatMapStatus.GetImpactDetail(businessServiceId))
        //        .ReturnsAsync(mockImpactDetails);

        //    var result = await _controller.GetImpactDetailsByBusinessServiceId(businessServiceId) as JsonResult;


        //    Assert.NotNull(result);
        //    var jsonData = result.Value as ImpactDetailVm;
        //    Assert.Equal(mockImpactDetails, jsonData);
        //}

        //[Fact]
        //public async Task BusinessServiceAvailability_Returns_JsonResult_With_AvailabilityDetails()
        //{
        //    var mockAvailabilityDetails = new BusinessServiceAvailabilityVm();
        //    _mockDataProvider.Setup(x => x.HeatMapStatus.GetBusinessServiceAvailability())
        //        .ReturnsAsync(mockAvailabilityDetails);

        //    var result = await _controller.BusinessServiceAvailability() as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as dynamic;
        //    var json = JsonConvert.SerializeObject(result.Value);
        //    Assert.Contains("\"Success\":true", json);
        //    Assert.NotNull(result);
        //}

        //[Fact]
        //public async Task BusinessFunctionAvailability_Returns_JsonResult_With_FunctionAvailabilityDetails()
        //{
        //    var mockFunctionAvailabilityDetails = new BusinessFunctionAvailabilityVm();
        //    _mockDataProvider.Setup(x => x.HeatMapStatus.GetBusinessFunctionAvailability())
        //        .ReturnsAsync(mockFunctionAvailabilityDetails);

        //    var result = await _controller.BusinessFunctionAvailability() as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as dynamic;
        //    var json = JsonConvert.SerializeObject(result.Value);
        //    Assert.Contains("\"Success\":true", json);
        //    Assert.NotNull(result);
        //}

        //[Fact]
        //public async Task DRReadyStatusList_Returns_JsonResult_With_DRReadyStatus()
        //{
        //    var mockDrReadyStatus = new List<DRReadyStatusListVm>();
        //    _mockDataProvider.Setup(x => x.DrReadyStatus.GetDrReadyStatus())
        //        .ReturnsAsync(mockDrReadyStatus);

        //    var result = await _controller.DRReadyStatusList() as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as dynamic;
        //    var json = JsonConvert.SerializeObject(result.Value);
        //    Assert.Contains("\"Success\":true", json);
        //    Assert.NotNull(result);
        //}

        //[Fact]
        //public async Task GetBusinessServiceDrReady_Returns_JsonResult_With_DRReadyDetails()
        //{
        //    var businessServiceId = "testServiceId";
        //    var mockDrReadyDetails = new List<BusinessServiceDrReadyDetailVm>();
        //    _mockDataProvider.Setup(x => x.DrReadyStatus.GetBusinessServiceDrReady(businessServiceId))
        //        .ReturnsAsync(mockDrReadyDetails);

        //    var result = await _controller.GetBusinessServiceDrReadyByBusinessServiceId(businessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as dynamic;
        //    var json = JsonConvert.SerializeObject(result.Value);
        //    Assert.Contains("\"Success\":true", json);
        //    Assert.NotNull(result);
        //}

        //[Fact]
        //public async Task BusinessService_Returns_JsonResult_With_ProfileAndBusinessFunction()
        //{
        //    var businessServiceId = "testServiceId";
        //    var mockProfileExecuted = new ProfileExecutorByBusinessServiceIdVm();
        //    var mockBusinessFunctionAffected = new AllCount();
        //    _mockDataProvider.Setup(x => x.WorkflowOperation.GetProfileExecutorByBusinessServiceId(businessServiceId))
        //        .ReturnsAsync(mockProfileExecuted);
        //    _mockDataProvider.Setup(x => x.DrReadyStatus.GetBusinessServiceIdByCount(businessServiceId))
        //        .ReturnsAsync(mockBusinessFunctionAffected);

        //    var result = await _controller.GetBusinessServiceDetailViewByBusinessServiceId(businessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as dynamic;
        //    var json = JsonConvert.SerializeObject(result.Value);
        //    Assert.Contains("\"success\":true", json);
        //    Assert.NotNull(result);
        //}

        [Fact]
        public async Task ImpactDetails_Returns_JsonResult_With_HeatMapDetails()
        {
            var businessServiceId = "testServiceId";
            var heatmapType = "type";
            var mockHeatmapDetails = new List<HeatMapStatusListVm>();
            _mockDataProvider.Setup(x => x.HeatMapStatus.GetHeatMapStatusByType(businessServiceId, heatmapType, It.IsAny<bool>()))
                .ReturnsAsync(mockHeatmapDetails);

            var result = await _controller.GetImpactDetailByHeatMapStatusType(businessServiceId, heatmapType, It.IsAny<bool>()) as JsonResult;

            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(mockHeatmapDetails, jsonResult.Value);
        }

        [Fact]
        public async Task BusinessTreeViewList_Returns_JsonResult_With_BusinessServiceDiagram()
        {

            var businessServiceId = "testServiceId";
            var mockBusinessList = new GetBusinessServiceDiagramDetailVm();
            _mockDataProvider.Setup(x => x.BusinessService.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId))
                .ReturnsAsync(mockBusinessList);

            var result = await _controller.GetBusinessServiceTreeViewListByBusinessServiceId(businessServiceId) as JsonResult;

            Assert.NotNull(result);
            var jsonData = result.Value as GetBusinessServiceDiagramDetailVm;
            Assert.Equal(mockBusinessList, jsonData);
        }

        //[Fact]
        //public async Task BusinessFunctionList_Returns_JsonResult_With_BusinessFunctionList()
        //{
        //    var businessServiceId = "testServiceId";
        //    var mockBusinessFunctionList = new List<GetBusinessFunctionListByBusinessServiceIdVm>();
        //    _mockDataProvider.Setup(x => x.DrReadyStatus.GetBusinessFunctionListByBusinessServiceId(businessServiceId))
        //        .ReturnsAsync(mockBusinessFunctionList);

        //    var result = await _controller.GetBusinessFunctionListByBusinessServiceId(businessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    var jsonData = result.Value as List<GetBusinessFunctionListByBusinessServiceIdVm>;
        //    Assert.Equal(mockBusinessFunctionList, jsonData);
        //}

        //[Fact]
        //public void RTOByBusinessService_ReturnsError_WhenBusinessServiceIdIsInvalid()
        //{
        //    string invalidBusinessServiceId = null;

        //    var result = _controller.GetRTOByBusinessServiceId(invalidBusinessServiceId);

        //    Assert.NotNull(result);
        //    var json = JsonConvert.SerializeObject(result.Result);
        //    Assert.Contains("\"Success\":false", json);
        //    Assert.NotNull(result);
        //}

        //[Fact]
        //public async Task RTOByBusinessService_ReturnsRTOData_WhenBusinessServiceIdIsValid()
        //{
        //    string validBusinessServiceId = "123";
        //    var mockTreeView = new RTOByBusinessServiceIdVm();
        //    _mockDataProvider.Setup(p => p.DashboardView.GetRTOByBusinessServiceId(validBusinessServiceId))
        //                 .ReturnsAsync(mockTreeView);

        //    var result = await _controller.GetRTOByBusinessServiceId(validBusinessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    Assert.Equal(mockTreeView, result.Value);
        //}
        //[Fact]
        //public async Task DrReadyByBusinessServiceId_ReturnsDrReadyData_WhenBusinessServiceIdIsValid()
        //{
        //    string validBusinessServiceId = "123";
        //    var mockTreeView = new DrReadyByBusinessServiceIdVm();
        //    _mockDataProvider.Setup(p => p.DashboardView.GetDrReadyByBusinessServiceId(validBusinessServiceId))
        //                 .ReturnsAsync(mockTreeView);

        //    var result = await _controller.DrReadyByBusinessServiceId(validBusinessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    Assert.Equal(mockTreeView, result.Value);
        //}

        //[Fact]
        //public async Task DrReadyByBusinessServiceId_ReturnsEmptyString_WhenExceptionOccurs()
        //{
        //    string validBusinessServiceId = "123";
        //    _mockDataProvider.Setup(p => p.DashboardView.GetDrReadyByBusinessServiceId(validBusinessServiceId))
        //                 .ThrowsAsync(new Exception("Test exception"));

        //    var result = await _controller.DrReadyByBusinessServiceId(validBusinessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    Assert.Equal("", result.Value);
            
        //}
        //[Fact]
        //public async Task ImpactAvailabilityByBusinessServiceId_ReturnsImpactAvailability_WhenBusinessServiceIdIsValid()
        //{
        //    string validBusinessServiceId = "123";
        //    var mockTreeView = new ImpactAvailabilityDetailVm();
        //    _mockDataProvider.Setup(p => p.DashboardView.GetImpactAvailabilityByBusinessServiceId(validBusinessServiceId))
        //                 .ReturnsAsync(mockTreeView);

        //    var result = await _controller.ImpactAvailabilityByBusinessServiceId(validBusinessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    Assert.Equal(mockTreeView, result.Value);
        //}
        //[Fact]
        //public async Task DataLagByBusinessService_ReturnsDataLag_WhenBusinessServiceIdIsValid()
        //{
        //    string validBusinessServiceId = "123";
        //    var mockTreeView = new DataLagListVm();
        //    _mockDataProvider.Setup(p => p.DashboardView.GetDataLagByBusinessServiceId(validBusinessServiceId))
        //                 .ReturnsAsync(mockTreeView);

        //    var result = await _controller.DataLagByBusinessServiceId(validBusinessServiceId) as JsonResult;

        //    Assert.NotNull(result);
        //    Assert.Equal(mockTreeView, result.Value);
        //}

        [Fact]
        public async Task DataLagByBusinessService_ReturnsEmptyString_WhenExceptionOccurs()
        {
            string validBusinessServiceId = "123";
            _mockDataProvider.Setup(p => p.DashboardView.GetDataLagByBusinessServiceId(validBusinessServiceId))
                         .ThrowsAsync(new Exception("Test exception"));

            var result = await _controller.DataLagByBusinessServiceId(validBusinessServiceId) as JsonResult;

            Assert.NotNull(result);
            
        }
        [Fact]
        public async Task SitePropertiesByBusinessService_ReturnsSiteProperties_WhenBusinessServiceIdIsValid()
        {
            string validBusinessServiceId = "123";
            var mockTreeView = new SitePropertiesByBusinessServiceIdVm();
            _mockDataProvider.Setup(p => p.DashboardView.GetSitePropertiesByBusinessServiceId(validBusinessServiceId))
                         .ReturnsAsync(mockTreeView);

            var result = await _controller.GetSitePropertiesByBusinessServiceId(validBusinessServiceId) as JsonResult;

            Assert.NotNull(result);
            Assert.Equal(mockTreeView, result.Value);
        }
        [Fact]
        public async Task SitePropertiesByBusinessService_ReturnsEmptyString_WhenExceptionOccurs()
        {
            string validBusinessServiceId = "123";
            _mockDataProvider.Setup(p => p.DashboardView.GetSitePropertiesByBusinessServiceId(validBusinessServiceId))
                         .ThrowsAsync(new Exception("Test exception"));

            var result = await _controller.GetSitePropertiesByBusinessServiceId(validBusinessServiceId) as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Test exception", json);
        }
        //[Fact]
        //public async Task DownloadOperationalFunctionsAvailabilityReport_ReturnsErrorContent_WhenExceptionOccurs()
        //{

        //    _mockDataProvider.Setup(p => p.HeatMapStatus.GetBusinessFunctionAvailability())
        //                 .ThrowsAsync(new Exception("Test exception"));


        //    var result = await _controller.DownloadOperationalFunctionsAvailabilityReport() as ContentResult;


        //    Assert.NotNull(result);
        //    Assert.Equal("An error occurred while generating the report.", result.Content);
        //    //_mockLogger.Verify(l => l.LogError(It.Is<string>(s => s.Contains("An error occurred: Test exception"))), Times.Once);
        //}
        [Fact]
        public void GetDrDrillDetailsByBusinessService_HandlesException()
        {
            var businessServiceId = "test-id";
            var isReport = "1";
            _mockDataProvider.Setup(p => p.WorkflowOperation.GetDrDrillDetailsByBusinessServiceId(businessServiceId))
                         .ThrowsAsync(new Exception("Test exception"));

            var result =  _controller.GetDrDrillDetailsByBusinessServiceId(businessServiceId, isReport);

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }
        [Fact]
        public async Task GetWorkflowOperation_HandlesException()
        {
            var workflowOperationId = "test-id";
            _mockDataProvider.Setup(p => p.Company.GetCompanyById(It.IsAny<string>()))
                         .ThrowsAsync(new Exception("Test exception"));

            var result = await _controller.GetWorkflowOperation(workflowOperationId) as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"success\":false", json);
            Assert.NotNull(result);
        }
        [Fact]
        public async Task GetDynamicDashboardList_ReturnsDynamicDashboardListWithSubDashboards()
        {
            var dynamicDashboardList = new List<DynamicDashboardListVm>
            {
              new DynamicDashboardListVm { Id = "1", Name = "Dashboard 1" },
              new DynamicDashboardListVm { Id = "2", Name = "Dashboard 2" }
            };

            var dynamicSubDashboardList = new List<DynamicSubDashboardListVm>
            {
              new DynamicSubDashboardListVm { Id = "10",  Name = "SubDashboard 1" },
              new DynamicSubDashboardListVm { Id = "11", Name = "SubDashboard 2" }
            };

            var dynamicDashboardMapList = new List<DynamicDashboardMapListVm>
            {
              new DynamicDashboardMapListVm { DashBoardSubId = "10" },
              new DynamicDashboardMapListVm { DashBoardSubId = "11" }
            };

            _mockDataProvider.Setup(p => p.DynamicDashboard.GetDynamicDashboardList()).ReturnsAsync(dynamicDashboardList);
            _mockDataProvider.Setup(p => p.DynamicSubDashboard.GetByDashboardIdAsync(dynamicDashboardList[0].Id)).ReturnsAsync(dynamicSubDashboardList);
                        
            _mockDataProvider.Setup(p => p.DynamicDashboardMap.GetDynamicDashboardMapList()).ReturnsAsync(dynamicDashboardMapList);
            _mockMapper.Setup(x=>x.Map<List<DynamicDashboardViewModel>>(dynamicDashboardList)).Returns(new List<DynamicDashboardViewModel>());

            var result = await _controller.GetDynamicDashboardList() as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetDynamicDashboardList_HandlesException()
        {
            _mockDataProvider.Setup(p => p.DynamicDashboard.GetDynamicDashboardList())
                         .ThrowsAsync(new Exception("Test exception"));

            var result = await _controller.GetDynamicDashboardList() as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }
        [Fact]
        public async Task GetCustomDashboardNames_ReturnsCustomDashboard()
        {

            var mockDashboardList = new List<DynamicDashboardListVm>
        {
            new DynamicDashboardListVm { Id = "1", Name = "Dashboard 1", IsDelete = false },
            new DynamicDashboardListVm { Id = "2", Name = "Dashboard 2", IsDelete = true },
            new DynamicDashboardListVm { Id = "3", Name = "Dashboard 3", IsDelete = false }
        };

            _mockDataProvider.Setup(p => p.DynamicDashboard.GetDynamicDashboardList())
                         .ReturnsAsync(mockDashboardList);


            var result = await _controller.GetCustomDashboardNames() as JsonResult;


            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetCustomDashboardNames_ReturnsNullWhenNoCustomDashboard()
        {
            var mockDashboardList = new List<DynamicDashboardListVm>
        {
            new DynamicDashboardListVm { Id = "1", Name = "Dashboard 1", IsDelete = false },
            new DynamicDashboardListVm { Id = "3", Name = "Dashboard 3", IsDelete = false }
        };

            _mockDataProvider.Setup(p => p.DynamicDashboard.GetDynamicDashboardList())
                         .ReturnsAsync(mockDashboardList);

            var result = await _controller.GetCustomDashboardNames() as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }
		[Fact]
		public async Task GetDynamicSubDashboardList_ReturnsSuccessResult()
        {

            var mockSubDashboardList = new List<DynamicSubDashboardListVm>
        {
            new DynamicSubDashboardListVm { Name = "SubDashboard 1" },
            new DynamicSubDashboardListVm { Name = "SubDashboard 2" },
        };

            _mockDataProvider.Setup(p => p.DynamicSubDashboard.GetDynamicSubDashboardList())
                         .ReturnsAsync(mockSubDashboardList);

            var result = await _controller.GetDynamicSubDashboardList() as JsonResult;

            var jsonResult=Assert.IsType<JsonResult>(result);
            Assert.NotNull(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			
		}

        [Fact]
        public void GetDynamicSubDashboardList_ReturnsErrorResult_OnException()
        {
            _mockDataProvider.Setup(p => p.DynamicSubDashboard.GetDynamicSubDashboardList())
                         .ThrowsAsync(new Exception("Test exception"));

            var result =  _controller.GetDynamicSubDashboardList();

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Result);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }
    }
}