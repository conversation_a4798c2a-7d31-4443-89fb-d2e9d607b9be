﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class DRCalendarService : IDrCalendarService
{
    private readonly IBaseClient _client;
    public DRCalendarService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<BaseResponse> CreateAsync(CreateDrCalendarCommand createDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/drcalender", Method.Post);

        request.AddJsonBody(createDrCalenderCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string calId)
    {
        var request = new RestRequest($"api/v6/businessfunctions/{calId}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<DrCalendarDetailVm> GetDrCalendarById(string id)
    {
        var request = new RestRequest($"api/v6/drcalender/{id}");

        return await _client.Get<DrCalendarDetailVm>(request);
    }

    public async Task<List<DrCalendarActivityListVm>> GetDrCalenderList()
    {
        var request = new RestRequest("api/v6/drcalender");

        return await _client.Get<List<DrCalendarActivityListVm>>(request);
    }

    public async Task<PaginatedResult<DrCalendarActivityListVm>> GetPaginatedDrCalendar(GetDrCalendarPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/drcalender/paginated-list{query}");

        return await _client.Get<PaginatedResult<DrCalendarActivityListVm>>(request);
    }

    public async Task<GetUpcomingDrillCountVm> GetDrCalendarDrillEvents()
    {
        var request = new RestRequest("api/v6/drcalender/drill-events");

        return await _client.Get<GetUpcomingDrillCountVm>(request);
    }

    public async Task<bool> IsDrCalendarNameExist(string activityName, string? id, DateTime scheduleStartTime)
    {
        var request = new RestRequest($"api/v6/drcalender/name-exist?activityName={activityName}&id={id}&scheduleStartTime={scheduleStartTime}");

        return await _client.Get<bool>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDrCalendarCommand updateDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/drcalender", Method.Put);

        request.AddJsonBody(updateDrCalenderCommand);

        return await _client.Put<BaseResponse>(request);
    }
}