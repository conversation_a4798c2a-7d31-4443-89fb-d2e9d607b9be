﻿using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class ReportServiceFixture : IDisposable
{
    public GetRtoReportQuery RtoQuery { get; }
    public GetLicenseReportQuery LicenseQuery { get; }
    public GetRunBookReportQuery RunbookQuery { get; }
    public GetCyberSnapsReportQuery CyberSnapReportQuery { get; }
    public GetInfraObjectSchedulerLogsReportQuery SchedulerLogQuery { get; }

    public RTOReports RtoResponse { get; set; }
    public LicenseReport LicenseResponse { get; }
    public GetRunBookReportVm RunbookResponse { get; }
    public GetCyberSnapsReportVm CyberSnapResponse { get; }
    public GetResiliencyReadinessSchedulerLogReportVm SchedulerLogResponse { get; }

    public ReportServiceFixture()
    {
        var fixture = new Fixture();

        RtoQuery = fixture.Create<GetRtoReportQuery>();
        LicenseQuery = fixture.Create<GetLicenseReportQuery>();
        RunbookQuery = fixture.Create<GetRunBookReportQuery>();
        CyberSnapReportQuery = fixture.Create<GetCyberSnapsReportQuery>();
        SchedulerLogQuery = fixture.Create<GetInfraObjectSchedulerLogsReportQuery>();

        RtoResponse = fixture.Build<RTOReports>()
            .With(x => x.Date, RtoQuery.Id)
            .Create();

        LicenseResponse = fixture.Build<LicenseReport>()
            .With(x => x.Date, LicenseQuery.LicenseId)
            .Create();

        RunbookResponse = fixture.Build<GetRunBookReportVm>()
            .With(x => x.WorkflowId, RunbookQuery.WorkflowId)
            .Create();

        CyberSnapResponse = fixture.Build<GetCyberSnapsReportVm>()
            .With(x => x.ReportGeneratedTime, CyberSnapReportQuery.CyberSnapTagName)
            .Create();

        SchedulerLogResponse = fixture.Build<GetResiliencyReadinessSchedulerLogReportVm>()
            .With(x => x.ReportGeneratedTime, SchedulerLogQuery.StartDate)
            .Create();
    }

    public void Dispose()
    {

    }
}