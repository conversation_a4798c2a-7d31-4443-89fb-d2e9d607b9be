﻿using ContinuityPatrol.Application.Features.DataLag.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.Extensions.Logging;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.DataLag.Events
{
    public class DataLagDeletedEventHandlerTests : IClassFixture<DataLagFixture>
    {
        private readonly DataLagFixture _fixture;

        public DataLagDeletedEventHandlerTests(DataLagFixture fixture)
        {
            _fixture = fixture;
        }

        [Fact]
        public async Task Handle_Should_Log_And_Record_UserActivity_When_DataLag_Deleted()
        {
            // Arrange
            var deletedEvent = _fixture.DataLagDeletedEvent;

            var loggerMock = new Mock<ILogger<DataLagDeletedEventHandler>>();
            var userActivityRepoMock = new Mock<IUserActivityRepository>();
            var userServiceMock = new Mock<ILoggedInUserService>();

            userServiceMock.Setup(x => x.UserId).Returns("user-id-123");
            userServiceMock.Setup(x => x.LoginName).Returns("testuser");
            userServiceMock.Setup(x => x.CompanyId).Returns("company-xyz");
            userServiceMock.Setup(x => x.RequestedUrl).Returns("http://localhost/api/datalag/delete");
            userServiceMock.Setup(x => x.IpAddress).Returns("***********");

            var handler = new DataLagDeletedEventHandler(
                userServiceMock.Object,
                loggerMock.Object,
                userActivityRepoMock.Object
            );

            // Act
            await handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "user-id-123" &&
                ua.LoginName == "testuser" &&
                ua.CompanyId == "company-xyz" &&
                ua.RequestUrl == "http://localhost/api/datalag/delete" &&
                ua.HostAddress == "***********" &&
                ua.Action == "Delete DataLag" &&
                ua.Entity == "DataLag" &&
                ua.ActivityType == "Delete" &&
                ua.ActivityDetails.Contains(deletedEvent.Name)
            )), Times.Once);

            loggerMock.Verify(l => l.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((obj, _) => obj.ToString().Contains($"DataLag '{deletedEvent.Name}' deleted successfully.")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
        }
    }
}
