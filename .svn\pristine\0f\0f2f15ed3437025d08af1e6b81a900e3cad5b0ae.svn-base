﻿using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.WorkflowExecutionTempModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class WorkflowExecutionTempServiceFixture : IDisposable
{
    public List<WorkflowExecutionTempListVm> WorkflowExecutionTempList { get; }
    public WorkflowExecutionTempDetailVm WorkflowExecutionTempDetailVm { get; }
    public CreateWorkflowExecutionTempCommand CreateCommand { get; }
    public UpdateWorkflowExecutionTempCommand UpdateCommand { get; }
    public CreateWorkflowExecutionTempResponse CreateResponse { get; }
    public UpdateWorkflowExecutionTempResponse UpdateResponse { get; }
    public BaseResponse DeleteResponse { get; }
    public BaseResponse DeleteByWorkflowIdResponse { get; }

    public WorkflowExecutionTempServiceFixture()
    {
        var fixture = new Fixture();

        WorkflowExecutionTempList = fixture.Create<List<WorkflowExecutionTempListVm>>();
        WorkflowExecutionTempDetailVm = fixture.Create<WorkflowExecutionTempDetailVm>();
        CreateCommand = fixture.Create<CreateWorkflowExecutionTempCommand>();
        UpdateCommand = fixture.Create<UpdateWorkflowExecutionTempCommand>();
        CreateResponse = new CreateWorkflowExecutionTempResponse
        {
            WorkflowExecutionTempId = Guid.NewGuid().ToString(),
            Message = "Created",
            Success = true
        };
        UpdateResponse = new UpdateWorkflowExecutionTempResponse
        {
            WorkflowExecutionTempId = Guid.NewGuid().ToString(),
            Message = "Updated",
            Success = true
        };
        DeleteResponse = new BaseResponse
        {
            Message = "Deleted",
            Success = true
        };
        DeleteByWorkflowIdResponse = new BaseResponse
        {
            Message = "Deleted By WorkflowId",
            Success = true
        };
    }

    public void Dispose()
    {
        // Clean-up logic if needed
    }
}
