using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class WorkflowActionTypeControllerTests : IClassFixture<WorkflowActionTypeFixture>
{
    private readonly WorkflowActionTypeFixture _workflowActionTypeFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly WorkflowActionTypeController _controller;

    public WorkflowActionTypeControllerTests(WorkflowActionTypeFixture workflowActionTypeFixture)
    {
        _workflowActionTypeFixture = workflowActionTypeFixture;
        
        var testBuilder = new ControllerTestBuilder<WorkflowActionTypeController>();
        _controller = testBuilder.CreateController(
            _ => new WorkflowActionTypeController(),
            out _mediatorMock);
    }

    #region GetWorkflowActionTypes Tests

    [Fact]
    public async Task GetWorkflowActionTypes_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowActionTypeListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_workflowActionTypeFixture.WorkflowActionTypeListVm);

        // Act
        var result = await _controller.GetWorkflowActionTypes();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actionTypes = Assert.IsAssignableFrom<List<WorkflowActionTypeListVm>>(okResult.Value);
        Assert.Equal(6, actionTypes.Count);
        Assert.Contains(actionTypes, at => at.ActionType == "Database Connection");
        Assert.Contains(actionTypes, at => at.ActionType == "File Operation");
        Assert.Contains(actionTypes, at => at.ActionType == "Email Notification");
        Assert.Contains(actionTypes, at => at.ActionType == "HTTP Request");
        Assert.Contains(actionTypes, at => at.ActionType == "Script Execution");
        Assert.Contains(actionTypes, at => at.ActionType == "System Command");
        Assert.All(actionTypes.Take(5), at => Assert.True(at.IsDelete));
        Assert.False(actionTypes.Last().IsDelete);
    }

    [Fact]
    public async Task GetWorkflowActionTypes_ReturnsEmptyList_WhenNoActionTypesExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowActionTypeListQuery>(), default))
            .ReturnsAsync(new List<WorkflowActionTypeListVm>());

        // Act
        var result = await _controller.GetWorkflowActionTypes();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<WorkflowActionTypeListVm>)okResult.Value!));
    }

    #endregion

    #region CreateWorkflowActionTypes Tests

    [Fact]
    public async Task CreateWorkflowActionTypes_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _workflowActionTypeFixture.CreateWorkflowActionTypeCommand;
        var expectedResponse = _workflowActionTypeFixture.CreateWorkflowActionTypeResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateWorkflowActionTypes(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateWorkflowActionTypeResponse>(createdResult.Value);
        Assert.Equal("WorkflowActionType created successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

   

    #endregion

    #region DeleteWorkflowActionType Tests

    [Fact]
    public async Task DeleteWorkflowActionType_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _workflowActionTypeFixture.DeleteWorkflowActionTypeResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteWorkflowActionTypeCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteWorkflowActionType(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteWorkflowActionTypeResponse>(okResult.Value);
        Assert.Equal("WorkflowActionType deleted successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteWorkflowActionType_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteWorkflowActionType("invalid-guid"));
    }

    [Fact]
    public async Task DeleteWorkflowActionType_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteWorkflowActionType(""));
    }

    [Fact]
    public async Task DeleteWorkflowActionType_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteWorkflowActionTypeCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("WorkflowActionType", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteWorkflowActionType(nonExistentId));
    }

    #endregion

    #region UpdateWorkflowActionType Tests

    [Fact]
    public async Task UpdateWorkflowActionType_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand;
        var expectedResponse = _workflowActionTypeFixture.UpdateWorkflowActionTypeResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateWorkflowActionType(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateWorkflowActionTypeResponse>(okResult.Value);
        Assert.Equal("WorkflowActionType updated successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateWorkflowActionType_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _workflowActionTypeFixture.UpdateWorkflowActionTypeCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("WorkflowActionType", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateWorkflowActionType(command));
    }

    #endregion

    #region GetPaginatedWorkflowActionType Tests

    [Fact]
    public async Task GetPaginatedWorkflowActionType_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _workflowActionTypeFixture.GetWorkflowActionTypePaginatedListQuery;
        var expectedResult = _workflowActionTypeFixture.PaginatedWorkflowActionTypes;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedWorkflowActionType(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<WorkflowActionTypeListVm>>(okResult.Value);
        Assert.Equal(6, paginatedResult.Data.Count);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(6, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedWorkflowActionType_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetWorkflowActionTypePaginatedListQuery
        {
            PageSize = 10,
            SearchString = "Database",
            SortColumn = "ActionType",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<WorkflowActionTypeListVm>
        {
            Data = _workflowActionTypeFixture.WorkflowActionTypeListVm
                .Where(at => at.ActionType.Contains("Database")).ToList(),
            PageSize = 10,
            TotalCount = 1,
            TotalPages = 1
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedWorkflowActionType(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<WorkflowActionTypeListVm>>(okResult.Value);
        Assert.Equal(1, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, at => Assert.Contains("Database", at.ActionType));
    }

    [Fact]
    public async Task GetPaginatedWorkflowActionType_WithDeletableFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetWorkflowActionTypePaginatedListQuery
        {
            PageSize = 10,
            SearchString = "IsDelete=true",
            SortColumn = "ActionType",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<WorkflowActionTypeListVm>
        {
            Data = _workflowActionTypeFixture.WorkflowActionTypeListVm
                .Where(at => at.IsDelete).ToList(),
            PageSize = 10,
            TotalCount = 5,
            TotalPages = 1
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedWorkflowActionType(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<WorkflowActionTypeListVm>>(okResult.Value);
        Assert.Equal(5, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, at => Assert.True(at.IsDelete));
    }

    #endregion

    #region IsWorkflowActionTypeExist Tests

    [Fact]
    public async Task IsWorkflowActionTypeExist_WithValidActionType_ReturnsOkResult()
    {
        // Arrange
        var actionType = "Test Action Type";
        var id = "0";
        var expectedResult = _workflowActionTypeFixture.IsWorkflowActionTypeNameExistResult;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetWorkflowActionTypeNameUniqueQuery>(
                q => q.ActionType == actionType && q.ActionTypeId == id), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.IsWorkflowActionTypeExist(actionType, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task IsWorkflowActionTypeExist_WithEmptyActionType_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsWorkflowActionTypeExist("", "0"));
    }

    [Fact]
    public async Task IsWorkflowActionTypeExist_WithWhiteSpaceActionType_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsWorkflowActionTypeExist("   ", "0"));
    }

    [Fact]
    public async Task IsWorkflowActionTypeExist_WithExistingActionType_ReturnsTrue()
    {
        // Arrange
        var actionType = "Database Connection";
        var id = "0";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetWorkflowActionTypeNameUniqueQuery>(
                q => q.ActionType == actionType && q.ActionTypeId == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsWorkflowActionTypeExist(actionType, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsWorkflowActionTypeExist_WithNonExistingActionType_ReturnsFalse()
    {
        // Arrange
        var actionType = "Non-Existing Action Type";
        var id = "0";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetWorkflowActionTypeNameUniqueQuery>(
                q => q.ActionType == actionType && q.ActionTypeId == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsWorkflowActionTypeExist(actionType, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    #endregion
}
