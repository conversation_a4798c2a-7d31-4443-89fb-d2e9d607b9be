﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MysqlMonitorLogsControllerTests : IClassFixture<MysqlMonitorLogsFixture>
{
    private readonly MysqlMonitorLogsFixture _fixture;
    private readonly MysqlMonitorLogsController _controller;
    private readonly Mock<IMediator> _mediatorMock;

    public MysqlMonitorLogsControllerTests(MysqlMonitorLogsFixture fixture)
    {
        _fixture = fixture;

        var builder = new ControllerTestBuilder<MysqlMonitorLogsController>();
        _controller = builder.CreateController(_ => new MysqlMonitorLogsController(), out _mediatorMock);
    }

    [Fact]
    public async Task CreateMysqlMonitorLog_ReturnsCreated()
    {
        _mediatorMock.Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.CreateResponse);

        var result = await _controller.CreateMysqlMonitorLog(_fixture.CreateCommand);

        var created = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsAssignableFrom<CreateMYSQLMonitorLogResponse>(created.Value);
        Assert.Equal(_fixture.CreateResponse.Id, response.Id);
    }

    [Fact]
    public async Task GetAllMysqlMonitorLogs_ReturnsList()
    {
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetMYSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.LogsList);

        var result = await _controller.GetAllMysqlMonitorLogs();

        var ok = Assert.IsType<OkObjectResult>(result.Result);
        var list = Assert.IsAssignableFrom<List<MYSQLMonitorLogsListVm>>(ok.Value);
        Assert.Equal(_fixture.LogsList.Count, list.Count);
    }

    [Fact]
    public async Task GetMysqlMonitorLogsById_ReturnsDetail()
    {
        _mediatorMock.Setup(m => m.Send(It.Is<GetMYSQLMonitorLogsDetailQuery>(q => q.Id == _fixture.TestId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _controller.GetMysqlMonitorLogsById(_fixture.TestId);

        var ok = Assert.IsType<OkObjectResult>(result.Result);
        var detail = Assert.IsType<MYSQLMonitorLogsDetailVm>(ok.Value);
        Assert.Equal(_fixture.DetailVm.Id, detail.Id);
    }

    [Fact]
    public async Task GetMysqlMonitorLogsByType_ReturnsDetailByType()
    {
        _mediatorMock.Setup(m => m.Send(It.Is<GetMYSQLMonitorLogsDetailByTypeQuery>(q => q.Type == _fixture.TestType), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MYSQLMonitorLogsDetailByTypeVm> {});

        var result = await _controller.GetMysqlMonitorLogsByType(_fixture.TestType);

       
    }

    [Fact]
    public async Task GetPaginatedMysqlMonitorLogs_ReturnsPaginatedList()
    {
        _mediatorMock.Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PaginatedResult<MYSQLMonitorLogsListVm> {} );

        var result = await _controller.GetPaginatedMysqlMonitorLogs(_fixture.PaginatedQuery);

        var ok = Assert.IsType<OkObjectResult>(result.Result);
       
    }
}
