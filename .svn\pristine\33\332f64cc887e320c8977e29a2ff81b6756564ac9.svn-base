﻿using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicSubDashboard.Command.Delete;

public class DeleteDynamicSubDashboardCommandHandlerTests
{
    private readonly Mock<IDynamicSubDashboardRepository> _repositoryMock;
    private readonly Mock<IPublisher> _publisherMock;
    private readonly DeleteDynamicSubDashboardCommandHandler _handler;

    public DeleteDynamicSubDashboardCommandHandlerTests()
    {
        _repositoryMock = new Mock<IDynamicSubDashboardRepository>();
        _publisherMock = new Mock<IPublisher>();
        _repositoryMock = DynamicSubDashboardRepositoryMocks.DeleteDynamicSubDashboardRepository();
        _handler = new DeleteDynamicSubDashboardCommandHandler(_repositoryMock.Object, _publisherMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldSoftDelete_WhenEntityExists()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = Guid.NewGuid().ToString() };
        var dashboardEntity = new Domain.Entities.DynamicSubDashboard
        {
            ReferenceId = command.Id,
            Name = "TestDashboard",
            IsActive = true
        };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id))
                       .ReturnsAsync(dashboardEntity);

        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicSubDashboard>()))
            .ReturnsAsync((Domain.Entities.DynamicSubDashboard dashboard) => dashboard);

        _publisherMock.Setup(p => p.Publish(It.IsAny<DynamicSubDashboardDeletedEvent>(), It.IsAny<CancellationToken>()))
                      .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsActive.Should().BeFalse();
        result.Message.Should().Contain("DynamicSubDashboard");
        _repositoryMock.Verify(r => r.UpdateAsync(It.Is<Domain.Entities.DynamicSubDashboard>(x => !x.IsActive)), Times.Once);
        _publisherMock.Verify(p => p.Publish(It.IsAny<DynamicSubDashboardDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsNull()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = Guid.NewGuid().ToString() };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id))
                       .ReturnsAsync((Domain.Entities.DynamicSubDashboard)null!);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldSetIsActiveFalse_AndReturnSuccessResponse()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = "test-id" };
        var dashboard = new Domain.Entities.DynamicSubDashboard { ReferenceId = command.Id, Name = "TestDashboard", IsActive = true };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(dashboard);
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicSubDashboard>()))
                       .ReturnsAsync((Domain.Entities.DynamicSubDashboard d) => d);

        var handler = new DeleteDynamicSubDashboardCommandHandler(_repositoryMock.Object, _publisherMock.Object);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsActive.Should().BeFalse();
        result.Message.Should().Contain("deleted successfully");
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenDynamicSubDashboardNotFound()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = "non-existent-id" };
        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync((Domain.Entities.DynamicSubDashboard)null!);

        var handler = new DeleteDynamicSubDashboardCommandHandler(_repositoryMock.Object, _publisherMock.Object);

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldPublishDeletedEvent()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = "test-id" };
        var dashboard = new Domain.Entities.DynamicSubDashboard { ReferenceId = command.Id, Name = "TestDashboard", IsActive = true };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(dashboard);
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicSubDashboard>()))
                       .ReturnsAsync((Domain.Entities.DynamicSubDashboard d) => d);

        var handler = new DeleteDynamicSubDashboardCommandHandler(_repositoryMock.Object, _publisherMock.Object);

        // Act
        await handler.Handle(command, CancellationToken.None);

        // Assert
        _publisherMock.Verify(p => p.Publish(It.Is<DynamicSubDashboardDeletedEvent>(e => e.Name == dashboard.Name),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCallUpdateAsync_Once()
    {
        // Arrange
        var command = new DeleteDynamicSubDashboardCommand { Id = "test-id" };
        var dashboard = new Domain.Entities.DynamicSubDashboard { ReferenceId = command.Id, Name = "TestDashboard", IsActive = true };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(dashboard);
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicSubDashboard>()))
                       .ReturnsAsync((Domain.Entities.DynamicSubDashboard d) => d);

        var handler = new DeleteDynamicSubDashboardCommandHandler(_repositoryMock.Object, _publisherMock.Object);

        // Act
        await handler.Handle(command, CancellationToken.None);

        // Assert
        _repositoryMock.Verify(r => r.UpdateAsync(It.Is<Domain.Entities.DynamicSubDashboard>(d => d.IsActive == false)), Times.Once);
    }
}
