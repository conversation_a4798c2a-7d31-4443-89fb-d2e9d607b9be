﻿using ContinuityPatrol.Application.Features.Site.Queries.GetByType;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Queries;

public class GetSiteByTypeQueryHandlerTests : IClassFixture<SiteFixture>
{
    private readonly SiteFixture _siteFixture;

    private Mock<ISiteRepository> _mockSiteRepository;

    private readonly GetSiteByTypeQueryHandler _handler;

    public GetSiteByTypeQueryHandlerTests(SiteFixture siteFixture)
    {
        _siteFixture = siteFixture;

        _mockSiteRepository = SiteRepositoryMocks.GetSiteByTypeRepository(_siteFixture.Sites);

        _handler = new GetSiteByTypeQueryHandler(_mockSiteRepository.Object, _siteFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Valid_DatabasesType()
    {
        var result = await _handler.Handle(new GetSiteByTypeQuery { TypeId = _siteFixture.Sites[0].Type }, CancellationToken.None);

        result.ShouldBeOfType<List<SiteBySiteTypeVm>>();

        result[0].Id.ShouldBe(_siteFixture.Sites[0].ReferenceId);
        result[0].Name.ShouldBe(_siteFixture.Sites[0].Name);
        result[0].Location.ShouldBe(_siteFixture.Sites[0].Location);
        result[0].Type.ShouldBe(_siteFixture.Sites[0].Type);
        result[0].PlatformType.ShouldBe(_siteFixture.Sites[0].PlatformType);
        result[0].CompanyId.ShouldBe(_siteFixture.Sites[0].CompanyId);
        result[0].CompanyName.ShouldBe(_siteFixture.Sites[0].CompanyName);
    }

    [Fact]
    public async Task Handle_ReturnEmptyType_When_NoRecords()
    {
        _mockSiteRepository = SiteRepositoryMocks.GetSiteEmptyRepository();

        var handler = new GetSiteByTypeQueryHandler(_mockSiteRepository.Object, _siteFixture.Mapper);

        var result = await handler.Handle(new GetSiteByTypeQuery { TypeId = _siteFixture.Sites[0].Type }, CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSiteByTypeQuery { TypeId = _siteFixture.Sites[0].Type }, CancellationToken.None);

        _mockSiteRepository.Verify(x => x.GetSiteBySiteType(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
    [Fact]
    public void GetSiteByTypeQuery_Should_Assign_And_Return_Properties()
    {
        // Arrange
        var expectedCompanyId = "COMP001";
        var expectedTypeId = "TYPE002";

        // Act
        var query = new GetSiteByTypeQuery
        {
            CompanyId = expectedCompanyId,
            TypeId = expectedTypeId
        };

        // Assert
        Assert.Equal(expectedCompanyId, query.CompanyId);
        Assert.Equal(expectedTypeId, query.TypeId);
    }
    [Fact]
    public void SiteBySiteTypeVm_Should_Assign_And_Return_All_Properties()
    {
        // Arrange
        var id = "S001";
        var name = "Data Center A";
        var location = "Chennai";
        var locationId = "LOC123";
        var typeId = "TYPE001";
        var type = "Production";
        var platformType = "On-Premise";
        var companyId = "COMP456";
        var companyName = "TechCorp";
        var lat = "13.0827";
        var lng = "80.2707";

        // Act
        var vm = new SiteBySiteTypeVm
        {
            Id = id,
            Name = name,
            Location = location,
            LocationId = locationId,
            TypeId = typeId,
            Type = type,
            PlatformType = platformType,
            CompanyId = companyId,
            CompanyName = companyName,
            Lat = lat,
            Lng = lng
        };

        // Assert
        Assert.Equal(id, vm.Id);
        Assert.Equal(name, vm.Name);
        Assert.Equal(location, vm.Location);
        Assert.Equal(locationId, vm.LocationId);
        Assert.Equal(typeId, vm.TypeId);
        Assert.Equal(type, vm.Type);
        Assert.Equal(platformType, vm.PlatformType);
        Assert.Equal(companyId, vm.CompanyId);
        Assert.Equal(companyName, vm.CompanyName);
        Assert.Equal(lat, vm.Lat);
        Assert.Equal(lng, vm.Lng);
    }
}