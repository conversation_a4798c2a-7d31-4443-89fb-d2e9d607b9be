using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BulkImportOperationGroupFixture : IDisposable
{
    public List<BulkImportOperationGroup> BulkImportOperationGroups { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBulkImportOperationGroupCommand CreateBulkImportOperationGroupCommand { get; set; }
    public UpdateBulkImportOperationGroupCommand UpdateBulkImportOperationGroupCommand { get; set; }
    public DeleteBulkImportOperationGroupCommand DeleteBulkImportOperationGroupCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BulkImportOperationGroupFixture()
    {
        BulkImportOperationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"test\":\"value\"}",
                Status = "Pending",
                ProgressStatus = "0/5",
                ErrorMessage = "",
                ConditionalOperation = 1,
                NodeId = "Node001",
                InfraObjectName = "TestInfraObject",
                IsActive = true
            }
        };

        //BulkImportOperationGroups = AutoBulkImportOperationGroupFixture.Create<List<BulkImportOperationGroup>>();
        UserActivities = AutoBulkImportOperationGroupFixture.Create<List<UserActivity>>();
        CreateBulkImportOperationGroupCommand = AutoBulkImportOperationGroupFixture.Create<CreateBulkImportOperationGroupCommand>();
        UpdateBulkImportOperationGroupCommand = AutoBulkImportOperationGroupFixture.Create<UpdateBulkImportOperationGroupCommand>();
        DeleteBulkImportOperationGroupCommand = AutoBulkImportOperationGroupFixture.Create<DeleteBulkImportOperationGroupCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BulkImportOperationGroupProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBulkImportOperationGroupFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBulkImportOperationGroupCommand>(p => p.InfraObjectName, 10));
            fixture.Customize<CreateBulkImportOperationGroupCommand>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportOperationGroupCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportOperationGroupCommand>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<CreateBulkImportOperationGroupCommand>(c => c.With(b => b.ProgressStatus, "0/5"));
            fixture.Customize<CreateBulkImportOperationGroupCommand>(c => c.With(b => b.ConditionalOperation, 1));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBulkImportOperationGroupCommand>(p => p.InfraObjectName, 10));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.ProgressStatus, "0/5"));
            fixture.Customize<UpdateBulkImportOperationGroupCommand>(c => c.With(b => b.ConditionalOperation, 1));

            fixture.Customize<DeleteBulkImportOperationGroupCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ProgressStatus, "0/5"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ConditionalOperation, 1));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
