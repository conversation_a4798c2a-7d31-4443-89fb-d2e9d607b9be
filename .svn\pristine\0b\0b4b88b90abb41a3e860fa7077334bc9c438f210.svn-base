﻿using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetCpslScript;

[ExcludeFromCodeCoverage]
public class GetCpslScriptDetailQueryHandler : IRequestHandler<GetCpslScriptDetailQuery, GetCpslScriptDetailVm>
{
    private readonly IServerRepository _serverRepository;
    private readonly IDatabaseRepository _databaseRepository;
    
    public GetCpslScriptDetailQueryHandler(IServerRepository serverRepository, IDatabaseRepository databaseRepository)
    {
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
    }

    public async Task<GetCpslScriptDetailVm> Handle(GetCpslScriptDetailQuery request, CancellationToken cancellationToken)
    {
        var scriptDetailDto = await CpslConverter(request.Script, request.IsSubstituteAuth);

        return new GetCpslScriptDetailVm { Script = scriptDetailDto };
    }

  
    public async Task<string> CpslConverter(string cpslText, bool checkSubstituteAuth)
    {

        var script = string.Empty;
        try
        {
            var splitText = cpslText.Split("\n");

            var sshConnectCount = 0;
            var sshDbCount = 0;

            foreach (var commands in splitText)
            {
                string resultVariable;
                switch (commands.ToLower())
                {
                    case var s when commands.ToLower().Contains("disconnectssh"):
                        script += "\n" + "ssh.disconnect();";
                        break;
                    case var s1 when commands.ToLower().Contains("r_connect"):
                        sshConnectCount++;
                        var rvalue = commands.Split('"').Where((_, index) => index % 2 != 0)
                            .ToArray()[0];

                        resultVariable = sshConnectCount == 1 ? "PRConnectResult" : "DRConnectResult";

                        var serverlist1 = await _serverRepository.GetServerByServerName(rvalue);

                        if(serverlist1 is null)
                        {
                            throw new Exception($"Server ({rvalue}) is not found or not authorized");
                        }

                        var newValue1 = serverlist1.ReferenceId;//.Replace("-", "_");

                        script += "\n" + "ssh = new ssh()";

                        script += "\n" + resultVariable +
                                  " = ssh.connectsubstitute(\"prompt\":\"$\",\"timeout\":\"1\",\"waittime\":\"1\",\"servername\":\"" +
                                  newValue1 + "\",\"substitute\":\"" + checkSubstituteAuth + "\")";
                        break;

                    case var s when commands.ToLower().Contains("connectssh"):
                        sshConnectCount++;
                        var value = commands.Split('"').Where((_, index) => index % 2 != 0).ToArray()[0];
                        resultVariable = sshConnectCount == 1 ? "PRConnectResult" : "DRConnectResult";

                        var serverlist = await _serverRepository.GetServerByServerName(value);

                        if (serverlist is null)
                        {
                            throw new Exception($"Server ({value}) is not found or not authorized");
                        }

                        script += "\n" + "ssh = new ssh()";
                        script += "\n" + resultVariable +
                                  " = ssh.connect(\"prompt\":\"\",\"timeout\":\"1\",\"waittime\":\"1\",\"servername\":\"" +
                                  serverlist.ReferenceId + "\")";
                        break;
                    case var s when commands.ToLower().Contains("startsql"):
                        sshDbCount++;
                        resultVariable = sshDbCount == 1 ? "PRDBResult" : "DRDBResult";

                        var value1 = commands.Split('"').Where((_, index) => index % 2 != 0)
                            .ToArray()[0];

                        var dblist = await _databaseRepository.GetByDatabaseName(value1);

                        if (dblist is null)
                        {
                            throw new Exception($"Dataase ({value1}) is not found or not authorized");
                        }

                        script += "\n" + resultVariable +
                                  " =ssh.connectDB(\"prompt\":\"\",\"timeout\":\"1\",\"waittime\":\"1\",\"dbName\":\"" +
                                  dblist.ReferenceId + "\", \"dbType\":\"SQL\")";
                        break;

                    case var s when commands.ToLower().Contains("executeshellcommandenter"):
                        var executeCommandenter = commands.Split('"').Where((_, index) => index % 2 != 0)
                            .ToArray();
                        var result = commands.Split(',').ToList().TakeLast(2).ToList();

                        var resVariableValue = commands.ToLower().Split("executeshellcommandenter")[0]
                            .Replace("$", "");

                        var prompt2 = result[0].Trim('"');

                        // _logger.Information("executeshellcommandenter prompt : " + prompt2);

                        string commandScript;

                        if (string.IsNullOrEmpty(resVariableValue))
                        {
                            //script += "\n" + "ssh.execute(\"prompt\":\"" + prompt2 +"\",\"timeout\":\"" + result[1] + "\",\"waittime\":\"" + result[1] + "\",\"executecommand\":\"" +
                            //          executeCommandenter[0] + "\")";


                            commandScript = $"ssh.execute(\"prompt\":\"{prompt2}\",\"timeout\":\"{result[1]}\",\"waittime\":\"{result[1]}\",\"executecommand\":\"{executeCommandenter[0]}\")";

                            // _logger.Information("executeshellcommandenter commandScript : " + commandScript);

                            script += "\n" + commandScript;
                        }
                        else if (script.Contains(executeCommandenter[0]))
                        {
                            //script += "\n" + resVariableValue + "ssh.execute(\"prompt\":" + result[0] +
                            //          "\" \",\"timeout\":\"" + result[1] + "\",\"waittime\":\"" + result[1] + "\",\"executecommand\":" +
                            //          executeCommandenter[0] + ")";


                            commandScript = $"ssh.execute(\"prompt\":\"{prompt2}\",\"timeout\":\"{result[1]}\",\"waittime\":\"{result[1]}\",\"executecommand\":\"{executeCommandenter[0]}\")";

                            // _logger.Information("executeshellcommandenter commandScript : " + commandScript);

                            script += "\n" + resVariableValue + commandScript;
                        }
                        else
                        {
                            //script += "\n" + resVariableValue + "ssh.execute(\"prompt\":\"" + result[0] +
                            //          "\",\"timeout\":\"" + result[1] + "\",\"waittime\":\"" + result[1] + "\",\"executecommand\":\"" +
                            //          executeCommandenter[0] + "\")";


                            commandScript = $"ssh.execute(\"prompt\":\"{prompt2}\",\"timeout\":\"{result[1]}\",\"waittime\":\"{result[1]}\",\"executecommand\":\"{executeCommandenter[0]}\")";

                            //_logger.Information("executeshellcommandenter commandScript : " + commandScript);

                            script += "\n" + resVariableValue + commandScript;
                        }

                        break;
                    case var s when commands.ToLower().Contains("executeshellcommand"):
                        var executeCommand = commands.Split('"').Where((_, index) => index % 2 != 0)
                            .ToArray();
                        var result1 = commands.Split(',').ToList().TakeLast(2).ToList();

                        var resVariable = commands.ToLower().Split("executeshellcommand")[0].Replace("$", "");

                        var prompt1 = result1[0].Trim('"');

                        // _logger.Information("executeshellcommand prompt : " + prompt1);

                        script += "\n" + resVariable + " ssh.execute(" + "\"prompt\":\"" + prompt1 +
                                  " \",\"timeout\":\"1\",\"waittime\":\"" + result1[1] + " \",\"executecommand\":\"" +
                                  executeCommand[0] + "\")";

                        break;
                    case var s when commands.ToLower().Contains("endsql"):
                        //script += "\n" + "ssh.disconnectsql();";
                        break;
                    case var s when commands.ToLower().Contains("if"):
                        if (commands.ToLower().Contains("notcontains"))
                        {
                            var ifValue = commands.Replace("then", "\n{").Replace("notcontains", "contains");
                            var res = ifValue.Split("if(");
                            var finalres = res[0] + "if(!" + res[1];
                            script += "\n" + "" + finalres + "";
                            break;
                        }
                        else
                        {
                            var ifValue = commands.Replace("then", "\n{");
                            script += "\n" + "" + ifValue + "";
                            break;
                        }

                    case var s when commands.ToLower().Contains("return"):
                        var returnvalue = commands;
                        if (commands.Contains("true"))
                        {
                            returnvalue = returnvalue.Replace("true", "\"Success\"");
                        }
                        else
                        {
                            returnvalue = returnvalue.Replace("false", "\"Error\"");
                        }
                        script += "\n" + "" + returnvalue + "";
                        break;
                    case var s when commands.ToLower().Contains("else"):
                        var elseCommand = "}\n" + commands.ToLower() + "\n{";
                        script += "\n" + "" + elseCommand + "";
                        break;
                    case var s when commands.ToLower().Contains("end"):
                        var endCommand = "}";
                        script += "\n" + "" + endCommand + "";
                        break;

                    case var s when commands.ToLower().Contains("r_key"):
                        break;
                    case var s when commands.ToLower().Contains("concat"):
                        var concatCommand = commands.Split('"').Where((_, index) => index % 2 != 0)
                            .ToArray();
                        var concatResult = String.Empty;
                        var resConcatVariable = commands.ToLower().Split("concat")[0].Replace("$", "");

                        foreach (var concatvalues in concatCommand)
                        {
                            if (script.Contains(concatvalues.ToLower()))
                            {
                                concatResult += "@@" + concatvalues.ToLower() + "@@";
                            }
                            else
                            {
                                concatResult += "\"" + concatvalues + "\"";
                            }

                            concatResult += "+";
                        }

                        script += "\n" + resConcatVariable + "String(" + concatResult.TrimEnd('+') + ")";
                        break;
                    case var s when commands.ToLower().Contains("callcmd"):
                        break;
                    case var s when commands.ToLower().Contains("find"):
                        break;
                    case var s when commands.ToLower().Contains("getlinecontent"):
                        var executeCommandenter23 = commands.Split('"').Where((_, index) => index % 2 != 0).ToArray();

                        var resVariableValue23 = commands.Split('.').ToList()[0].ToLower().ToString().Split("=").ToList();

                        var resvariable = resVariableValue23[0].Replace("$", "");
                        var variable = resVariableValue23[1];

                        string pattern = "\"([^\"]*)\"";
                        MatchCollection matches = Regex.Matches(commands, pattern);
                        int lastQuoteIndex = commands.LastIndexOf('\"');
                        string resultIndex = commands.Substring(lastQuoteIndex + 2).Trim();

                        script += "\n" + resvariable + "=" + variable + ".split(sep=" + matches[0] + ").AT(" + resultIndex.Split(")")[0] + ")";
                        break;
                    case var s when commands.ToLower().Contains("extractstringwithlength"):
                        break;
                    case var s when commands.ToLower().Contains("while"):
                        break;
                    case var s when commands.ToLower().Contains("breakloopaftercycle"):
                        var breakLoopValue = commands.ToLower().Replace(commands.ToLower(), "");
                        script += breakLoopValue + "";
                        break;
                    case var s when commands.ToLower().Contains("goto loop"):
                        var gotoLoopValue = commands.ToLower().Replace("goto loop", "}");
                        script += "\n" + "" + gotoLoopValue + "";
                        break;
                    case var s when commands.ToLower().Contains("loop"):
                        int[] matchingIndexes =
                            (from current in splitText.Select((value, index) => new { value, index })
                             where current.value.ToString().ToLower().Contains("breakloopaftercycle")
                             select current.index).ToArray();
                        var index = matchingIndexes[0];
                        var loopIteration = splitText[index].Split("(")[1].TrimEnd(')');
                        var loopText = "for(int i=0;i<" + loopIteration + ";i++)\n{";
                        var loopValue = commands.ToLower().Replace("loop", loopText);
                        script += "\n" + "" + loopValue + "";
                        break;
                    case var s when commands.ToLower().Contains("waituntilmatchwithclear"):
                        var cmd = string.Empty;
                        var prompt = string.Empty;
                        foreach (var v in splitText)
                        {
                            if (v.ToLower().Contains("executeshellcommandenter"))
                            {

                                var executeCommand1 = v.ToString().Split('"').Where((_, index) => index % 2 != 0)
                                    .ToArray();
                                var result11 = v.Split(',').ToList().TakeLast(2).ToList();

                                cmd = executeCommand1[0];
                                prompt = result11[0];
                            }

                            if (!string.IsNullOrWhiteSpace(cmd) && !string.IsNullOrWhiteSpace(prompt))
                                break;
                        }

                        var val22 = commands.Split('"');
                        script += "\nWaitAndCheck(" + "\"" + cmd + "\"" + "," + prompt + ", " + val22[1] + ",10," +
                                  val22[2].Trim(',') + ")";


                        break;
                    case var s when commands.ToLower().Contains("wait"):
                        var waitTimeValue = commands.ToLower().Split(' ')[1];
                        var val = "common.wait(\"WaitTime\":\"" + waitTimeValue + "\")";
                        script += "\n" + "" + val + "";
                        break;

                    case var s when commands.ToLower().Contains("getserverpass"):
                        var serverName = commands.Split('"')[1];
                        script += "getServer = common.getserver(\"servername\":\"" + serverName + "\")";
                        script += "PRJSONResult = GetVariableFromJSON(\"getServer\")";
                        script += "Password = PRJSONResult[\"Password\"]";
                        break;

                    case var s when commands.ToLower().Contains("getserveruser"):
                        var serverName1 = commands.Split('"')[1];
                        script += "getServer1 = common.getserver(\"servername\":\"" + serverName1 + "\")";
                        script += "PRJSONResult = GetVariableFromJSON(\"getServer1\")";
                        script += "UserName = PRJSONResult[\"UserName\"]";
                        break;

                    case var s when commands.ToLower().Contains("getserverip"):
                        var serverName2 = commands.Split('"')[1];
                        script += "getServer2 = common.getserver(\"servername\":\"" + serverName2 + "\")";
                        script += "PRJSONResult = GetVariableFromJSON(\"getServer2\")";
                        script += "IpAddress = PRJSONResult[\"IpAddress\"]";
                        break;

                    case var s when commands.ToLower().Contains("getserverport"):
                        var serverName3 = commands.Split('"')[1];
                        script += "getServer3 = common.getserver(\"servername\":\"" + serverName3 + "\")";
                        script += "PRJSONResult = GetVariableFromJSON(\"getServer3\")";
                        script += "Port = PRJSONResult[\"Port\"]";
                        break;

                    case var s when commands.ToLower().Contains("getdatabasename"):
                        var dbName = commands.Split('"')[1];
                        script += "getDB = common.getDatabase(\"databasename\":\"" + dbName + "\"))";
                        script += "\n PRJSONResult = GetVariableFromJSON(\" getDB \")";
                        script += "\n DBName = PRJSONResult[\"DatabaseName\"]";
                        break;

                    case var s when commands.ToLower().Contains("getdatabaseuser"):
                        var dbName1 = commands.Split('"')[1];
                        script += "getDB1 = common.getDatabase(\"databasename\":\"" + dbName1 + "\"))";
                        script += "\n PRJSONResult = GetVariableFromJSON(\" getDB1 \")";
                        script += "\n DBUserName = PRJSONResult[\"DatabaseUserName\"]";
                        break;

                    case var s when commands.ToLower().Contains("getdatabasepass"):
                        var dbName2 = commands.Split('"')[1];
                        script += "getDB2 = common.getDatabase(\"databasename\":\"" + dbName2 + "\"))";
                        script += "PRJSONResult = GetVariableFromJSON(\" getDB2 \")";
                        script += "DBPassword = PRJSONResult[\"DatabasePassword\"]";
                        break;

                    case var s when commands.ToLower().Contains("getreplication"):
                        //var rep = commands.ToString().Split("\"")[1];
                        //script += "";
                        break;

                    case var s when commands.ToLower().Contains("r_key"):
                        var execute = commands.Split(" ")[1];
                        var splitKey = execute.Split(",");
                        script += "ResultVariable = ssh.sendkey(\"prompt\":\"" + splitKey[1] +
                                  "\", \"timeout\":\"1\", \"waittime\":\"" + splitKey[2] + "\",\"key\":\"" +
                                  splitKey[0] + "\",\"RequiredEnter\":\"false\")";
                        break;

                }
            }
        }
        catch (Exception exception)
        {

            throw new Exception(exception.GetMessage());

        }
        return script;
    }

}
