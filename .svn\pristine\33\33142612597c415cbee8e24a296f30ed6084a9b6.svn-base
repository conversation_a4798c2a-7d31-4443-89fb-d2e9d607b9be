﻿using System.Reflection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;
using FluentValidation;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Validators;

public class UpdateServerCommandValidatorTests
{
    private readonly UpdateServerCommandValidator _validator;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository;
    private readonly Mock<ILicenseValidationService> _mockLicenseValidationService;
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private readonly Mock<IDatabaseViewRepository> _mockDatabaseViewRepository;

    public UpdateServerCommandValidatorTests()
    {
        _mockServerRepository = new Mock<IServerRepository>();
        _mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
        _mockSiteRepository = new Mock<ISiteRepository>();
        _mockLicenseValidationService = new Mock<ILicenseValidationService>();
        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
        _mockDatabaseViewRepository = new Mock<IDatabaseViewRepository>();

        _validator = new UpdateServerCommandValidator(
            _mockServerRepository.Object,
            _mockLicenseManagerRepository.Object,
            _mockSiteRepository.Object,
            _mockLicenseValidationService.Object,
            _mockSiteTypeRepository.Object,
            _mockDatabaseViewRepository.Object);
    }

    private UpdateServerCommand GetValidCommand() => new()
    {
        Id = Guid.NewGuid().ToString(),
        Name = "TestServer",
        SiteId = Guid.NewGuid().ToString(),
        SiteName = "TestSite",
        BusinessServiceId = Guid.NewGuid().ToString(),
        BusinessServiceName = "TestBusinessService",
        RoleTypeId = Guid.NewGuid().ToString(),
        RoleType = "Application",
        ServerTypeId = Guid.NewGuid().ToString(),
        ServerType = "Windows",
        Logo = "test-logo.png",
        OSTypeId = Guid.NewGuid().ToString(),
        OSType = "Windows Server 2019",
        Status = "Active",
        Properties = "{\"IpAddress\":\"*************\",\"HostName\":\"testserver\"}",
        LicenseId = Guid.NewGuid().ToString(),
        LicenseKey = "TEST-LICENSE-KEY",
        Version = "1.0",
        ExceptionMessage = "",
        FormVersion = "1.0",
        IsAttached = false
    };

    private void SetupValidMocks()
    {
        // Setup server name unique check (should return false for update - name doesn't exist for other servers)
        _mockServerRepository.Setup(x => x.IsServerNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Setup license expired check
        _mockLicenseValidationService.Setup(x => x.IsLicenseExpired(It.IsAny<string>()))
            .ReturnsAsync(true);
        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
    .ReturnsAsync(new ContinuityPatrol.Domain.Entities.Server
    {
        ReferenceId = Guid.NewGuid().ToString(),
        LicenseId = "dummy-license-id",
        RoleType = "Application",
        SiteId = Guid.NewGuid().ToString()
    });
        // Setup license active check
        var mockLicense = new Domain.Entities.LicenseManager
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IsState = true,
            IsActive = true,
            ExpiryDate = DateTime.Now.AddMonths(6).ToString("dd MMMM yyyy")
        };
        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockLicense);

        // Setup site and site type
        var mockSite = new Domain.Entities.Site
        {
            ReferenceId = Guid.NewGuid().ToString(),
            TypeId = Guid.NewGuid().ToString(),
            IsActive = true
        };
        _mockSiteRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSite);

        var mockSiteType = new Domain.Entities.SiteType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(mockSiteType);

        _mockSiteTypeRepository.Setup(x => x.GetSiteTypeIndexByIdAsync(It.IsAny<string>()))
            .ReturnsAsync(1);

        // Setup server count and license validation
        _mockServerRepository.Setup(x => x.GetServerCountByLicenseKey(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(5);

        _mockLicenseValidationService.Setup(x => x.IsServerLicenseCountExitMaxLimit(
            It.IsAny<Domain.Entities.LicenseManager>(), It.IsAny<Domain.Entities.SiteType>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(false);

        // Setup IP and hostname validation
        _mockLicenseValidationService.Setup(x => x.IsIpAddressValidAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        _mockLicenseValidationService.Setup(x => x.IsHostNameValidAsync(It.IsAny<string>()))
            .ReturnsAsync(true);

        // Setup database view repository for server attachment check
        _mockDatabaseViewRepository.Setup(x => x.GetDatabaseByServerId(It.IsAny<string>()))
            .ReturnsAsync(new List<DatabaseView>());
    }

    [Fact]
    public async Task Should_Pass_For_Valid_Command()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
    }

    [Theory]
    [InlineData("LicenseKey")]
    [InlineData("Name")]
    [InlineData("SiteName")]
    [InlineData("ServerType")]
    [InlineData("OSType")]
    [InlineData("RoleType")]
    public async Task Should_Fail_When_Required_Field_Is_Empty(string property)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, string.Empty);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == property);
    }

    [Theory]
    [InlineData("LicenseKey")]
    [InlineData("Name")]
    [InlineData("SiteName")]
    [InlineData("ServerType")]
    [InlineData("OSType")]
    [InlineData("RoleType")]
    public async Task Should_Fail_When_Required_Field_Is_Null(string property)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, null);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == property);
    }

    [Theory]
    [InlineData("Name", "AB")] // Too short
    //[InlineData("Name", new string('A', 101))] // Too long
    public async Task Should_Fail_When_Name_Length_Is_Invalid(string property, string value)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, value);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == property);
    }

    [Theory]
    [InlineData("Name", "Test@Server")] // Invalid characters
    [InlineData("Name", "Test#Server")]
    [InlineData("Name", "Test$Server")]
    [InlineData("SiteName", "Site@123")]
    [InlineData("ServerType", "Type@123")]
    [InlineData("OSType", "OS@Type")]
    [InlineData("RoleType", "Role@Type")]
    public async Task Should_Fail_When_Field_Contains_Invalid_Characters(string property, string value)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, value);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.PropertyName == property);
    }


    [Fact]
    public async Task Should_Fail_When_Server_Name_Already_Exists_For_Different_Server()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        _mockServerRepository.Setup(x => x.IsServerNameExist(command.Name, command.Id))
            .ReturnsAsync(true); // Name exists for different server

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage == "A same name already exists.");
    }



    [Fact]
    public async Task Should_Fail_When_License_Is_Inactive()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        var inactiveLicense = new Domain.Entities.LicenseManager
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IsState = false, // Inactive license
            IsActive = true,
            ExpiryDate = DateTime.Now.AddMonths(6).ToString("dd MMMM yyyy")
        };
        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(It.IsAny<string>()))
            .ReturnsAsync(inactiveLicense);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage == "License is in 'InActive' state");
    }



    [Theory]
    [InlineData("Id")]
    [InlineData("SiteId")]
    [InlineData("RoleTypeId")]
    [InlineData("ServerTypeId")]
    [InlineData("BusinessServiceId")]
    [InlineData("OSTypeId")]
    [InlineData("LicenseId")]
    public async Task Should_Fail_When_Guid_Field_Is_Invalid(string property)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, "invalid-guid");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("is not valid format");
    }

    [Theory]
    [InlineData("Id")]
    [InlineData("SiteId")]
    [InlineData("RoleTypeId")]
    [InlineData("ServerTypeId")]
    [InlineData("BusinessServiceId")]
    [InlineData("OSTypeId")]
    [InlineData("LicenseId")]
    public async Task Should_Fail_When_Guid_Field_Is_Empty_Guid(string property)
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        typeof(UpdateServerCommand).GetProperty(property)?.SetValue(command, Guid.Empty.ToString());

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidArgumentException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("is not valid format");
    }

    [Fact]
    public async Task Should_Fail_When_IP_Address_Is_Invalid()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        command.Properties = "{\"IpAddress\":\"invalid-ip\",\"HostName\":\"testserver\"}";

        // Fix: Setup for GetByReferenceIdAsync
        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(new ContinuityPatrol.Domain.Entities.Server
            {
                ReferenceId = command.Id,
                LicenseId = command.LicenseId,
                RoleType = command.RoleType,
                SiteId = command.SiteId
            });

        _mockLicenseValidationService.Setup(x => x.IsIpAddressValidAsync("invalid-ip"))
            .ReturnsAsync(false);

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage == "Please enter valid IP address.");
    }


    [Fact]
    public async Task Should_Fail_When_Site_Not_Found()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        _mockSiteRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.Site)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("Site");
    }

    [Fact]
    public async Task Should_Fail_When_Site_Is_Inactive()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        var inactiveSite = new Domain.Entities.Site
        {
            ReferenceId = Guid.NewGuid().ToString(),
            TypeId = Guid.NewGuid().ToString(),
            IsActive = false
        };
        _mockSiteRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(inactiveSite);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("Site");
    }

    [Fact]
    public async Task Should_Fail_When_SiteType_Not_Found()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.SiteType)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("SiteType");
    }

    [Fact]
    public async Task Should_Fail_When_SiteType_Is_Inactive()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        var inactiveSiteType = new Domain.Entities.SiteType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = false
        };
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync(inactiveSiteType);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(
            () => _validator.ValidateAsync(command));
        exception.Message.Should().Contain("SiteType");
    }


    [Fact]
    public async Task Should_Pass_When_All_Validations_Are_Successful()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();


    }

    [Fact]
    public async Task Should_Handle_License_Validation_When_RoleType_Is_Not_Database()
    {
        // Arrange
        var command = GetValidCommand();
        command.RoleType = "Application"; // Non-database role
        SetupValidMocks();

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();

        // Verify license count validation was called
        _mockLicenseValidationService.Verify(x => x.IsServerLicenseCountExitMaxLimit(
            It.IsAny<Domain.Entities.LicenseManager>(), It.IsAny<Domain.Entities.SiteType>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()),
            Times.Once);
    }

    [Fact]
    public async Task Should_Skip_License_Count_Validation_When_RoleType_Is_Database()
    {
        // Arrange
        var command = GetValidCommand();
        command.RoleType = "Database"; // Database role
        SetupValidMocks();

        // Setup empty database list so server is not attached
        _mockDatabaseViewRepository.Setup(x => x.GetDatabaseByServerId(command.Id))
            .ReturnsAsync(new List<DatabaseView>());

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();


    }
    [Fact]
    public async Task IsLicenseActiveAsync_Should_ReturnFalse_When_LicenseId_Is_NullOrWhiteSpace()
    {
        // Arrange
        var mockLicenseManagerRepo = new Mock<ILicenseManagerRepository>();
        var mockServerRepository = new Mock<IServerRepository>();
        var mockSiteRepository = new Mock<ISiteRepository>();
        var mockLicenseValidationService = new Mock<ILicenseValidationService>();
        var mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
        var mockDatabaseViewRepo = new Mock<IDatabaseViewRepository>();

        var validator = new UpdateServerCommandValidator(
            mockServerRepository.Object,
            mockLicenseManagerRepo.Object,
            mockSiteRepository.Object,
            mockLicenseValidationService.Object,
            mockSiteTypeRepository.Object,
            mockDatabaseViewRepo.Object);

        var command = new UpdateServerCommand
        {
            LicenseId = "   ", // whitespace input to trigger the branch
            Id = "dummy",
            SiteId = "dummy",
            RoleType = "dummy",
            Properties = "{}"
        };

        // Act
        var result = await validator.IsLicenseActiveAsync(command, CancellationToken.None);

        // Assert
        result.Should().BeFalse(); // This confirms the 'return false' line is covered
    }
    [Fact]
    public async Task ValidateLicenseCountAsync_Should_ReturnFalse_When_LicenseId_IsNullOrWhiteSpace()
    {
        // Arrange
        var mockServerRepo = new Mock<IServerRepository>();
        var mockLicenseManagerRepo = new Mock<ILicenseManagerRepository>();
        var mockSiteRepo = new Mock<ISiteRepository>();
        var mockLicenseValidationService = new Mock<ILicenseValidationService>();
        var mockSiteTypeRepo = new Mock<ISiteTypeRepository>();
        var mockDatabaseViewRepo = new Mock<IDatabaseViewRepository>();

        var validator = new UpdateServerCommandValidator(
            mockServerRepo.Object,
            mockLicenseManagerRepo.Object,
            mockSiteRepo.Object,
            mockLicenseValidationService.Object,
            mockSiteTypeRepo.Object,
            mockDatabaseViewRepo.Object);

        var command = new UpdateServerCommand
        {
            LicenseId = "   ", // whitespace
            Id = "dummy-id",
            SiteId = "dummy-site",
            RoleType = "role",
            Properties = "{}"
        };

        // Access private method via reflection
        var method = typeof(UpdateServerCommandValidator).GetMethod("ValidateLicenseCountAsync",
            BindingFlags.NonPublic | BindingFlags.Instance);

        method.Should().NotBeNull("ValidateLicenseCountAsync must exist as a private method");

        // Act
        var task = (Task<bool>)method.Invoke(validator, new object[] { command, CancellationToken.None });
        var result = await task;

        // Assert
        result.Should().BeFalse("Method should return false when LicenseId is null or whitespace");
    }
 
    [Fact]
    public async Task Should_Fail_With_Custom_Message_When_ServerIsAttachedWithDatabase_And_RoleTypeChanged()
    {
        // Arrange
        var command = GetValidCommand();
        command.RoleType = "Application"; // New role type

        var server = new Domain.Entities.Server
        {
            ReferenceId = command.Id,
            RoleType = "Database", // Existing role is "Database"
            SiteId = command.SiteId,
            LicenseId = command.LicenseId
        };
        SetupValidMocks();

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(server);

        _mockDatabaseViewRepository.Setup(x => x.GetDatabaseByServerId(command.Id))
            .ReturnsAsync(new List<DatabaseView> { new DatabaseView { Name = "AttachedDb" } });

        // Ensure all other rules pass

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e =>
            e.ErrorMessage == "Server is attached with 'AttachedDb' database.");
    }

    [Fact]
    public async Task Should_ReturnFalse_When_ServerIsDatabaseAndRoleTypeChanged_And_DatabaseAttached()
    {
        // Arrange
        var command = GetValidCommand();
        command.RoleType = "Application"; // incoming command says Application

        var server = new ContinuityPatrol.Domain.Entities.Server
        {
            ReferenceId = command.Id,
            RoleType = "Database", // actual server is a Database
            SiteId = command.SiteId,
            LicenseId = command.LicenseId
        };

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(server);

        _mockDatabaseViewRepository.Setup(x => x.GetDatabaseByServerId(command.Id))
            .ReturnsAsync(new List<DatabaseView> {
            new DatabaseView { Name = "SampleDb" } // attached DB
            });

        var method = typeof(UpdateServerCommandValidator)
            .GetMethod("IsServerAttachedWithDataBase", BindingFlags.NonPublic | BindingFlags.Instance);

        method.Should().NotBeNull("Validator should contain IsServerAttachedWithDataBase method");

        // Act
        var task = (Task<bool>)method.Invoke(_validator, new object[] { command, CancellationToken.None });
        var result = await task;

        // Assert
        result.Should().BeFalse("Server is attached with a database and role type changed");
    }
    [Fact]
    public async Task Should_ReturnTrue_When_ServerIsNotDatabaseOrRoleTypeNotChanged()
    {
        // Arrange
        var command = GetValidCommand();
        command.RoleType = "Database"; // incoming command matches existing role

        var server = new ContinuityPatrol.Domain.Entities.Server
        {
            ReferenceId = command.Id,
            RoleType = "Database",
            SiteId = command.SiteId,
            LicenseId = command.LicenseId
        };

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync(server);

        var method = typeof(UpdateServerCommandValidator)
            .GetMethod("IsServerAttachedWithDataBase", BindingFlags.NonPublic | BindingFlags.Instance);

        method.Should().NotBeNull("Validator should contain IsServerAttachedWithDataBase method");

        // Act
        var task = (Task<bool>)method.Invoke(_validator, new object[] { command, CancellationToken.None });
        var result = await task;

        // Assert
        result.Should().BeTrue("No change in role type; should return true");
    }
    [Fact]
    public async Task Should_ReturnTrue_When_Server_Is_Null()
    {
        // Arrange
        var command = GetValidCommand();

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id))
            .ReturnsAsync((ContinuityPatrol.Domain.Entities.Server)null);

        var method = typeof(UpdateServerCommandValidator)
            .GetMethod("IsServerAttachedWithDataBase", BindingFlags.NonPublic | BindingFlags.Instance);

        // Act
        var task = (Task<bool>)method.Invoke(_validator, new object[] { command, CancellationToken.None });
        var result = await task;

        // Assert
        result.Should().BeTrue("Server not found, should return true");
    }
    [Fact]
    public async Task Should_ThrowInvalidOperationException_When_License_IsNotExpired_And_FieldsMatch()
    {
        var command = new UpdateServerCommand
        {
            LicenseId = "valid",
            RoleType = "App",
            SiteId = "site1",
            Id = "server1"
        };

        var server = new Domain.Entities.Server { LicenseId = "valid", RoleType = "App", SiteId = "site1" };
        var license = new Domain.Entities.LicenseManager { ExpiryDate = DateTime.UtcNow.AddDays(30).ToString() };

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id)).ReturnsAsync(server);
        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(command.LicenseId)).ReturnsAsync(license);
        _mockLicenseValidationService.Setup(x => x.IsLicenseExpired(license.ExpiryDate)).ReturnsAsync(false);

        var method = typeof(UpdateServerCommandValidator)
            .GetMethod("ValidateLicenseCountAsync", BindingFlags.NonPublic | BindingFlags.Instance);

        var act = async () =>
        {
            var task = (Task<bool>)method.Invoke(_validator, new object[] { command, CancellationToken.None });
            await task;
        };

        await Assert.ThrowsAsync<InvalidOperationException>(act);
    }

    [Fact]
    public async Task Should_ThrowNotFoundException_When_Site_Is_NullOrInactive()
    {
        var command = new UpdateServerCommand
        {
            LicenseId = "valid",
            RoleType = "App",
            SiteId = "site1",
            Id = "server1"
        };

        var server = new Domain.Entities.Server { LicenseId = "diff", RoleType = "App", SiteId = "diff" };
        var license = new Domain.Entities.LicenseManager { ExpiryDate = DateTime.UtcNow.ToString() };
        Domain.Entities.Site nullSite = null;

        _mockServerRepository.Setup(x => x.GetByReferenceIdAsync(command.Id)).ReturnsAsync(server);
        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(command.LicenseId)).ReturnsAsync(license);
        _mockSiteRepository.Setup(x => x.GetByReferenceIdAsync(command.SiteId)).ReturnsAsync(nullSite);

        var method = typeof(UpdateServerCommandValidator)
            .GetMethod("ValidateLicenseCountAsync", BindingFlags.NonPublic | BindingFlags.Instance);

        var act = async () =>
        {
            var task = (Task<bool>)method.Invoke(_validator, new object[] { command, CancellationToken.None });
            await task;
        };

        await Assert.ThrowsAsync<NotFoundException>(act);
    }




    [Fact]
    public async Task Should_Pass_When_IP_Is_NA()
    {
        // Arrange
        var command = GetValidCommand();
        SetupValidMocks();
        command.Properties = "{\"IpAddress\":\"NA\",\"HostName\":\"testserver\"}";

        // Act
        var result = await _validator.ValidateAsync(command);

        // Assert
        result.IsValid.Should().BeFalse(); // Should pass as IP = "NA" is considered valid
    }
}