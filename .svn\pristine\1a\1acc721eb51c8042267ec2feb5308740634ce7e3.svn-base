﻿using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Events.PaginatedView;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class DataSyncPropertiesController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<DataSyncPropertiesController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public DataSyncPropertiesController(IPublisher publisher, ILogger<DataSyncPropertiesController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;

    }

    [AntiXss]
    [EventCode(EventCodes.DataSyncProperties.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in DataSyncProperties");

        await _publisher.Publish(new DataSyncOptionsPaginatedEvent());

        return View();
    }

    [HttpGet]
    [EventCode(EventCodes.DataSyncProperties.GetPaginated)]
    public async Task<JsonResult> GetPaginated(GetDataSyncOptionsPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginated method in DataSyncProperties");

        try
        {
            return Json(await _dataProvider.DataSync.GetPaginatedDataSyncs(query));

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on datasync page while processing the pagination request. {ex.GetJsonException()}");

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.DataSyncProperties.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(DataSyncOptionsViewModel dataSyncs)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in DataSyncProperties");

        var dataSyncId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (dataSyncId.IsNullOrWhiteSpace())
            {

                var createCommand = _mapper.Map<CreateDataSyncOptionsCommand>(dataSyncs);

                _logger.LogDebug($"Creating DataSync '{createCommand.Name}'");

                result = await _dataProvider.DataSync.CreateAsync(createCommand);

                //  TempData.NotifySuccess(response.Message);
            }
            else
            {

                var updateCommand = _mapper.Map<UpdateDataSyncOptionsCommand>(dataSyncs);

                _logger.LogDebug($"Updating DataSync '{updateCommand.Name}'");

                result = await _dataProvider.DataSync.UpdateAsync(updateCommand);

                //   TempData.NotifySuccess(response.Message);
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in DataSyncProperties, returning view.");

            return Json(new { Success = true, data = result });
        }
        //catch (ValidationException ex)
        //{
        //    _logger.LogError($"Validation error on datasync page: {ex.ValidationErrors.FirstOrDefault()}");

        //    TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

        //    return RedirectToAction("List");
        //}
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on datasync page while processing the request for create or update.", ex);

            //TempData.NotifyWarning(ex.GetMessage());

            return ex.GetJsonException();
        }
    }


    [EventCode(EventCodes.DataSyncProperties.Delete)]
    public async Task<IActionResult> DeleteAsync(string id)
    {
        _logger.LogDebug("Entering DeleteAsync method in DataSyncProperties");

        try
        {
            var result = await _dataProvider.DataSync.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in datasync properties");

            TempData.NotifySuccess(result.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on datasync properties.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }

    [HttpGet]
    [EventCode(EventCodes.DataSyncProperties.IsDataSyncNameExist)]
    public async Task<bool> IsDataSyncNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsDataSyncNameExist method in DataSyncProperties");

        try
        {
            var nameExist = await _dataProvider.DataSync.IsDataSyncNameExist(name, id);

            _logger.LogDebug("Returning result for IsDataSyncNameExist on datasync properties");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on datasync properties while checking if datasync name exists for : {name}.", ex);

            return false;
        }
    }


}