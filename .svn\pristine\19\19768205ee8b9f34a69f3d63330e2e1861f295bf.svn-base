using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class GlobalVariableService : IGlobalVariableService
{
    private readonly IBaseClient _client;
    public GlobalVariableService(IBaseClient client)
    {
        _client = client;
    }
   
    public async Task<List<GlobalVariableListVm>> GetGlobalVariableList()
    {
        var request = new RestRequest("api/v6/globalvariables");

        return await _client.GetFromCache<List<GlobalVariableListVm>>(request, "GetGlobalVariableList");
    }

    public async Task<BaseResponse> CreateAsync(CreateGlobalVariableCommand createGlobalVariableCommand)
    {
        var request = new RestRequest("api/v6/globalvariables", Method.Post);

        request.AddJsonBody(createGlobalVariableCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateGlobalVariableCommand updateGlobalVariableCommand)
    {
        var request = new RestRequest("api/v6/globalvariables", Method.Put);

        request.AddJsonBody(updateGlobalVariableCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/globalvariables/{id}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<GlobalVariableDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/globalvariables/{id}");

        return await _client.Get<GlobalVariableDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsGlobalVariableNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/globalvariables/name-exist?globalvariableName={name}&id={id}");

     return await _client.Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<GlobalVariableListVm>> GetPaginatedGlobalVariables(GetGlobalVariablePaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/globalvariables/paginated-list");

      return await _client.Get<PaginatedResult<GlobalVariableListVm>>(request);
  }

    public async  Task<List<GlobalVariableDetailVm>> GetByVariableName(string name)
    {
        var request = new RestRequest($"api/v6/globalvariables/variablename/{name}");

        return await _client.Get<List<GlobalVariableDetailVm>>(request);
    }


    #endregion
}
