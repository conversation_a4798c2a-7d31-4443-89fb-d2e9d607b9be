using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Delete;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetList;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetNameUnique;
using ContinuityPatrol.Services.Db.Impl.Configuration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Configuration;

public class HacmpClusterServiceTests : BaseServiceTestSetup<HacmpClusterService>, IClassFixture<HacmpClusterFixture>
{
    private readonly HacmpClusterFixture _fixture;

    public HacmpClusterServiceTests(HacmpClusterFixture fixture)
    {
        InitializeService(accessor => new HacmpClusterService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetHacmpClusterList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetHacmpClusterListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.HacmpClusterListVm);

        var result = await ServiceUnderTest.GetHacmpClusterList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var hacmpClusterId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetHacmpClusterDetailQuery>(q => q.Id == hacmpClusterId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.HacmpClusterDetailVm);

        var result = await ServiceUnderTest.GetByReferenceId(hacmpClusterId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.HacmpClusterDetailVm.Id, result.Id);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateHacmpClusterResponse
        {
            Message = "Created",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateHacmpClusterCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateHacmpClusterCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateHacmpClusterResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateHacmpClusterCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateHacmpClusterCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var hacmpClusterId = Guid.NewGuid().ToString();
        var response = new DeleteHacmpClusterResponse
        {
            Message = "Deleted",
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteHacmpClusterCommand>(c => c.Id == hacmpClusterId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(hacmpClusterId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Theory]
    [InlineData("HacmpClusterName1", null)]
    [InlineData("AnotherName", "some-guid")]
    public async Task IsHacmpClusterNameExist_Should_Return_True(string name, string? id)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetHacmpClusterNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsHacmpClusterNameExist(name, id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedHacmpClusters_Should_Return_Result()
    {
        var query = _fixture.GetHacmpClusterPaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedHacmpClusterListVm);

        var result = await ServiceUnderTest.GetPaginatedHacmpClusters(query);

        Assert.Equal(_fixture.HacmpClusterListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetByReferenceId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetByReferenceId(null!));
    }

    [Fact]
    public async Task GetByReferenceId_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetByReferenceId(""));
    }

    [Fact]
    public async Task IsHacmpClusterNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.IsHacmpClusterNameExist(null!, null));
    }

    [Fact]
    public async Task IsHacmpClusterNameExist_EmptyName_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.IsHacmpClusterNameExist("", null));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }
}
