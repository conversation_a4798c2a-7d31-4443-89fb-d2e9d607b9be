﻿using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Validators;

public class UpdateDataSetValidatorTests
{
    public List<Domain.Entities.DataSet> DataSets { get; set; }

    private readonly Mock<IDataSetRepository> _mockDataSetRepository;

    private UpdateDataSetCommandValidator validator;

    public UpdateDataSetValidatorTests()
    {
        DataSets = new Fixture().Create<List<Domain.Entities.DataSet>>();

        _mockDataSetRepository = DataSetRepositoryMocks.UpdateDataSetRepository(DataSets);
    }

    //DataSetName

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_DataSetName_InDataSet_WithEmpty(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_DataSetName_InDataSet_IsNull(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = null;

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_DataSetName_InDataSet_MinimumRange(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "BA";
            
        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_UpdateDataSetCommandValidator_DataSetName_MaximumRange(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "ZYXWVUTSRQPONMLKJIHGFEDCBA_ZYXWVUTSRQPONMLKJIHGFEDCBA_ZYXWVUTSRQPONMLKJIHGFEDCBA_ZYXWVUTSRQPONMLKJIHGFEDCBA";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Data Set Name should contain between 3 to 100 characters.");
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "   PPTS  ";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_DoubleSpace_InFront(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "  PPTS";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_DoubleSpace_InBack(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "PPTS  ";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_TripleSpace_InBetween(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "PTS   India";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_SpecialCharacters_InFront(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "$%^&PTS India";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_SpecialCharacters_InBetween(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "PTS@#$%^&India";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_SpecialCharacters_Only(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "@#$@%&$%";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_UnderScore_InFront(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);
        updateDataSetCommand.DataSetName = "_PPTS";
        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_UnderScore_InFront_AndBack(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "_PPTS_";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_Numbers_InFront(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "746PPTS";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "_746PPTS_";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_UnderScore_InFront_AndNumbers_InBack(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "_PPTS765";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoDataSetData]
    public async Task Verify_Update_Valid_Name_InDataSet_With_Numbers_Only(UpdateDataSetCommand updateDataSetCommand)
    {
        validator = new UpdateDataSetCommandValidator(_mockDataSetRepository.Object);

        updateDataSetCommand.DataSetName = "2346479647";

        var validateResult = await validator.ValidateAsync(updateDataSetCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.DataSet.DataSetNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }
}