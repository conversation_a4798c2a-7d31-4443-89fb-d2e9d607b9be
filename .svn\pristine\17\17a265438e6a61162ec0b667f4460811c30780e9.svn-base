let bulkImportObject = "", infraObjectIndex = "", validationResponse = "", validatorResponse = "";
let bulkImportOperationStatus;
let allowInsertToDB = true;

const requiredKeysServer = ["name", "siteName", "businessServiceName", "serverRole", "serverType", "osType", "licenseKey", "osVersion", "formVersion"];
const requiredKeysDatabase = ["name", "databaseType", "businessServiceName", "databaseVersion", "serverName", "licenseKey", "exceptionMessage", "formVersion"];
const requiredKeysReplication = ["name", "siteName", "businessServiceName", "licenseKey", "replicationType", "formVersion"];

$(async function () {
    $("#insertBulkImport").removeClass('d-none');
    $("#CompleteBulkImport").addClass('d-none');

    const dropzone = document?.getElementById('dropzone');
    const fileInput = document?.getElementById('file-input');

    dropzone.addEventListener('dragover', (e) => {
        e?.preventDefault();
        e?.stopPropagation();
    });

    dropzone.addEventListener('drop', (e) => {
        e?.preventDefault();
        e?.stopPropagation();
        const file = e?.dataTransfer?.files[0];
        handleFiles(file);
    });

    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            $("#insertBulkImport").removeClass('d-none');
            $("#CompleteBulkImport").addClass('d-none');
            handleFiles(e?.target?.files[0]);
        });
    }

    setupValidation("#PRServerName", "Configuration/Server/IsServerNameExist", "#PRServerNameError", "Enter server name", "serverName");
    setupValidation("#DRServerName", "Configuration/Server/IsServerNameExist", "#DRServerNameError", "Enter server name", "serverName");
    setupValidation("#PRDatabaseName", "Configuration/Database/IsDatabaseNameExist", "#PRDatabaseNameError", "Enter Database name", "databaseName");
    setupValidation("#DRDatabaseName", "Configuration/Database/IsDatabaseNameExist", "#DRDatabaseNameError", "Enter Database name", "databaseName");
    setupValidation("#PRReplicationName", "Configuration/Replication/IsReplicationNameExist", "#PRReplicationNameError", "Enter Replication name", "replicationName");
    setupValidation("#DRReplicationName", "Configuration/Replication/IsReplicationNameExist", "#DRReplicationNameError", "Enter Replication name", "replicationName");
    setupValidation("#InfraObjectName", "Configuration/InfraObject/IsInfraObjectNameExist", "#InfraObjectNameError", "Enter InfraObject name", "infraObjectName");

    $("#completeAction").hide();
    runningStatus();

    $("#flexCheckDefault").on("change", function () {
        let isChecked = $(this)?.prop("checked");
        $(".checkBoxBulkImport").prop("checked", isChecked);
    });

    $('#validationLists').on('click', '.delete-Row', function () {
        let infraName = $(this)?.data("infra-name");
        let rowIndex = $(this)?.data("row-id");
        $("#bulkImportIndex")?.val(rowIndex);
        $("#deleteData")?.text(infraName)?.attr("title", infraName);
    });

    $("#confirmBulkDeleteButton").on("click", function () {
        const checkboxes = document.querySelectorAll('.checkBoxBulkImport');
        const selectedValues = [...checkboxes].filter(checkbox => checkbox?.checked)
            .map(checkbox => parseInt(checkbox?.value, 10)).sort((a, b) => b - a);

        if (selectedValues?.length > 0 && selectedValues?.length < bulkImportObject?.bulkImportOperationList?.length) {
            selectedValues.forEach(index => {
                bulkImportObject?.bulkImportOperationList?.splice(index, 1);
            });
        }
        bulkImportValidation("validate");
    });

    $("#confirmDeleteButton").on('click', function () {
        let rowIndex = Number($("#bulkImportIndex").val());
        if (rowIndex >= 0 && rowIndex < bulkImportObject?.bulkImportOperationList?.length) {
            bulkImportObject?.bulkImportOperationList?.splice(rowIndex, 1); 
        }
        bulkImportValidation("validate");
    })

});

// bulk import onclick functions
// upload filed save function

$("#confirmSave").on("click", function () {
    let value = $("#bulkimportDescription").val();
    let result = descriptionValidation(value);

    if (result && hasErrorInTbodyRows()) {

        $("#totalInfra, #successInfra, #errorInfra").html(0);
        $("#addDescription, #offcanvasTop").modal("hide");
        $("#InsetDBModal").modal("show");

        bulkImportObject.description = value;

        const checkboxes = document?.querySelectorAll('.checkBoxBulkImport');
        const selectedValues = [...checkboxes]?.filter(checkbox => checkbox?.checked)?.map(checkbox => parseInt(checkbox?.value, 10));
        $("#bulkimportDescription").val("");

        // Update the bulkImportOperationList to keep only selected values
        if (selectedValues?.length) {
            bulkImportObject.bulkImportOperationList = bulkImportObject?.bulkImportOperationList?.length && bulkImportObject?.bulkImportOperationList?.filter((_, index) =>
                selectedValues?.includes(index)
            );
        }

        let $ul = $('#bulkImportLists')?.empty();
        let $timeLine = $('#bulkImportTimeLine')?.empty();

        $("#totalInfra").html(bulkImportObject?.bulkImportOperationList?.length);

        bulkImportObject?.bulkImportOperationList?.length && bulkImportObject?.bulkImportOperationList?.forEach(function (data, index) {
            let htmlDesign = `<li class="list-group-item border-top d-flex justify-content-between align-items-center">
                     <div class="d-flex">
                         <i class="cp-idea Skipped_Paused me-2"></i>
                         <span class="fw-bold text-truncate" style="width:150px" title="${data?.infraObject?.name}">${data?.infraObject?.name}</span>
                     </div>
                     <div>
                     <span id="actionName" class="text-truncate d-inline-block" style="max-width:90%"></span>
                         <div class="d-flex align-items-center gap-1">
                             <i class="cp-thunder"></i>
                                 <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                                     <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%;"></div>
                                 </div>
                                 <span class="text-truncate d-inline-block" style="width:50px">Pending</span><span class="ms-2">0/9</span>
                         </div>
                     </div>
                     <div class="btn-group-sm">
                         <button type="button" disabled class='btn btn btn-outline-secondary border-0'><i class="cp-circle-playnext fs-6 me-2"></i><span class="align-middle">Next</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-reload fs-6 me-2"></i><span class="align-middle">Rollback</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-disable fs-6 me-2"></i><span class="align-middle">Abort</span></button>
                     </div>
                 </li>`
            $ul.append(htmlDesign);
        });

        $timeLine.append(`<li><span style = "text-align: center;"> 
        <img src="/img/isomatric/no_data_found.svg" style="width: auto; height: 200px;" alt="No Data"></span>
        </li>`);
    }
});

$("#insertBulkImport").on("click", function () {
    if (allowInsertToDB) {
        $("#insertBulkImport").addClass('d-none');
        insertBulkImportToDB();
    }
});

$("#closeInsertDBModal").on("click", function () {
    $('#closeOffCanvas').trigger('click');
});

$("#insertToDB").on("click", function () {
    let value = $("#bulkimportDescription").val();
    let result = descriptionValidation(value);
    if (result) {
        $("#totalInfra").html(0);
        $("#successInfra").html(0);
        $("#errorInfra").html(0);
        $("#addDescription").modal("hide");
        $("#offcanvasTop").modal("hide");
        $("#InsetDBModal").modal("show");

        let value = $("#bulkimportDescription").val();
        bulkImportObject.description = value;
        const checkboxes = document.querySelectorAll('.checkBoxBulkImport');
        const selectedValues = [...checkboxes]
            .filter(checkbox => checkbox.checked)
            .map(checkbox => parseInt(checkbox.value, 10));
        $("#bulkimportDescription").val("");

        // Update the bulkImportOperationList to keep only selected values
        if (selectedValues.length > 0) {
            bulkImportObject.bulkImportOperationList = bulkImportObject.bulkImportOperationList.filter((_, index) =>
                selectedValues.includes(index)
            );
        }

        let $ul = $('#bulkImportLists');
        $ul.empty();
        let $timeLine = $('#bulkImportTimeLine');
        $timeLine.empty();

        $("#totalInfra").html(bulkImportObject?.bulkImportOperationList?.length);

        bulkImportObject?.bulkImportOperationList?.forEach(function (data, index) {
            let htmlDesign = `<li class="list-group-item border-top d-flex justify-content-between align-items-center">
                     <div class="d-flex">
                         <i class="cp-idea Skipped_Paused me-2"></i>
                         <span class="fw-bold text-truncate" style="width:150px" title="${data?.infraObject?.name}">${data?.infraObject?.name}</span>
                     </div>
                     <div>
                     <span id="actionName" class="text-truncate d-inline-block" style="max-width:90%"></span>
                         <div class="d-flex align-items-center gap-1">
                             <i class="cp-thunder"></i>
                                 <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                                     <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%;"></div>
                                 </div>
                                 <span class="text-truncate d-inline-block" style="width:50px">Pending</span><span class="ms-2">0/9</span>
                         </div>
                     </div>
                     <div class="btn-group-sm">
                         <button type="button" disabled class='btn btn btn-outline-secondary border-0'><i class="cp-circle-playnext fs-6 me-2"></i><span class="align-middle">Next</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-reload fs-6 me-2"></i><span class="align-middle">Rollback</span></button>
                         <button type="button" disabled class="btn btn btn-outline-secondary border-0"><i class="cp-disable fs-6 me-2"></i><span class="align-middle">Abort</span></button>
                     </div>
                 </li>`
            $ul.append(htmlDesign);
        });

        let $litime = $('<li></li>');
        $litime.append(`<span style = "text-align: center;"> <img src="/img/isomatric/no_data_found.svg" style="width: auto; height: 200px;" alt="No Data"></span>`);
        $timeLine.append($litime);
    }
});

$('#saveServerList').on("click", function () {
    saveEditedServerData();
});

$('#saveDatabaseList').on("click", function () {
    saveEditedDatabaseData();
});

$('#saveReplicationList').on("click", function () {
    saveEditedReplicationData();
});

$('#saveInfraObjectList').on("click", function () {
    saveEditedInfraObjectData();
});

$("#closeOffCanvas").on("click", function () {
    bulkImportObject = "";
    validationResponse = "";
    removeUploadedFile();
});

// error validation in table function

function hasErrorInTbodyRows() {
    let hasNoError = true;
    const $rows = $('#validationLists tr');
    const $checkedRows = $rows.filter(function () {
        return $(this)?.find('.checkBoxBulkImport')?.is(':checked');
    });

    const rowsToValidate = $checkedRows.length ? $checkedRows : $rows;

    rowsToValidate.each(function () {
        const $tds = $(this).find('td');
        const targetTds = [$tds.eq(3), $tds.eq(4), $tds.eq(5), $tds.eq(6)];

        for (const $td of targetTds) {
            if ($td.find('.cp-error')?.length) {
                hasNoError = false;
                notificationAlert('warning', 'Fix the selected row or all row errors.');
                return false;
            }
        }
    });
    return hasNoError;
}

//upload file functionality
function handleFiles(file) {
    allowInsertToDB = true;
    bulkImportObject = { description: "string", infraObjectName: "string", bulkImportOperationList: [] };
    validationResponse = "";
    const filename = file?.name;

    if (!isXlsxFile(filename)) {
        $("#fileInput").val("");
        $("#drop-zone").css({ "font-weight": "bold" });
        setTimeout(() => $("#drop-zone").css({ "font-weight": "normal" }), 2000);
        return;
    }

    $("#fileName").html(filename);

    const reader = new FileReader();
    reader.onload = function (e) {
        try {
            const data = new Uint8Array(e?.target?.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook?.SheetNames[0];
            const worksheet = workbook?.Sheets[sheetName];
            const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            const nonEmptyRows = rows.filter(row =>
                row?.some(cell => (typeof cell === 'string' ? cell?.trim() !== '' : cell != null))
            );

            let updatedArray = replaceEmptyStrings(nonEmptyRows);
            let lastIndexValue = updatedArray[0][updatedArray[0]?.length - 1];
            let diff = updatedArray[1]?.length - updatedArray[0]?.length;
            if (diff > 0) {                    
                updatedArray[0] = updatedArray[0]?.concat(new Array(diff)?.fill(lastIndexValue));
            }          

            // Process all rows fast
            for (let i = 2; i < updatedArray?.length; i++) {
                const json = excelToJson(updatedArray, i, updatedArray?.length);
                bulkImportObject.bulkImportOperationList.push(json);
            }

            animateProgressBar(() => {
                bulkImportValidation();
            });

        } catch (error) { //console.error('Error reading Excel file:', error);
        }
    };

    reader.onerror = () => console.error('Error reading file');
    reader.readAsArrayBuffer(file);
}

function isXlsxFile(filename) {
    return filename?.toLowerCase()?.endsWith('.xls');
}

function replaceEmptyStrings(array) {
    let serviceLabel;
    for (let i = 0; i < array[0]?.length; i++) {
        let sub = array[0][i]
        if (sub !== undefined && sub !== null && sub?.trim() !== "") {
            serviceLabel = sub?.trim();
        } else {
            array[0][i] = serviceLabel;
        }
    }
    return array;
}

function animateProgressBar(onComplete) {
    const bar = document.getElementById('dynamicProgressBar');
    const label = $("#percentageProgressBar");
    let progress = 0;
    const target = 95;
    function update() {
        if (progress < target) {
            progress += 1;
            bar.style.width = `${progress}%`;
            label.html(`${progress}%`);
            requestAnimationFrame(update);
        } else {
            bar.style.width = `${target}%`;
            label.html(`${target}%`);
            if (onComplete) onComplete();
        }
    }

    requestAnimationFrame(update);
}

// excel to json functionality
function excelToJson(updatedArray, i, arrayLength) {
    let keyMaps = createKeyMaps();

    const serverProps = JSON.parse(keyMaps?.infraobjectconfiguration?.serverProperties);
    const dbProps = JSON.parse(keyMaps?.infraobjectconfiguration?.databaseProperties);
    const replProps = JSON.parse(keyMaps?.infraobjectconfiguration?.replicationProperties);
    const siteProps = JSON.parse(keyMaps?.infraobjectconfiguration?.siteProperties);

    for (let index = 0; index < updatedArray[0]?.length; index++) {
        const key = updatedArray[0][index];

        let sanitizedKey = key?.toLowerCase()?.replace(/\s+/g, '');
        let sanitizedValue = updatedArray[1][index]?.slice(0, 2)?.toLowerCase() + updatedArray[1][index]?.slice(2)?.replace(/\s+/g, '');
        let propertiesValue = updatedArray[1][index]?.replace(/\s+/g, '');
        let rowValue = updatedArray[i][index]
        let lowerMapped = sanitizedValue?.toLowerCase();

        if (["isswitchover", "isfailover", "isswitchback", "isfailback"].includes(lowerMapped)) {
            keyMaps[sanitizedValue] = rowValue === "true";
            continue;
        }

        if (["operationalServiceName", "operationalService"].includes(sanitizedValue)) {
            ["prserverconfiguration", "prdatabaseconfiguration", "prreplicationconfiguration", "drserverconfiguration",
                "drdatabaseconfiguration", "drreplicationconfiguration", "infraobjectconfiguration"].forEach(cfg => {
                    keyMaps[cfg].businessServiceName = rowValue;
                });
            continue;
        }

        if (sanitizedValue === "licenseKey") {
            ["prserverconfiguration", "prdatabaseconfiguration", "prreplicationconfiguration", "drserverconfiguration",
                "drdatabaseconfiguration", "drreplicationconfiguration"].forEach(cfg => {
                    keyMaps[cfg].licenseKey = rowValue;
                });
            continue;
        }

        if (sanitizedValue === "operationalFunction") {
            keyMaps["infraobjectconfiguration"]["businessFunctionName"] = rowValue;
        }

        if (keyMaps[sanitizedKey]) {

            if (sanitizedKey === "prserverconfiguration" || sanitizedKey === "drserverconfiguration") {
                if (!requiredKeysServer.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;

                } else {
                    key === "serverRole" ? "roleType" : key === "osVersion" ? "version" : key;
                    sanitizedValue = sanitizedValue === "serverRole" ? "roleType" : sanitizedValue === "osVersion" ? "version" : sanitizedValue;
                    rowValue = sanitizedValue === "version" ? (String(rowValue)?.split(" ")[1] || String(rowValue)?.split(" ")[0]) : rowValue;

                    if (sanitizedValue === "name") {

                        if (sanitizedKey === "prserverconfiguration") {
                            keyMaps.prdatabaseconfiguration.serverName = rowValue;
                            keyMaps.infraobjectconfiguration.prServerName = rowValue;
                            serverProps.PR.id = "@" + rowValue;
                            serverProps.PR.name = rowValue;
                        }

                        if (sanitizedKey === "drserverconfiguration") {
                            keyMaps.drdatabaseconfiguration.serverName = rowValue;
                            keyMaps.infraobjectconfiguration.drServerName = rowValue;
                            serverProps.DR.id = "@" + rowValue;
                            serverProps.DR.name = rowValue;
                        }
                    }

                    keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                }
            }

            if (sanitizedKey === "prdatabaseconfiguration" || sanitizedKey === "drdatabaseconfiguration") {

                if (!requiredKeysDatabase.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;
                } else {
                    sanitizedValue = sanitizedValue === "databaseVersion" ? "version" : sanitizedValue;
                    rowValue = sanitizedValue === "version" ? (String(rowValue)?.split(" ")[1] || String(rowValue)?.split(" ")[0]) : rowValue;

                    if (sanitizedValue === "name") {

                        if (sanitizedKey === "prdatabaseconfiguration") {
                            keyMaps.infraobjectconfiguration.prDatabaseName = rowValue;
                            dbProps.PR.id = "@" + rowValue;
                            dbProps.PR.name = rowValue;
                        }
                        if (sanitizedKey === "drdatabaseconfiguration") {
                            keyMaps.infraobjectconfiguration.drDatabaseName = rowValue;
                            dbProps.DR.id = "@" + rowValue;
                            dbProps.DR.name = rowValue;
                        }
                    }

                    if (sanitizedValue !== "serverName") keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                }
            }

            if (sanitizedKey === "prreplicationconfiguration" || sanitizedKey === "drreplicationconfiguration") {
                if (!requiredKeysReplication.includes(sanitizedValue)) {
                    rowValue = rowValue === "false" ? false : rowValue === "true" ? true : rowValue;
                    keyMaps[sanitizedKey].properties[propertiesValue] = rowValue;
                } else {
                    sanitizedValue = sanitizedValue === "replicationType" ? "type" : sanitizedValue;

                    if (sanitizedValue === "name") {

                        if (sanitizedKey === "prreplicationconfiguration") {
                            keyMaps.infraobjectconfiguration.prReplicationName = rowValue;
                            replProps.PR.id = "@" + rowValue;
                            replProps.PR.name = rowValue;
                        }
                        if (sanitizedKey === "drreplicationconfiguration") {
                            keyMaps.infraobjectconfiguration.drReplicationName = rowValue;
                            replProps.DR.id = "@" + rowValue;
                            replProps.DR.name = rowValue;
                        }
                    }

                    keyMaps[sanitizedKey][sanitizedValue] = rowValue;

                    if (sanitizedValue === "type" && !rowValue?.toLowerCase()?.includes("perpetuuiti")) {

                        ["prreplicationconfiguration", "drreplicationconfiguration"].forEach(r => {
                            keyMaps[r].licenseId = "NA";
                            keyMaps[r].licenseKey = "NA";
                        });
                    }
                }
            }

            if (sanitizedKey === "infraobjectconfiguration") {

                const relevantKeys = new Set([
                    "name", "description", "businessFunctionName", "businessServiceName", "activityType", "databaseType",
                    "priority", "isPair", "isAssociate", "siteTypePR", "siteTypeDR", "replicationCategoryType", "replicationType"
                ]);

                if (relevantKeys.has(sanitizedValue)) {

                    sanitizedValue = sanitizedValue === "activityType" ? "typeName" : sanitizedValue === "databaseType" ? "subType" :
                        sanitizedValue === "replicationType" ? "replicationTypeName" : sanitizedValue;
                    rowValue = rowValue === "false" ? false : rowValue === "true" ? true : rowValue;

                    if (sanitizedValue === "siteTypePR" || sanitizedValue === "siteTypeDR") {
                        const category = sanitizedValue === "siteTypePR" ? "Primary" : "DR";

                        getSiteID(rowValue).then(siteID => {
                            siteProps.push({
                                id: siteID?.data?.[0]?.id || "",
                                name: rowValue,
                                category: category
                            });

                            keyMaps.infraobjectconfiguration.siteProperties = JSON.stringify(siteProps);
                        });

                    } else {

                        if (rowValue !== 1 && rowValue !== 2 && rowValue !== 3 && rowValue !== false && rowValue !== true) {
                            if (rowValue?.toLowerCase() === "application") keyMaps[sanitizedKey]['type'] = 1;
                            if (rowValue?.toLowerCase() === "db") keyMaps[sanitizedKey]['type'] = 2;
                            if (rowValue?.toLowerCase() === "virtual") keyMaps[sanitizedKey]['type'] = 3;
                        }
                        keyMaps[sanitizedKey][sanitizedValue] = rowValue;
                    }
                }
            }
        }
    };

    keyMaps.infraobjectconfiguration.serverProperties = JSON.stringify(serverProps);
    keyMaps.infraobjectconfiguration.databaseProperties = JSON.stringify(dbProps);
    keyMaps.infraobjectconfiguration.replicationProperties = JSON.stringify(replProps);

    return stringifyProperties({
        serverList: [keyMaps?.prserverconfiguration, keyMaps?.drserverconfiguration],
        databaseList: [keyMaps?.prdatabaseconfiguration, keyMaps?.drdatabaseconfiguration],
        replicationList: [keyMaps?.prreplicationconfiguration, keyMaps?.drreplicationconfiguration],
        infraObject: keyMaps?.infraobjectconfiguration,
        isSwitchOver: keyMaps?.isSwitchOver,
        isFailOver: keyMaps?.isFailOver,
        isSwitchBack: keyMaps?.isSwitchBack,
        isFailBack: keyMaps?.isFailBack,
        isServer: isNotEmptyData(keyMaps?.prserverconfiguration) && isNotEmptyData(keyMaps?.drserverconfiguration),
        isDatabase: isNotEmptyData(keyMaps?.prdatabaseconfiguration) && isNotEmptyData(keyMaps?.drdatabaseconfiguration),
        isReplication: isNotEmptyData(keyMaps?.prreplicationconfiguration) && isNotEmptyData(keyMaps?.drreplicationconfiguration),
        isInfraObject: true,
    });
}

async function getSiteID(name) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/BulkImport/GetSiteByName',
            dataType: "json",
            data: { name: name },
            success: function (result) {
                if (result?.success) {
                    resolve(result);
                } else {
                    errorNotification(result);
                    resolve(null);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                reject(errorThrown);
            }
        });
    });
}

const isNotEmptyData = (val) => {
    if (!val) return false;
    let parsed = typeof val?.properties === 'string' ? JSON.parse(val?.properties) : val?.properties;
    const hasName = val?.name?.trim() !== '';
    return parsed && Object.keys(parsed)?.length > 0 && hasName;
};

function stringifyProperties(jsonData) {
    const traverse = (obj) => {
        for (const key in obj) {
            if (obj[key] && typeof obj[key] === 'object') {
                if (key === 'properties') {
                    obj[key] = JSON.stringify(obj[key]);
                } else {
                    traverse(obj[key]);
                }
            }
        }
    };
    traverse(jsonData);
    return jsonData;
}

function createKeyMaps() {
    return {
        isSwitchOver: "",
        isFailOver: "",
        isSwitchBack: "",
        isFailBack: "",
        prserverconfiguration: PRAndDRServerConfig("PR"),
        drserverconfiguration: PRAndDRServerConfig("DR"),
        prdatabaseconfiguration: PRAndDRDatabaseConfig("PR"),
        drdatabaseconfiguration: PRAndDRDatabaseConfig("DR"),
        prreplicationconfiguration: PRAndDRReplicationConfig("PR"),
        drreplicationconfiguration: PRAndDRReplicationConfig("DR"),
        infraobjectconfiguration: {
            companyId: "string", businessServiceId: "string", businessFunctionId: "string", prServerId: "string", drServerId: "string",
            nearDRServerId: "string", prDatabaseId: "string", drDatabaseId: "string", nearDRDatabaseId: "string", drReplicationId: "string",
            prReplicationId: "string", nearDRReplicationId: "string", pairInfraObjectId: "string", isAssociateInfraObjectId: "string",
            replicationTypeId: "string", replicationCategoryTypeId: "string", subTypeId: "string", prNodeId: "string", drNodeId: "string",
            type: 2,
            drReady: true, nearDR: false, recoveryType: 0, prServerName: "Mssql_PR_Server", drServerName: "",
            nearDRServerName: "", prDatabaseName: "Mssql_PR_Database", drDatabaseName: "", nearDRDatabaseName: "",
            prReplicationName: "Today_Replication", drReplicationName: "", nearDRReplicationName: "", state: "Maintenance",
            replicationStatus: 0, drOperationStatus: 0, pairInfraObjectName: "", isAssociateInfraObjectName: "",
            replicationTypeName: "", replicationCategoryType: "",
            prNodeName: "", drNodeName: "", nodeProperties: "{}", reason: "",
            serverProperties: JSON.stringify({
                PR: { id: "", name: "", type: "PRDBServer" },
                DR: { id: "", name: "", type: "DRDBServer" }
            }),
            databaseProperties: JSON.stringify({
                PR: { id: "", name: "", type: "PRDB" },
                DR: { id: "", name: "", type: "DRDB" }
            }),
            replicationProperties: JSON.stringify({
                PR: { id: "", name: "" },
                DR: { id: "", name: "" }
            }),
            siteProperties: JSON.stringify([])
        }
    };
}

const PRAndDRServerConfig = (type) => {
    return {
        "type": type, "properties": {}, "status": "Pending", "siteId": "string", "logo": "string", "businessServiceId": "string",
        "roleTypeId": "string", "serverTypeId": "string", "osTypeId": "string", "licenseId": "string",
    }
}

const PRAndDRDatabaseConfig = (type) => {
    return {
        type: type, properties: {}, databaseTypeId: "string", logo: "string", serverId: "string", companyId: "string",
        licenseId: "string", businessServiceId: "string", modeType: "Pending", serverName: ""
    }
}

const PRAndDRReplicationConfig = (type) => {
    return {
        replType: type, properties: {}, companyId: "string", logo: "string", siteId: "string", typeId: "string",
        licenseId: "string", businessServiceId: "string"
    }
}

// running data loading function

async function runningStatus() {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationsrunningStatus",
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        success: function (result) {

            let $ul = $('#bulkImportLists')?.empty();

            if (result?.data?.length) {
                $("#insertBulkImport").addClass('d-none');
                bulkImportOperationStatus = result?.data;

                result?.data?.forEach(async function (item, parentindex) {
                    let actionSuccess = 0, actionError = 0;
                    let success = [], percent = [];

                    $("#totalInfra").html(item?.bulkImportOperationGroup?.length);

                    item?.bulkImportOperationGroup?.length && item?.bulkImportOperationGroup?.forEach(function (data, childindex) {

                        let groupID = JSON.stringify(data?.id);
                        const percentage = eval(data?.progressStatus) * 100;
                        let infraName = data?.infraObjectName || "NA";
                        let errorClass = data?.status === "Error" ? "bg-danger" : "bg-success";
                        let actionStatus = [];

                        percent.push(percentage);
                        success.push(data?.status);

                        let html = `<li onclick="renderTimeline(this)" class="dynamic-active-card${parentindex}${childindex} list-group-item border-top ${(parentindex === 0 && childindex === 0) ? 'Active-Card' : ''}  d-flex justify-content-between align-items-center" id=${data?.id}>
                                            <div class="d-flex">
                                                <i class="cp-idea Skipped_Paused me-2"></i>
                                                <span class="fw-bold text-truncate" style="width:150px" title="${infraName}">${infraName}</span>
                                            </div>
                                            <div>        
                                            <span id="actionName${childindex}" class="text-truncate d-inline-block" style="max-width:90%"></span>                                                                                                 
                                                <div class="d-flex align-items-center gap-1 mt-1">
                                                    <i class="${data?.status?.toLowerCase() === "running" ? "cp-thunder" : data?.status.toLowerCase() === "success" ? "cp-success" : data?.status?.toLowerCase() === "error" ? "cp-error" : "cp-pause"}"></i>
                                                        <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                                                            <div class="progress-bar ${errorClass} progress-bar-striped progress-bar-animated" style="width: ${percentage}%;"></div>
                                                        </div>
                                                        <span class="text-truncate d-inline-block ${data?.status?.toLowerCase() === "error" ? "text-danger" : ''} statusText" title="${data?.status}" style="width:50px">${data?.status}</span><span class="ms-2 progressStatus">${data?.progressStatus}</span>
                                                </div>
                                            </div>
                                            <div class="btn-group-sm">
                                                <button type="button" ${(data?.status === "Error" || data?.status === "Next") && percentage !== 100 ? "" : "disabled"} onclick='actionButton(${groupID},"Next",${1})' class='btn btn btn-outline-secondary border-0'><i class="cp-circle-playnext fs-6 me-2"></i><span class="align-middle">Next</span></button>
                                                <button type="button" ${(data?.status === "Error" || data?.status === "Next") && percentage !== 100 ? "" : "disabled"} onclick='actionButton(${groupID},"Rollback")' class="btn btn btn-outline-secondary border-0"><i class="cp-reload fs-6 me-2"></i><span class="align-middle">Rollback</span></button>
                                                <button type="button" onclick='actionButton(${groupID},"Abort",${0})' class="btn btn btn-outline-secondary border-0"><i class="cp-disable fs-6 me-2"></i><span class="align-middle">Abort</span></button>
                                            </div>
                                        </li>`

                        $ul.append(html);

                        data?.bulkImportActionResultListVms?.length && data?.bulkImportActionResultListVms?.map(function (action) {
                            const name = action?.entityName || "NA";
                            $("#actionName" + childindex)?.text(name)?.attr("title", name);
                            actionStatus.push(action?.status);
                        })

                        if (actionStatus?.length) {
                            let resultStatus = actionStatus?.every(value => value.toLowerCase() === "success");
                            if (resultStatus) actionSuccess++
                            else actionError++

                            $("#successInfra").html(actionSuccess);
                            $("#errorInfra").html(actionError);
                        }
                        if (parentindex === 0 && childindex === 0 && data?.id) {
                            getTimeLineByGroupId(data?.id)
                        }
                    });

                    if (success?.length) {
                        let successed = success?.every(value => ["completed", "abort"].includes(value?.toLowerCase()));
                        let resultPercentage = percent?.every(value => value === 100);
                        success.forEach(function (data, index) {
                            if (data?.toLowerCase() === "rollback" || data?.toLowerCase() === "abort") {
                                resultPercentage = true
                            }
                        })

                        if (successed && resultPercentage) {
                            $("#CompleteBulkImport").removeClass('d-none').attr("onclick", `completeAction(${JSON.stringify(item.id)}, ${parentindex})`);
                            $("#insertBulkImport").addClass('d-none');
                        }
                    }
                });

                $("#InsetDBModal").modal("show");
            } else {
                bindTimelineData([]);
                $("#closeInsertDBModal").trigger("click");
                removeUploadedFile();
            }
        }
    });
}

// functions for next, rollback and abort

async function actionButton(GroupId, status, conditional = '') {

    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationGroup",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { id: GroupId },
        success: function (response) {
            if (response?.success) {
                let updateBulkFroup = {
                    id: response?.data?.id,
                    bulkImportOperationId: response?.data?.bulkImportOperationId,
                    companyId: response?.data?.companyId,
                    properties: response?.data?.properties,
                    status: status,
                    ErrorMessage: response?.data?.errorMessage,
                    conditionalOperation: conditional || response?.data?.conditionalOperation,
                    nodeId: response?.data?.nodeId,
                    infraObjectName: response?.data?.infraObjectName,
                    progressStatus: response?.data?.progressStatus,
                };

                UpdateBulkImportGroup(response, updateBulkFroup, status, conditional || response?.data?.conditionalOperation);
            } else {
                errorNotification(response);
            }
        }
    });

    if (status === 'Rollback') {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Configuration/BulkImport/RollBackBulkImportAction",
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { rollBackBulkImportCommand: { groupId: GroupId } },
            success: function (result) {
                if (result?.success) {
                    $(`.${GroupId}`).attr("disabled", "disabled");
                    notificationAlert("success", result?.data?.message);
                } else {
                    errorNotification(result);
                }
            }
        });
    }
}

async function handleBulkImportGroupAction({ groupId, status, conditional = false, isRollback = false }) {
    try {
        const groupResponse = await $.ajax({
            type: "GET",
            url: RootUrl + "Configuration/BulkImport/GetBulkImportOperationGroup",
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { id: groupId }
        });

        if (!groupResponse?.success) return errorNotification(groupResponse);

        const groupData = groupResponse.data;

        const updateBulkGroup = {
            id: groupData.id,
            bulkImportOperationId: groupData.bulkImportOperationId,
            companyId: groupData.companyId,
            properties: groupData.properties,
            status,
            ErrorMessage: groupData.errorMessage,
            conditionalOperation: conditional || groupData.conditionalOperation,
            nodeId: groupData.nodeId,
            infraObjectName: groupData.infraObjectName,
            progressStatus: groupData.progressStatus
        };

        UpdateBulkImportGroup(groupResponse, updateBulkGroup, status, updateBulkGroup.conditionalOperation);

        // If rollback, make additional POST call
        if (isRollback) {
            const rollbackResult = await $.ajax({
                type: "POST",
                url: RootUrl + "Configuration/BulkImport/RollBackBulkImportAction",
                dataType: "json",
                headers: { 'RequestVerificationToken': await gettoken() },
                data: { rollBackBulkImportCommand: { groupId } }
            });

            if (rollbackResult?.success) {
                $(`.${groupId}`).attr("disabled", "disabled");
                notificationAlert("success", rollbackResult?.data?.message);
            } else {
                errorNotification(rollbackResult);
            }
        }

    } catch (err) {
        errorNotification({ success: false, message: err.message });
    }
}

async function UpdateBulkImportGroup(response, updateBulkFroup, status, conditional) {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/UpdateBulkImportGroup",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { updateBulkImportOperationGroupCommand: updateBulkFroup },
        success: function (result) {
            if (result?.success) {
                if (status?.toLowerCase() === "next") {
                    getBulkImportActionResult(response, status, conditional);
                } else {
                    //<i class="${data?.status.toLowerCase() === "running" ? "cp-thunder" : data?.status.toLowerCase() === "success" ? "cp-success" : data?.status.toLowerCase() === "error" ? "cp-error" : "cp-pause"}"></i>
                    //    <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="height: 4px; width:180px;">
                    //        <div class="progress-bar ${errorClass} progress-bar-striped progress-bar-animated" style="width: ${percentage}%;"></div>
                    //    </div>
                    //    <span class="text-truncate d-inline-block ${data?.status?.toLowerCase() === "error" ? "text - danger" : ''} statusText" title = "${data?.status}" style = "width:50px" > ${ data?.status }</span > <span class="ms-2 progressStatus">${data?.progressStatus}</span>

                   // $(`#${updateBulkFroup?.id}`)?.find('.statusText').text(updateBulkFroup?.status)
                }
            } else {
                errorNotification(result);
            }
        }
    });
}

async function getBulkImportActionResult(response, status, conditional) {

    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetBulkImportActionResult",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { operationId: response?.data?.bulkImportOperationId, operationGroupId: response?.data?.id },
        success: function (res) {
            if (res?.success) {
                res?.data?.length && res?.data?.forEach(function (data, index) {

                    if (data?.status?.toLowerCase() === "error") {
                        let updateBulkImportAction = {
                            id: res?.data[index]?.id,
                            companyId: res?.data[index]?.companyId,
                            nodeId: res?.data[index]?.nodeId,
                            nodeName: res?.data[index]?.nodeName,
                            bulkImportOperationId: res?.data[index]?.bulkImportOperationId,
                            bulkImportOperationGroupId: res?.data[index]?.bulkImportOperationGroupId,
                            conditionalOperation: conditional,
                            entityId: res?.data[index]?.entityId,
                            entityName: res?.data[index]?.entityName,
                            entityType: res?.data[index]?.entityType,
                            status: status,
                            errorMessage: res?.data[index]?.errorMessage,
                            startTime: res?.data[index]?.startTime,
                            endTime: res?.data[index]?.endTime
                        };
                        UpdateBulkActionResult(updateBulkImportAction);
                    }

                });
            } else {
                errorNotification(res);
            }
        }
    });
}

async function UpdateBulkActionResult(updateBulkImportAction) {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/UpdateBulkImportActionResult",
        dataType: "json",
        headers: { 'RequestVerificationToken': await gettoken() },
        data: { updateBulkImportActionResultCommand: updateBulkImportAction },
        success: function (result) {
            if (!result.success) {
                errorNotification(result);
            } 
        }
    });
}

// complete action function

async function completeAction(GroupId, index) {
    let filteredOperationGroup = bulkImportOperationStatus[index];
    let success = false;
    if (filteredOperationGroup?.bulkImportOperationGroup?.length) {
        success = filteredOperationGroup?.bulkImportOperationGroup?.every(value => (value?.status?.toLowerCase() === "success"));
    }

    let operationStatus = success ? "Success" : "SuccessWithError";

    try {
        $("#CompleteBulkImport").addClass('d-none');

        // Step 1: Get Bulk Operation Details
        const operationResult = await $.ajax({
            type: "GET",
            url: `${RootUrl}Configuration/BulkImport/GetBulkImportOperation`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { id: GroupId },
        });

        if (!operationResult?.success) return errorNotification(operationResult);

        const updateBulkOperation = {
            id: operationResult?.data?.id,
            companyId: operationResult?.data?.companyId,
            userName: operationResult?.data?.userName,
            description: operationResult?.data?.description,
            status: operationStatus,
            startTime: operationResult?.data?.startTime,
            endTime: operationResult?.data?.endTime,
        };

        // Step 2: Update Bulk Operation
        const updateOperationResult = await $.ajax({
            type: "POST",
            url: `${RootUrl}Configuration/BulkImport/UpdateBulkImportOperation`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { updateBulkImportOperationCommand: updateBulkOperation },
        });

        if (!updateOperationResult?.success) return errorNotification(updateOperationResult);

        // Step 3: Get but import by operation id
        const groupResponse = await $.ajax({
            type: "GET",
            url: `${RootUrl}Configuration/BulkImport/GetBulkImportOperationGroupByOperationId`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { id: GroupId },
        });

        if (!groupResponse?.success) return errorNotification(groupResponse);
        
        const updateBulkImOperationGroup = groupResponse?.data?.map(groupdata => ({
            Id: groupdata?.id,
            BulkImportOperationId: groupdata?.bulkImportOperationId,
            CompanyId: groupdata?.companyId,
            Properties: groupdata?.properties,
            Status: groupdata?.status?.toLowerCase() === "success" ? "Completed" : groupdata?.status?.toLowerCase() === "error" ? "CompletedWithError" : "Aborted",
            ProgressStatus: groupdata?.progressStatus,
            ErrorMessage: groupdata?.errorMessage,
            ConditionalOperation: groupdata?.conditionalOperation,
            NodeId: groupdata?.nodeId,
            InfraObjectName: groupdata?.infraObjectName,
        }));

        // Step 4: update bulk import group
        const updateGroupResult = await $.ajax({
            type: "POST",
            url: `${RootUrl}Configuration/BulkImport/UpdateBulkImportOperationGroup`,
            dataType: "json",
            headers: { 'RequestVerificationToken': await gettoken() },
            data: { updateBulkImportOperationGroupCommand: updateBulkImOperationGroup },
        });

        if (!updateGroupResult?.success) return errorNotification(updateGroupResult);
      
         setTimeout(() => {
                $('#InsetDBModal').modal('hide')
                $("#insertBulkImport").removeClass('d-none');
         }, 100)
        
    } catch (error) {
        errorNotification({ success: false, message: error.message });
    }
}

// active and render timeline based on id
function renderTimeline(element) {
    // Deselect the currently active item
    const currentActiveItem = document?.querySelector(`.Active-Card`);
    let activeCardId = '';

    if (currentActiveItem) {
        currentActiveItem?.classList?.remove('Active-Card');
        element?.classList?.add('Active-Card');
        activeCardId = element?.id;
    }

    if (activeCardId) getTimeLineByGroupId(activeCardId)
}

const getTimeLineByGroupId = async (activeCardId) => {

    await $.ajax({
        type: "GET",
        url: RootUrl + "Configuration/BulkImport/GetTimeLineByOperationGroupId",
        dataType: "json",
        data: { operationGroupId: activeCardId },
        headers: { 'RequestVerificationToken': await gettoken() },
        success: function (result) {
            if (result?.success) {
                if (result?.data?.length) bindTimelineData(result?.data)
            } else {
                errorNotification(reslt);
            }
        }
    });
}

// timeline data appending function
function bindTimelineData(data) {
    let $timeLine = $('#bulkImportTimeLine')?.empty();

    if (!Array.isArray(data) || !data?.length) {      
        $timeLine.append(`<li><span style="text-align: center;"> 
        <img src="/img/isomatric/no_data_found.svg" style="width: auto; height: 200px;" alt="No Data"></span></li>`);
        return
    }

    data?.forEach(function (tmline) {
        let $litime = getTimeLineHtml(tmline)
        $timeLine.append($litime);
    });
}

// update timeline by signalr

const signalStatusUpdate = (data) => {
    if (data?.message) {
        const $targetLi = $('#bulkImportLists li#' + CSS.escape(data?.message?.operationGroupId));
        const match = $('#bulkImportLists .Active-Card')?.attr('id') === data?.message?.operationGroupId
        const status = data?.status?.toLowerCase();

        if ($targetLi?.length) {
            const $progressBar = $targetLi?.find('.progress-bar');
            let progressValue = data?.message?.progrssBar;
            let percentage = eval(progressValue) * 100;
            let errorClass = status === "error" ? "bg-danger" : "bg-success";
            const iconClass = status === 'running' ? 'cp-thunder' : status === 'success' ? 'cp-success' :
                status === 'error' ? 'cp-error' : 'cp-pause';

            $progressBar?.css('width', `${percentage}%`).removeClass('bg-success bg-danger').addClass(errorClass);
            $targetLi.find('i').first().attr('class', iconClass);
            $targetLi.find('.statusText').attr('title', data?.message?.status).text(data?.message?.status)
                ?.addClass(`${status === "error" ? "text-danger" : ''}`);
            $targetLi.find('.progressStatus').text(data?.message?.progrssBar);
        }

        if (match) {
            let $timeLine = $('#bulkImportTimeLine'); 
            if ($('li img[alt="No Data"]')?.length) $('li img[alt="No Data"]')?.closest('li')?.remove();

            const timeLineHTML = getTimeLineHtml(data?.message);
            $timeLine.append(timeLineHTML);
        }

        //const allCompleted = $('#bulkImportLists li .statusText').toArray().every(el =>
        //    $(el)?.text()?.trim()?.toLowerCase() === 'completed'
        //);

        //if (allCompleted) $("#CompleteBulkImport").removeClass('d-none'); 
        //else $("#CompleteBulkImport").addClass('d-none');
    }
}

const getIconInfo = (entityTypes = '') => {
    const entityType = entityTypes?.toLowerCase();

    const iconMap = {
        server: { icon: 'cp-server', color: '#2f0406' },
        database: { icon: 'cp-database', color: '#1015eb' },
        replication: { icon: 'cp-replication-on', color: '#9d15b3' },
        infraobject: { icon: 'cp-infra-object', color: '#00183a' },
    };

    const isWorkflow = entityType?.includes('workflow');
    const iconInfo = isWorkflow
        ? { icon: 'cp-workflow-configuration', color: '' }
        : iconMap[entityType] || { icon: '', color: '' };

    return iconInfo
}

const formattedDateAndTime = (startTime) => {
    const date = new Date(startTime);
    const day = String(date?.getDate())?.padStart(2, '0');
    const month = String(date?.getMonth() + 1)?.padStart(2, '0');
    const year = date?.getFullYear();
    const hours = String(date?.getHours())?.padStart(2, '0');
    const minutes = String(date?.getMinutes())?.padStart(2, '0');
    const seconds = String(date?.getSeconds())?.padStart(2, '0');
    const formattedDateTime = `${day}.${month}.${year} ${hours}:${minutes}:${seconds}`;

    return formattedDateTime
}

const getTimeLineHtml = (data) => {

    const status = data?.status?.toLowerCase();
    const errorMessage = (data?.errorMessage || data?.message) || "";
    const iconInfo = getIconInfo(data?.entityType)
    // Choose icon and color
    const bgColor = ['error', 'next', 'rollback'].includes(status) ? '#ffd8da' : '#bde9de';
    const formattedDateTime = formattedDateAndTime(data?.startTime)

    let timeLineHTML = `<li><div class="item-icon" style="background: ${bgColor}">
                    <i class="${iconInfo?.icon}" style="color: ${iconInfo?.color}"></i></div>
                <div class="item-text"><div class="item-title"><div class="d-flex align-items-center">
                   <span class="ms-2">${errorMessage}</span></div></div>
                   <div class="item-timestamp fs-8 ms-2 text-end" style="width: 40%;">${formattedDateTime}</div></div></li>`

    return timeLineHTML;
}

// shows table data
async function bulkImportValidation(value = null) {
   
    if (["server", "database", "replication", "infraobject"].includes(value)) {
        $('#validationModal').trigger('click');
    }
    
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/BulkImportValidation",
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        contentType: 'application/json',
        data: JSON.stringify(bulkImportObject),

        success: function (response) {
            const progressBar = document?.getElementById('dynamicProgressBar');
            progressBar.style.width = '100%';
            $("#percentageProgressBar").html(`100%`);

            if (response?.success) {

                $("#validationLists")?.empty();
                validatorResponse = response?.data?.validatorResponse;

                if (response?.data?.validatorResponse?.length) {
                    let success = 0, failure = 0, newRow = '';
                    let workflowError = `<i class="cp-error text-danger"></i>`;
                    let workflowSuccess = `<i class="cp-success text-success"></i>`;
                    validationResponse = response?.data;

                    if (!value) $('#validationModal').trigger('click');
                    $('#totalInfraObject').text(response?.data?.validatorResponse?.length);                  
                    $("#failureInfraObject").text(failure);
                    $("#successInfraObject").text(success);

                    response?.data?.validatorResponse?.forEach(function (item, index) {
                        
                        const hasError = ['databaseCommand', 'infraObjectCommand', 'replicationCommand', 'serverCommand']
                            .some(key => item?.[key]?.length > 0);

                        const infraIcon = hasError ? workflowError : workflowSuccess;
                        hasError ? failure++ : success++;

                        $("#failureInfraObject").text(failure);
                        $("#successInfraObject").text(success);
                        if (!hasError) notificationAlert("success", "All the fields are correct")
                     
                        newRow += `<tr class="text-center">
                            <td class="text-center"><input class="form-check-input mx-0 checkBoxBulkImport" id="firstCheckBoxBulkImport${index}" type="checkbox" value="${index}"></td>
                            <td>${index + 1}</td>
                            <td>${infraIcon}<span class="ms-2">${item?.infraObjectName}</span></td>
                            <td
                                ${getTdAttributes(item, 'serverCommand', 'ServerModal', index).attributes}>
                                ${item?.isServer ? `<i class="${getTdAttributes(item, 'serverCommand', 'ServerModal')?.iconClass}"></i>` : '-'}
                            </td>
                            <td
                                ${getTdAttributes(item, 'databaseCommand', 'DatabaseModal', index).attributes}>
                                ${item?.isDatabase ? `<i class="${getTdAttributes(item, 'databaseCommand', 'DatabaseModal')?.iconClass}"></i>` : '-'}
                            </td>
                            <td
                                ${getTdAttributes(item, 'replicationCommand', 'ReplicationModal', index).attributes}>
                                ${item?.isReplication ? `<i class="${getTdAttributes(item, 'replicationCommand', 'ReplicationModal')?.iconClass}"></i>` : '-'}
                            </td>
                            <td                                    
                                ${getTdAttributes(item, 'infraObjectCommand', 'InfraObjectModal', index).attributes}>
                                ${item?.isInfraObject ? `<i class="${getTdAttributes(item, 'infraObjectCommand', 'InfraObjectModal')?.iconClass}"></i>` : '-'}
                            </td>
                            <td>${item?.isSwitchOver === true ? workflowSuccess : workflowError}</td>
                            <td>${item?.isSwitchBack === true ? workflowSuccess : workflowError}</td>
                            <td>${item?.isFailOver === true ? workflowSuccess : workflowError}</td>
                            <td>${item?.isFailBack === true ? workflowSuccess : workflowError}</td>
                            <td><i role="button" title="Delete" data-bs-toggle="modal" data-bs-target="#DeleteRow" data-infra-name="${item?.infraObjectName}" data-row-id="${index}" class="cp-Delete delete-Row text-danger"></i></td>                           
                        </tr>`;
                       
                    });

                    $("#validationLists").append(newRow);

                    $(".checkBoxBulkImport").on("change", function () {
                        if ($(".checkBoxBulkImport:checked")?.length === $(".checkBoxBulkImport")?.length) {
                            $("#flexCheckDefault").prop("checked", true);
                        }
                        $(".checkBoxBulkImport").each(function () {
                            if (!$(this).is(":checked")) {
                                $("#flexCheckDefault").prop("checked", false);
                            }
                        });
                    });

                    setTimeout(() => {
                        const now = new Date();
                        const formattedDate = now?.toISOString()?.split('T')[0]?.replace(/-/g, '_');
                        let description = "Bulk_Import_" + formattedDate;
                        $("#bulkimportDescription").val(description);
                    }, 200)

                } else { 
                    removeUploadedFile();
                    $("#closeOffCanvas").trigger("click");
                    notificationAlert("warning", "The configuration fields are missing in the uploaded document. Please re-upload the file with the complete or required information.");
                }
            } else {
                removeUploadedFile();
                notificationAlert("warning", "The configuration fields are missing in the uploaded document. Please re-upload the file with the complete or required information.");
            }
        }
    });
}

function getTdAttributes(item, commandType, modalId, index) {
    const commandExists = item?.[commandType]?.length;
    return {
        attributes: commandExists ? `data-bs-toggle="modal" data-bs-target="#${modalId}" role="button" onclick="edit${modalId}(${index})" ` : '',
        iconClass: commandExists ? 'cp-error text-danger' : 'cp-success text-success'
    };
}

// edit excel datas server, database, replication and infraObject functions

function editServerModal(index) {
    editComponentModal(index, "Server", "serverList", "serverCommand", (server) => ({
        pr: {
            PRServerName: server?.name,
            PRSiteName: server?.siteName,
            PROperationalService: server?.businessServiceName,
            PRServerRole: server?.roleType,
            PRServerType: server?.serverType,
            PROSType: server?.osType,
            PRVersion: server?.version,
            PRLicense: server?.licenseKey
        },
        dr: {
            DRServerName: server?.name,
            DRSiteName: server?.siteName,
            DROperationalService: server?.businessServiceName,
            DRServerRole: server?.roleType,
            DRServerType: server?.serverType,
            DROSType: server?.osType,
            DRVersion: server?.version,
            DRLicense: server?.licenseKey
        }
    }));
}

function editDatabaseModal(index) {
    editComponentModal(index, "Database", "databaseList", "databaseCommand", (db) => ({
        pr: {
            PRDatabaseName: db?.name,
            PRDatabaseType: db?.databaseType,
            PRDBOperational: db?.businessServiceName,
            PRDBVersion: db?.version,
            PRDBServer: db?.serverName,
            PRDBLicense: db?.licenseKey
        },
        dr: {
            DRDatabaseName: db?.name,
            DRDatabaseType: db?.databaseType,
            DRDBOperational: db?.businessServiceName,
            DRDBVersion: db?.version,
            DRDBServer: db?.serverName,
            DRDBLicense: db?.licenseKey
        }
    }));
}

function editReplicationModal(index) {  

    editComponentModal(index, "Replication", "replicationList", "replicationCommand", (replication) => ({
        pr: {
            PRReplicationName: replication?.name,
            PRReplicationSite: replication?.siteName,
            PRReplicationOperational: replication?.businessServiceName,
            PRReplicationType: replication?.type,
            PRReplicationLicense: replication?.licenseKey,
        },
        dr: {
            DRReplicationName: replication?.name,
            DRReplicationSite: replication?.siteName,
            DRReplicationOperational: replication?.businessServiceName,
            DRReplicationType: replication?.type,
            DRReplicationLicense: replication?.licenseKey,
        }
    }), "replType"); 
}

function editInfraObjectModal(index) {
    infraObjectIndex = index;

    const infraObj = bulkImportObject?.bulkImportOperationList[index]?.infraObject;
    const fields = {
        InfraObjectName: infraObj?.name,
        InfraObjectDescription: infraObj?.description,
        InfraObjectOperationalService: infraObj?.businessServiceName,
        InfraObjectOperationalFunction: infraObj?.businessFunctionName,
        InfraObjectReplicationCategory: infraObj?.replicationCategoryType,
        InfraObjectReplicationType: infraObj?.replicationTypeName
    };

    // Set field values
    Object.entries(fields).forEach(([id, value]) => {
        $(`#${id}`)?.val(value);
    });

    // Display validation errors
    const commands = validationResponse?.validatorResponse[index]?.infraObjectCommand || [];
    commands.forEach(cmd => {
        Object.entries(fields).forEach(([id, value]) => {
            if (value?.toLowerCase() === cmd?.name?.toLowerCase()) {
                const errorFieldId = `InfraObject${cmd?.propertyName}Error`;
                $(`#${errorFieldId}`).text(cmd?.exception).addClass('field-validation-error');
            }
        });
    });
}

function saveEditedServerData() {
    saveComponentData("Server", "serverList", {
        pr: "PRServerName",
        dr: "DRServerName"
    }, "serverIndex");
}
function saveEditedDatabaseData() {
    saveComponentData("Database", "databaseList", {
        pr: "PRDatabaseName",
        dr: "DRDatabaseName"
    }, "databaseIndex");
}

function saveEditedReplicationData() {
    saveComponentData("Replication", "replicationList", {
        pr: "PRReplicationName",
        dr: "DRReplicationName"
    }, "replicationIndex", "replType");
}
function saveEditedInfraObjectData() {
    const InfraObjectName = $("#InfraObjectName").val();
    bulkImportObject.bulkImportOperationList[infraObjectIndex].infraObject.name = InfraObjectName;
    $('#InfraObjectModal').modal("hide");
    bulkImportValidation("infraobject");
}

function editComponentModal(index, type, listKey, commandKey, fieldsMap, idKey = 'type') {
    const componentList = bulkImportObject?.bulkImportOperationList[index]?.[listKey] || [];
    const commandList = validationResponse?.validatorResponse[index]?.[commandKey] || [];

    window[`${type}Index`] = index; 

    componentList.forEach((item) => {
        const variant = item?.[idKey]?.toLowerCase();
        const fields = fieldsMap(item);

        // Populate fields
        for (const [inputId, value] of Object.entries(fields[variant])) {
            $(`#${inputId}`).val(value);
        }

        // Show validation errors
        commandList.forEach((command) => {
            Object.entries(fields[variant]).forEach(([inputId, val]) => {
                if (val?.toLowerCase() === command?.name?.toLowerCase()) {
                    const errorId = `${variant?.toUpperCase()}${type}${command?.propertyName}Error`;
                    $(`#${errorId}`).text(command?.exception).addClass('field-validation-error');
                }
            });
        });
    });
}

function saveComponentData(type, listKey, nameSelectors, indexKey, replKey = 'type') {
    const index = window[`${type}Index`];

    const PRValue = $(`#${nameSelectors?.pr}`)?.val();
    const DRValue = $(`#${nameSelectors?.dr}`)?.val();

    const list = bulkImportObject?.bulkImportOperationList[index]?.[listKey];
    if (!Array.isArray(list)) return;

    list.forEach(item => {
        const variant = item?.[replKey]?.toLowerCase();
        if (variant === "pr") item.name = PRValue;
        if (variant === "dr") item.name = DRValue;
    });

    $(`#${type}Modal`).modal("hide");
    bulkImportValidation(type?.toLowerCase());
}

// remove file function
function removeUploadedFile() {
    $("#fileName")?.html("File Name");
    const progressBar = document?.getElementById('dynamicProgressBar');
    progressBar.style.width = '0%';
    $("#percentageProgressBar")?.html("0%");
    const fileInput = document?.getElementById('file-input');
    fileInput.value = '';
}

// create bulk import functionality

async function insertBulkImportToDB() {
    allowInsertToDB = false;

    validatorResponse?.length && validatorResponse.forEach(function (data, index) {
        bulkImportObject?.bulkImportOperationList.forEach(function (bulkdata, childindex) {
            bulkdata["failBackTemplate"] = data?.failBackTemplate;
            bulkdata["failOverTemplate"] = data?.failOverTemplate;
            bulkdata["switchBackTemplate"] = data?.switchBackTemplate;
            bulkdata["switchOverTemplate"] = data?.switchOverTemplate;
        });
    });

    bulkImportObject?.bulkImportOperationList?.length && bulkImportObject?.bulkImportOperationList.forEach(function (bulkdata, childindex) {
        // Parse the switchBackTemplate
        const parsedTemplate = bulkdata?.switchBackTemplate && JSON.parse(bulkdata?.switchBackTemplate);

        parsedTemplate?.nodes?.length && parsedTemplate?.nodes?.forEach(function (nodeData) {
            const actionInfo = nodeData?.actionInfo;
            if (!actionInfo?.formInput || !actionInfo.properties) return;
            const props = {};

            actionInfo?.formInput?.length && actionInfo?.formInput.forEach(form => {
                const name = form?.name?.toLowerCase();

                const setPropIfMatch = (list, typeKey, matchKey) => {
                    list?.forEach(item => {
                        if (item?.[typeKey] === "PR" && name?.includes(`pr${matchKey}`)) {
                            props[form?.name] = `@${item?.name}`;
                        }
                        if (item?.[typeKey] === "DR" && name?.includes(`dr${matchKey}`)) {
                            props[form?.name] = `@${item?.name}`;
                        }
                    });
                };

                setPropIfMatch(bulkdata?.serverList, "type", "server");
                setPropIfMatch(bulkdata?.databaseList, "type", "db");
                setPropIfMatch(bulkdata?.replicationList, "replType", "replication")
            });
            nodeData.actionInfo.properties = props;
        });

        bulkdata.switchBackTemplate = JSON.stringify(parsedTemplate);
    });

    createBulkImport();
}

async function createBulkImport() {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Configuration/BulkImport/SaveBulkImport",
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        contentType: 'application/json',
        data: JSON.stringify(bulkImportObject),
        success: function (response) {
            if (response?.success) {
                bulkImportObject = '';
                runningStatus();
            } else {
                allowInsertToDB = true;
                removeUploadedFile();
                errorNotification(response);
                $("#insertBulkImport").removeClass('d-none');
            }
        }
    });
}

//onchange functions

$("#bulkimportDescription").on("keyup", async function () {
    let value = $(this)?.val();
    descriptionValidation(value);
});

// validation functions

function descriptionValidation(value) {
    if (!value) {
        $('#Description-Error').text("Enter description").addClass('field-validation-error');
        return false;
    }else if (value?.length < 3 || value?.length > 250) {
        $('#Description-Error').text("Between 3 to 250 characters").addClass('field-validation-error');
        return false;
    }

    $('#Description-Error').text("").removeClass('field-validation-error');
    return true;
}

async function IsNameExist(url, data, errorFunc) {
    const names = ['serverName', 'databaseName', 'replicationName', 'infraObjectName'];
    for (const name of names) {
        if (data[name]) {
            const trimmedName = data[name]?.trim();
            if (!trimmedName) return true;
            if (await GetAsync(url, data, errorFunc)) return "A same name already exists";
        }
    }
    return true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function commonNameValidation(value, nameExistURL, errorele, errortext, nameexistsdata) {
    if (!value) {
        errorele.text(errortext).addClass('field-validation-error');
        return false;
    }else if (value.includes('<')) {
        errorele.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }

    const url = RootUrl + nameExistURL;
    let data = nameexistsdata;
    const validationResults = [
        await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorele, validationResults);
}

function setupValidation(selector, url, errorElement, errorText, nameKey) {
    $(selector).on("keyup", commonDebounce(async function () {
        let data = { id: null, [nameKey]: $(this).val() };
        commonNameValidation($(this).val(), url, $(errorElement), errorText, data);
    }));
}

