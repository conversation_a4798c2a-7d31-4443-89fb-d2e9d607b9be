using AutoMapper;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Events.Paginated;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;
using FluentValidation.Results;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace ContinuityPatrol.Web.UnitTests.Areas.CyberResiliency.Controllers;

[Collection("AirGapTests")]
public class AirGapControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<ILogger<AirGapController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly AirGapController _controller;

    public AirGapControllerShould()
    {
        _controller = new AirGapController(
            _mockPublisher.Object,
            _mockMapper.Object,
            _mockLogger.Object,
            _mockDataProvider.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.List();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(p => p.Publish(It.IsAny<CyberAirGapPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockLogger.VerifyLoggerWasCalled(LogLevel.Debug, Times.Once);
    }

    [Fact]
    public async Task GetPagination_ReturnsJsonResult_OnSuccess()
    {
        var query = new GetCyberAirGapPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test"
        };
        var paginatedResult = new PaginatedResult<CyberAirGapListVm>
        {
            Data = new List<CyberAirGapListVm>
            {
                new CyberAirGapListVm { Id = "1", Name = "Test AirGap" }
            },
            TotalCount = 1
        };

        _mockDataProvider.Setup(dp => dp.CyberAirGap.GetPaginatedCyberAirGaps(query))
                         .ReturnsAsync(paginatedResult);

        var result = await _controller.GetPagination(query);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = paginatedResult });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(dp => dp.CyberAirGap.GetPaginatedCyberAirGaps(query), Times.Once);
        _mockLogger.VerifyLoggerWasCalled(LogLevel.Debug, Times.AtLeastOnce);
    }

    [Fact]
    public async Task GetPagination_HandlesException()
    {
        var query = new GetCyberAirGapPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test"
        };
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.CyberAirGap.GetPaginatedCyberAirGaps(query))
                         .ThrowsAsync(exception);

        var result = await _controller.GetPagination(query);

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_CreatesNewAirGap_WhenIdIsEmpty()
    {
        var airGapViewModel = new CyberAirGapViewModel
        {
            Name = "Test AirGap",
            Description = "Test Description"
        };
        var createCommand = new CreateCyberAirGapCommand
        {
            Name = "Test AirGap",
            Description = "Test Description"
        };
        var createResponse = new CreateCyberAirGapResponse
        {
            Id = "new-id",
            Message = "AirGap created successfully"
        };

        _mockMapper.Setup(m => m.Map<CreateCyberAirGapCommand>(airGapViewModel))
                   .Returns(createCommand);
        _mockDataProvider.Setup(dp => dp.CyberAirGap.CreateAsync(createCommand))
                         .ReturnsAsync(createResponse);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(airGapViewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        _mockDataProvider.Verify(dp => dp.CyberAirGap.CreateAsync(It.IsAny<CreateCyberAirGapCommand>()), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_UpdatesExistingAirGap_WhenIdIsProvided()
    {
        var airGapViewModel = new CyberAirGapViewModel
        {
            Id = "existing-id",
            Name = "Updated AirGap",
            Description = "Updated Description"
        };
        var updateCommand = new UpdateCyberAirGapCommand
        {
            Id = "existing-id",
            Name = "Updated AirGap",
            Description = "Updated Description"
        };
        var updateResponse = new UpdateCyberAirGapResponse
        {
            Id = "existing-id",
            Message = "AirGap updated successfully"
        };

        _mockMapper.Setup(m => m.Map<UpdateCyberAirGapCommand>(airGapViewModel))
                   .Returns(updateCommand);
        _mockDataProvider.Setup(dp => dp.CyberAirGap.UpdateAsync(updateCommand))
                         .ReturnsAsync(updateResponse);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "existing-id" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(airGapViewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        _mockDataProvider.Verify(dp => dp.CyberAirGap.UpdateAsync(It.IsAny<UpdateCyberAirGapCommand>()), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_HandlesValidationException()
    {
        var airGapViewModel = new CyberAirGapViewModel
        {
            Name = "Test AirGap"
        };
        var validationException = new ValidationException(new ValidationResult(
            new List<ValidationFailure>
            {
                new FluentValidation.Results.ValidationFailure("Name", "Name is required")
            }));

        _mockMapper.Setup(m => m.Map<CreateCyberAirGapCommand>(airGapViewModel))
                   .Returns(new CreateCyberAirGapCommand());
        _mockDataProvider.Setup(dp => dp.CyberAirGap.CreateAsync(It.IsAny<CreateCyberAirGapCommand>()))
                         .ThrowsAsync(validationException);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(airGapViewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_HandlesGeneralException()
    {
        var airGapViewModel = new CyberAirGapViewModel
        {
            Name = "Test AirGap"
        };
        var exception = new Exception("Database error");

        _mockMapper.Setup(m => m.Map<CreateCyberAirGapCommand>(airGapViewModel))
                   .Returns(new CreateCyberAirGapCommand());
        _mockDataProvider.Setup(dp => dp.CyberAirGap.CreateAsync(It.IsAny<CreateCyberAirGapCommand>()))
                         .ThrowsAsync(exception);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(airGapViewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task GetCyberComponentList_ReturnsJsonResult_OnSuccess()
    {
        var componentList = new List<CyberComponentListVm>
        {
            new CyberComponentListVm { Id = "1", Name = "Component 1" },
            new CyberComponentListVm { Id = "2", Name = "Component 2" }
        };

        _mockDataProvider.Setup(dp => dp.CyberComponent.GetCyberComponentList())
                         .ReturnsAsync(componentList);

        var result = await _controller.GetCyberComponentList();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetCyberComponentList_HandlesException()
    {
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.CyberComponent.GetCyberComponentList())
                         .ThrowsAsync(exception);

        var result = await _controller.GetCyberComponentList();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetWorkflowList_ReturnsJsonResult_OnSuccess()
    {
        var workflowList = new List<WorkflowListVm>
        {
            new WorkflowListVm { Id = "1", Name = "Workflow 1" },
            new WorkflowListVm { Id = "2", Name = "Workflow 2" }
        };

        _mockDataProvider.Setup(dp => dp.Workflow.GetWorkflowList())
                         .ReturnsAsync(workflowList);

        var result = await _controller.GetWorkflowList();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetWorkflowList_HandlesException()
    {
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Workflow.GetWorkflowList())
                         .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowList();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task Delete_ReturnsRedirectToList_OnSuccess()
    {
        var airGapId = "test-id";
        var deleteResponse = new DeleteCyberAirGapResponse
        {
            Message = "AirGap deleted successfully",
            IsActive = false
        };

        _mockDataProvider.Setup(dp => dp.CyberAirGap.DeleteAsync(airGapId))
                         .ReturnsAsync(deleteResponse);

        var result = await _controller.Delete(airGapId);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        _mockDataProvider.Verify(dp => dp.CyberAirGap.DeleteAsync(airGapId), Times.Once);
    }

    [Fact]
    public async Task Delete_HandlesException()
    {
        var airGapId = "test-id";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.CyberAirGap.DeleteAsync(airGapId))
                         .ThrowsAsync(exception);

        var result = await _controller.Delete(airGapId);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task IsAirGapNameExist_ReturnsTrue_WhenNameExists()
    {
        var name = "Test AirGap";
        var id = "test-id";
        _mockDataProvider.Setup(dp => dp.CyberAirGap.IsCyberAirGapNameExist(name, id))
                         .ReturnsAsync(true);

        var result = await _controller.IsAirGapNameExist(name, id);

        Assert.True(result);
        _mockDataProvider.Verify(dp => dp.CyberAirGap.IsCyberAirGapNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsAirGapNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        var name = "Test AirGap";
        var id = "test-id";
        _mockDataProvider.Setup(dp => dp.CyberAirGap.IsCyberAirGapNameExist(name, id))
                         .ReturnsAsync(false);

        var result = await _controller.IsAirGapNameExist(name, id);

        Assert.False(result);
        _mockDataProvider.Verify(dp => dp.CyberAirGap.IsCyberAirGapNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsAirGapNameExist_ReturnsFalse_OnException()
    {
        var name = "Test AirGap";
        var id = "test-id";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.CyberAirGap.IsCyberAirGapNameExist(name, id))
                         .ThrowsAsync(exception);

        var result = await _controller.IsAirGapNameExist(name, id);

        Assert.False(result);
    }
}
