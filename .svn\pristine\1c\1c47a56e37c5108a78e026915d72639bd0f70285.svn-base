﻿using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Services.Api.Impl.Admin;
using ContinuityPatrol.Services.ApiTests.Fixtures;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;

namespace ContinuityPatrol.Services.ApiTests.Impl.Admin;

public class DynamicDashboardServiceTests : IClassFixture<DynamicDashboardFixture>
{
    private readonly DynamicDashboardFixture _fixture;
    private readonly Mock<IBaseClient> _clientMock;
    private readonly DynamicDashboardService _service;

    public DynamicDashboardServiceTests(DynamicDashboardFixture fixture)
    {
        _fixture = fixture;
        _clientMock = new Mock<IBaseClient>();
        _service = new DynamicDashboardService(_clientMock.Object);
    }

    [Fact]
    public async Task GetDynamicDashboardList_ReturnsExpectedList()
    {
        _clientMock.Setup(x => x.GetFromCache<List<DynamicDashboardListVm>>(It.IsAny<RestRequest>(), "GetDynamicDashboardList"))
                   .ReturnsAsync(_fixture.ListVm);

        var result = await _service.GetDynamicDashboardList();

        Assert.Equal(_fixture.ListVm.Count, result.Count);
    }

    [Fact]
    public async Task CreateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Post<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.CreateAsync(_fixture.CreateCommand);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Put<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.UpdateAsync(_fixture.UpdateCommand);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task DeleteAsync_ReturnsBaseResponse()
    {
        _clientMock.Setup(x => x.Delete<BaseResponse>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.BaseResponse);

        var result = await _service.DeleteAsync(_fixture.Id);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsDetail()
    {
        _clientMock.Setup(x => x.Get<DynamicDashboardDetailVm>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.DetailVm);

        var result = await _service.GetByReferenceId(_fixture.Id);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DetailVm.Id, result.Id);
    }

    [Fact]
    public async Task IsDynamicDashboardNameExist_ReturnsTrue()
    {
        _clientMock.Setup(x => x.Get<bool>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(true);

        var result = await _service.IsDynamicDashboardNameExist(_fixture.Name, _fixture.Id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboards_ReturnsPaginatedList()
    {
        _clientMock.Setup(x => x.Get<PaginatedResult<DynamicDashboardListVm>>(It.IsAny<RestRequest>()))
                   .ReturnsAsync(_fixture.PaginatedListVm);

        var result = await _service.GetPaginatedDynamicDashboards(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedListVm.TotalCount, result.TotalCount);
    }
}
