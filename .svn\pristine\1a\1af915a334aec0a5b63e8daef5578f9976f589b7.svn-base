﻿QUnit.config.noglobals = false;

QUnit.module("siteType.js Functions", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input id="siteTypeName" value="">
            <span id="siteTypeName-error"></span>
            <select id="siteTypeDropdown">
                <option value="">Select</option>
                <option value="Primary">Primary</option>
                <option value="DR">DR</option>
            </select>
            <span id="siteTypeDropdown-error"></span>
        `);

        // Global mocks
        window.gettoken = () => "mock-token";
        window.notificationAlert = () => { };
        window.errorNotification = () => { };
        window.sanitizeContainer = () => { };
        window.commonDebounce = fn => fn;

        // Validation mocks
        window.SpecialCharValidate = () => true;
        window.ShouldNotBeginWithUnderScore = () => true;
        window.ShouldNotBeginWithSpace = () => true;
        window.OnlyNumericsValidate = () => false;
        window.SpaceWithUnderScore = () => true;
        window.ShouldNotEndWithUnderScore = () => true;
        window.ShouldNotEndWithSpace = () => true;
        window.MultiUnderScoreRegex = () => true;
        window.SpaceAndUnderScoreRegex = () => true;
        window.ShouldNotAllowMultipleSpace = () => true;
        window.minMaxlength = () => true;
        window.secondChar = () => true;
        window.ShouldNotBeginWithNumber = () => true;
        window.CommonValidation = (_, results) => results.every(r => r);

        // IsNameExist mock
        window.getAysncWithHandler = async () => ({ success: true, data: { message: false } });
    });

    hooks.afterEach(() => {
        $('#qunit-fixture').empty();
        sinon.restore();
    });

    QUnit.test("validateDropDown returns false for empty", assert => {
        const result = validateDropDown("", "Required", "siteTypeDropdown-error");
        assert.strictEqual(result, false, "Dropdown validation failed for empty");
    });

    QUnit.test("validateDropDown returns true for valid", assert => {
        const result = validateDropDown("Primary", "Required", "siteTypeDropdown-error");
        assert.strictEqual(result, true, "Dropdown validation passed");
    });

    QUnit.test("validateType returns false for empty", async assert => {
        const result = await validateType("", "siteTypeName-error");
        assert.strictEqual(result, false, "Empty input fails");
    });

    QUnit.test("validateType handles special char case", async assert => {
        const result = await validateType("<invalid", "siteTypeName-error");
        assert.strictEqual(result, false, "Special char validation fails");
    });

    QUnit.test("validateType passes with valid input", async assert => {
        const result = await validateType("ValidType", "siteTypeName-error");
        assert.strictEqual(result, true, "Valid input passes");
    });

    QUnit.test("clearInputField resets modal fields", assert => {
        $('#qunit-fixture').append(`
            <input id="siteTypeName" value="ABC">
            <select id="siteTypeDropdown">
                <option value="Primary" selected>Primary</option>
            </select>
        `);
        clearInputField();
        assert.strictEqual($('#siteTypeName').val(), "", "Name cleared");
    });

    QUnit.test("populateSiteType sets modal fields", assert => {
        $('#qunit-fixture').append(`
            <input id="siteTypeName">
            <select id="siteTypeDropdown">
                <option value="Primary">Primary</option>
            </select>
            <input id="siteTypeName" cityId="">
        `);
        populateSiteType({ type: "DataCenter", category: "Primary", id: 5 });
        assert.strictEqual($("#siteTypeName").val(), "DataCenter", "Name set");
        assert.strictEqual($("#siteTypeDropdown").val(), "Primary", "Dropdown set");
    });

    QUnit.test("IsNameExist returns true when name is new", async assert => {
        const result = await IsNameExist("dummyurl", { name: "Test", id: "0" });
        assert.ok(result, "No duplicate name");
    });
});

QUnit.module("siteType.js Events", hooks => {
    hooks.beforeEach(() => {
        $.fn.modal = function (action) {
            this.each(function () {
                $(this).attr("data-opened", action);
            });
            return this;
        };

        $("#qunit-fixture").html(`
            <button id="siteTypeCreateButton"></button>
            <input id="siteTypeName" />
            <select id="siteTypeDropdown">
                <option value="Primary">Primary</option>
                <option value="DR">DR</option>
            </select>
            <button id="siteTypeSaveBtn"></button>
            <form id="siteTypeCreateForm"></form>
            <div id="siteTypeCreateModal"></div>
        `);

        window.SiteValue = [[{ category: "Primary" }]];
        window.permission = { create: 'true', delete: 'true' };
        window.gettoken = () => "mock-token";
        window.notificationAlert = () => { };
        window.errorNotification = () => { };
        window.sanitizeContainer = () => { };
        window.CommonValidation = () => true;
        window.globalIsDelete = true;
        window.commonDebounce = fn => fn;

        sinon.stub($, "post").callsFake((opts) => {
            if (typeof opts === "object" && typeof opts.success === "function") {
                opts.success({ success: true, data: { message: "Saved" } });
            } else if (typeof opts === "string") {
                arguments[2]({ success: true, data: { message: "Saved" } });
            }
        });
    });

    hooks.afterEach(() => {
        $("#qunit-fixture").empty();
        sinon.restore();
    });

    QUnit.test("#siteTypeCreateButton click clears fields and opens modal", assert => {
        $.fn.modal = function (action) {
            $(this).attr("data-opened", action);
            return this;
        };
        $('#siteTypeCreateButton').on('click', function () {
            $('#siteTypeName').attr('siteTypeNameId', "");
            $('#siteTypeDropdown').prop('disabled', false);
            $('#siteTypeName, #siteTypeDropdown').val('');
            const options = $('#siteTypeDropdown option');
            options.prop('disabled', false);
            window.SiteValue = [[{ category: 'Primary' }]];
            SiteValue.flat().forEach(item => {
                if (item.category === 'Primary') {
                    options.filter('[value="Primary"]').prop('disabled', true);
                }
            });
            $('#siteTypeCreateModal').modal('show');
        });
        $('#siteTypeCreateButton').trigger('click');
        assert.strictEqual($('#siteTypeCreateModal').attr("data-opened"), "show", "Modal opened");
    });

    QUnit.test("#siteTypeSaveBtn click triggers AJAX save", assert => {
        $("#siteTypeName").val("NewType");
        $("#siteTypeDropdown").val("DR");
        $("#siteTypeSaveBtn").trigger("click");
        assert.ok(true, "Save button click executed");
    });

    QUnit.test("#siteTypeCreateForm prevents Enter submit", assert => {
        $('#siteTypeCreateForm').on('keypress', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });

        const e = $.Event("keypress", {
            key: "Enter",
            which: 13,
            keyCode: 13  
        });
        $('#siteTypeCreateForm').trigger(e);
        assert.ok(e.isDefaultPrevented(), "Enter keypress was prevented");
    });
});

QUnit.module("siteType.js Events", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input id="siteTypeName" />
            <input id="siteTypeDeleteId" />
            <span id="siteTypeNameError"></span>
            <span id="siteTypeError"></span>
            <input type="checkbox" id="stNameFilter" value="stName:" checked />
            <input type="checkbox" id="stypeFilter" value="stype:" checked />
            <input id="siteTypeSearch" />
            <form id="siteTypeCreateForm"></form>
            <select id="siteTypeDropdown">
                <option value="">Select</option>
                <option value="Primary">Primary</option>
                <option value="DR">DR</option>
            </select>
            <table id="siteTypeTable">
                <tbody>
                    <tr>
                        <td><span class="siteTypeEditbtn" data-sitedata="${btoa(JSON.stringify({ type: 'EditMe', id: '123', category: 'Primary', icon: 'cp-icon', isDelete: true }))}"></span></td>
                        <td><span class="siteTypeDeletebtn" data-sitetypeid="123" data-sitetypename="DeleteMe"></span></td>
                    </tr>
                </tbody>
            </table>
            <div id="siteTypeCreateModal"></div>
            <div id="siteTypeDeleteModal"></div>
            <button id="siteTypeDeleteButton"></button>
        `);

        // Mocks
        $.fn.modal = function (action) {
            $(this).attr("data-opened", action);
            return this;
        };

        window.gettoken = () => "mock-token";
        window.notificationAlert = () => { };
        window.errorNotification = () => { };
        window.sanitizeContainer = () => { };
        window.CommonValidation = () => true;
        window.globalIsDelete = true;

        sinon.stub($, "ajax").callsFake(opts => {
            if (opts.type === "DELETE") {
                opts.success({ success: true, data: { message: "Deleted" } });
            } else {
                opts.success({ success: false });
            }
            return $.Deferred().resolve();
        });
    });

    hooks.afterEach(() => {
        $('#qunit-fixture').empty();
        sinon.restore();
    });

    QUnit.test("keyup on #siteTypeName triggers validation", async assert => {
        $('#siteTypeName').val("MySite").trigger("keyup");
        assert.strictEqual($('#siteTypeName').val(), "MySite", "Keyup updated value");
    });

    QUnit.test("change on #siteTypeDropdown triggers dropdown validation", assert => {
        $('#siteTypeDropdown').val("Primary").trigger("change");
        assert.strictEqual($('#siteTypeError').text(), "", "Dropdown is valid");
    });

    QUnit.test("click on .siteTypeEditbtn populates modal", assert => {
        $.fn.modal = function (action) {
            $(this).attr("data-opened", action);
            return this;
        };

        $('#siteTypeTable').on('click', '.siteTypeEditbtn', function () {
            const data = JSON.parse(atob($(this).data("sitedata")));
            if (data) {
                $('#siteTypeName').val(data.type);
                $('#siteTypeCreateModal').modal('show');
            }
        });

        $('.siteTypeEditbtn').trigger('click');

        assert.equal($('#siteTypeCreateModal').attr("data-opened"), "show", "Edit modal opened");
        assert.equal($('#siteTypeName').val(), "EditMe", "Edit value populated");
    });

    QUnit.test("click on .siteTypeDeletebtn opens delete modal", assert => {
        $.fn.modal = function (action) {
            $(this).attr("data-opened", action);
            return this;
        };

        $('.siteTypeDeletebtn').on('click', function () {
            const id = $(this).data('sitetypeid');
            const name = $(this).data('sitetypename');
            $('#siteTypeDeleteId').val(id).attr('title', name);
            $('#siteTypeDeleteModal').modal('show');
        });

        $('.siteTypeDeletebtn').trigger('click');

        assert.equal($('#siteTypeDeleteModal').attr("data-opened"), "show", "Delete modal opened");
        assert.equal($('#siteTypeDeleteId').val(), "123", "Delete ID set");
    });

    QUnit.test("click on #siteTypeDeleteButton triggers AJAX delete", assert => {
        $('#siteTypeDeleteId').val("123").attr("title", "DeleteMe");
        $('#siteTypeDeleteButton').trigger('click');
        assert.ok(true, "Delete button clicked and AJAX fired");
    });
});

QUnit.module("siteType.js DOM Event Handlers", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input id="siteTypeSearch" />
            <input type="checkbox" id="stNameFilter" value="stName:" checked />
            <input type="checkbox" id="stypeFilter" value="stype:" checked />
            <span class="dataTables_empty"></span>
        `);

        window.dataTable = {
            ajax: {
                reload: cb => cb({ recordsFiltered: 0 })
            }
        };

        window.commonDebounce = fn => fn;
    });

    QUnit.test("keydown on #siteTypeSearch blocks forbidden keys", assert => {
        const keys = ['=', '<', 'Enter'];
        keys.forEach(key => {
            const e = $.Event("keydown", { key: key, shiftKey: key === '<' });
            const result = $('#siteTypeSearch').trigger(e);
            assert.ok(true, `Keydown for '${key}' triggered and blocked`);
        });
    });

    QUnit.test("DataTable draw.dt event calls updatePaginationTitles", assert => {
        const dataTableMock = {
            on: function (event, handler) {
                this.handler = handler;
            },
            trigger: function (event) {
                if (event === 'draw.dt' && this.handler) {
                    this.handler();
                }
            }
        };
        window.dataTable = dataTableMock;

        const spy = sinon.spy(window, 'updatePaginationTitles');

        dataTableMock.on('draw.dt', updatePaginationTitles);

        dataTableMock.trigger('draw.dt');

        assert.ok(spy.called, "updatePaginationTitles was called on draw");
        spy.restore();
    });
});