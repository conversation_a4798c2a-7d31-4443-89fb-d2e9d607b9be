using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DynamicDashboardMapRepositoryTests : IClassFixture<DynamicDashboardMapFixture>
{
    private readonly DynamicDashboardMapFixture _dynamicDashboardMapFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DynamicDashboardMapRepository _repository;
    private readonly DynamicDashboardMapRepository _repositoryNotParent;

    public DynamicDashboardMapRepositoryTests(DynamicDashboardMapFixture dynamicDashboardMapFixture)
    {
        _dynamicDashboardMapFixture = dynamicDashboardMapFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DynamicDashboardMapRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DynamicDashboardMapRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dynamicDashboardMap = _dynamicDashboardMapFixture.DynamicDashboardMapDto;

        // Act
        var result = await _repository.AddAsync(dynamicDashboardMap);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardMap.DashBoardSubName, result.DashBoardSubName);
        Assert.Single(_dbContext.DynamicDashboardMaps);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "TestDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        dynamicDashboardMap.DashBoardSubName = "UpdatedDashboardSubName";

        // Act
        var result = await _repository.UpdateAsync(dynamicDashboardMap);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDashboardSubName", result.DashBoardSubName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "TestDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.DeleteAsync(dynamicDashboardMap);

        // Assert
        Assert.Equal(dynamicDashboardMap.DashBoardSubName, result.DashBoardSubName);
        Assert.Empty(_dbContext.DynamicDashboardMaps);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "TestDashboardSubName",
            IsActive = true
        };
        var addedEntity = await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.DashBoardSubName, result.DashBoardSubName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "TestDashboardSubName",
            IsActive = true
        };
        var addedEntity = await _repositoryNotParent.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "TestDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dynamicDashboardMap.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardMap.ReferenceId, result.ReferenceId);
        Assert.Equal(dynamicDashboardMap.DashBoardSubName, result.DashBoardSubName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dynamicDashboardMaps = new List<DynamicDashboardMap>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName2", IsActive = true }
        };
        await _repository.AddRangeAsync(dynamicDashboardMaps);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboardMaps.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dynamicDashboardMaps = new List<DynamicDashboardMap>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName2", IsActive = true }
        };
        await _repositoryNotParent.AddRangeAsync(dynamicDashboardMaps);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "ExistingDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsNameExist("ExistingDashboardSubName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentDashboardSubName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "SameDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsNameExist("SameDashboardSubName", dynamicDashboardMap.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var dynamicDashboardMap1 = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "ExistingDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap1);

        var dynamicDashboardMap2 = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubName = "DifferentDashboardSubName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap2);

        // Act
        var result = await _repository.IsNameExist("ExistingDashboardSubName", dynamicDashboardMap2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsDefaultDashboardByUserId Tests

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldReturnEntity_WhenDefaultDashboardExists()
    {
        // Arrange
        var userId = "USER_123";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = userId,
            DashBoardSubName = "DefaultUserDashboard",
            IsDefault = true,
            IsView = true,
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByUserId(userId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(userId, result.UserId);
        Assert.True(result.IsDefault);
        Assert.True(result.IsView);
    }

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldReturnNull_WhenNoDefaultDashboardExists()
    {
        // Arrange
        var userId = "USER_456";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = userId,
            DashBoardSubName = "NonDefaultUserDashboard",
            IsDefault = false,
            IsView = true,
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByUserId(userId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldReturnNull_WhenUserNotExists()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByUserId("NON_EXISTENT_USER");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldReturnNull_WhenIsViewIsFalse()
    {
        // Arrange
        var userId = "USER_789";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = userId,
            DashBoardSubName = "DefaultUserDashboard",
            IsDefault = true,
            IsView = false, // Not viewable
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByUserId(userId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldReturnNull_WhenIsActiveIsFalse()
    {
        // Arrange
        var userId = "USER_101";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = userId,
            DashBoardSubName = "DefaultUserDashboard",
            IsDefault = true,
            IsView = true,
            IsActive = false // Not active
        };
        await _dbContext.DynamicDashboardMaps.AddAsync(dynamicDashboardMap);

        _dbContext.SaveChanges();
        // Act
        var result = await _repository.IsDefaultDashboardByUserId(userId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region IsDefaultDashboardByRoleId Tests

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldReturnEntity_WhenDefaultDashboardExists()
    {
        // Arrange
        var roleId = "ROLE_123";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            RoleId = roleId,
            DashBoardSubName = "DefaultRoleDashboard",
            IsDefault = true,
            IsView = true,
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByRoleId(roleId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roleId, result.RoleId);
        Assert.True(result.IsDefault);
        Assert.True(result.IsView);
    }

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldReturnNull_WhenNoDefaultDashboardExists()
    {
        // Arrange
        var roleId = "ROLE_456";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            RoleId = roleId,
            DashBoardSubName = "NonDefaultRoleDashboard",
            IsDefault = false,
            IsView = true,
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByRoleId(roleId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldReturnNull_WhenRoleNotExists()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByRoleId("NON_EXISTENT_ROLE");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldReturnNull_WhenIsViewIsFalse()
    {
        // Arrange
        var roleId = "ROLE_789";
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            RoleId = roleId,
            DashBoardSubName = "DefaultRoleDashboard",
            IsDefault = true,
            IsView = false, // Not viewable
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.IsDefaultDashboardByRoleId(roleId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region MapDynamicDashboardMap Tests

    [Fact]
    public async Task ListAllAsync_ShouldMapDynamicSubDashboardName_WhenDynamicSubDashboardExists()
    {
        // Arrange
        var dashboardSubId = Guid.NewGuid().ToString();

        // Add DynamicSubDashboard first
        var dynamicSubDashboard = new DynamicSubDashboard
        {
            ReferenceId = dashboardSubId,
            Name = "MappedSubDashboardName",
            IsActive = true
        };
        await _dbContext.DynamicSubDashboards.AddAsync(dynamicSubDashboard);
        await _dbContext.SaveChangesAsync();

        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubId = dashboardSubId,
            DashBoardSubName = "OriginalName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("MappedSubDashboardName", result.First().DashBoardSubName);
    }

    [Fact]
    public async Task ListAllAsync_ShouldKeepOriginalName_WhenDynamicSubDashboardNotExists()
    {
        // Arrange
        var dynamicDashboardMap = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DashBoardSubId = Guid.NewGuid().ToString(), // Non-existent sub dashboard
            DashBoardSubName = "OriginalName",
            IsActive = true
        };
        await _repository.AddAsync(dynamicDashboardMap);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("OriginalName", result.First().DashBoardSubName);
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldHandleNullUserId()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByUserId(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByUserId_ShouldHandleEmptyUserId()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByUserId("");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldHandleNullRoleId()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByRoleId(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsDefaultDashboardByRoleId_ShouldHandleEmptyRoleId()
    {
        // Act
        var result = await _repository.IsDefaultDashboardByRoleId("");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullName()
    {
        // Act
        var result = await _repository.IsNameExist(null, "valid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleEmptyName()
    {
        // Act
        var result = await _repository.IsNameExist("", "valid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleMultipleDefaultDashboards_ForDifferentUsers()
    {
        // Arrange
        var user1Id = "USER_001";
        var user2Id = "USER_002";

        var dashboard1 = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = user1Id,
            DashBoardSubName = "User1DefaultDashboard",
            IsDefault = true,
            IsView = true,
            IsActive = true
        };

        var dashboard2 = new DynamicDashboardMap
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserId = user2Id,
            DashBoardSubName = "User2DefaultDashboard",
            IsDefault = true,
            IsView = true,
            IsActive = true
        };

        await _repository.AddRangeAsync(new[] { dashboard1, dashboard2 });

        // Act
        var result1 = await _repository.IsDefaultDashboardByUserId(user1Id);
        var result2 = await _repository.IsDefaultDashboardByUserId(user2Id);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.Equal(user1Id, result1.UserId);
        Assert.Equal(user2Id, result2.UserId);
        Assert.NotEqual(result1.ReferenceId, result2.ReferenceId);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dynamicDashboardMaps = new List<DynamicDashboardMap>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), DashBoardSubName = "DashboardSubName3", IsActive = true }
        };

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dynamicDashboardMaps);
        var initialCount = dynamicDashboardMaps.Count;

        var toUpdate = dynamicDashboardMaps.Take(2).ToList();
        toUpdate.ForEach(x => x.DashBoardSubName = "UpdatedDashboardSubName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = dynamicDashboardMaps.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.DashBoardSubName == "UpdatedDashboardSubName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
