﻿using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Queries;

public class GetComponentTypeDetailQueryHandlerTests : IClassFixture<ComponentTypeFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;

    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;

    private readonly GetComponentTypeDetailQueryHandler _handler;

    public GetComponentTypeDetailQueryHandlerTests(ComponentTypeFixture componentTypeFixture)
    {
        _componentTypeFixture = componentTypeFixture;

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.GetComponentTypeRepository(_componentTypeFixture.ComponentTypes);

        _handler = new GetComponentTypeDetailQueryHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_ComponentTypeDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetComponentTypeDetailQuery { Id = _componentTypeFixture.ComponentTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<ComponentTypeDetailVm>();

        result.Id.ShouldBe(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        result.ComponentName.ShouldBe(_componentTypeFixture.ComponentTypes[0].ComponentName);
        result.FormTypeId.ShouldBe(_componentTypeFixture.ComponentTypes[0].FormTypeId);
        result.FormTypeName.ShouldBe(_componentTypeFixture.ComponentTypes[0].FormTypeName);

        result.Properties.ShouldBe(_componentTypeFixture.ComponentTypes[0].Properties);
        result.ComponentProperties.ShouldBe(_componentTypeFixture.ComponentTypes[0].ComponentProperties);
        result.Logo.ShouldBe(_componentTypeFixture.ComponentTypes[0].Logo);
        result.Version.ShouldBe(_componentTypeFixture.ComponentTypes[0].Version);
        result.IsCustom.ShouldBeFalse();
        result.IsReplication.ShouldBeFalse();
        result.IsServer.ShouldBeTrue();
        result.IsDatabase.ShouldBeTrue();

    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidComponentTypeId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetComponentTypeDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetComponentTypeDetailQuery { Id = _componentTypeFixture.ComponentTypes[0].ReferenceId }, CancellationToken.None);

        _mockComponentTypeRepository.Verify(x => x.GetComponentTypeById(It.IsAny<string>()), Times.Once);
    }

}
