﻿using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorLogsModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringLogs.Queries
{
    public class GetSVCGMMonitorLogPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorLogRepository> _mockSVCGMMonitorLogRepository;
        private readonly GetSVCGMMonitorLogPaginatedListQueryHandler _handler;

        public GetSVCGMMonitorLogPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorLogRepository = new Mock<ISVCGMMonitorLogRepository>();
            _handler = new GetSVCGMMonitorLogPaginatedListQueryHandler(_mockSVCGMMonitorLogRepository.Object, _mockMapper.Object);
        }

     
        [Fact]
        public async Task Handle_ShouldApplyPaginationCorrectly()
        {
            var request = new GetSVCGMMonitorLogPaginatedListQuery
            {
                SearchString = "",
                PageNumber = 2,
                PageSize = 1
            };

            var mockQueryable = new List<Domain.Entities.SVCGMMonitorLog>
        {
            new Domain.Entities.SVCGMMonitorLog { Id = 1,  },
            new Domain.Entities.SVCGMMonitorLog { Id = 2,  }
        }.AsQueryable().BuildMock();

            _mockSVCGMMonitorLogRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            var mappedList = new List<SVCGMMonitorLogsListVm>
        {
            new SVCGMMonitorLogsListVm { Id = Guid.NewGuid().ToString() }
        };

            _mockMapper
                .Setup(mapper => mapper.Map<SVCGMMonitorLogsListVm>(It.IsAny<Domain.Entities.SVCGMMonitorLog>()))
                .Returns((Domain.Entities.SVCGMMonitorLog src) => new SVCGMMonitorLogsListVm
                {
                    Id = Guid.NewGuid().ToString(),

                });

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);

        }
    }
}
