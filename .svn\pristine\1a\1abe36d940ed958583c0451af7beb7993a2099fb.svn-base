﻿using AutoFixture;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.ApiTests.Fixtures;

public class LicenseInfoFixture
{
    private readonly IFixture _fixture;

    public LicenseInfoFixture()
    {
        _fixture = new Fixture();
    }

    public List<LicenseInfoListVm> ListVm => _fixture.CreateMany<LicenseInfoListVm>(3).ToList();
    public LicenseInfoDetailViewVm DetailViewVm => _fixture.Create<LicenseInfoDetailViewVm>();
    public List<LicenseInfoDetailVm> DetailListVm => _fixture.CreateMany<LicenseInfoDetailVm>(2).ToList();
    public AvailableCountVm AvailableCount => _fixture.Create<AvailableCountVm>();
    public List<LicenseInfoByBusinessServiceIdListVm> ByBusinessServiceIdList => _fixture.CreateMany<LicenseInfoByBusinessServiceIdListVm>(2).ToList();
    public PaginatedResult<LicenseInfoListVm> PaginatedResult => _fixture.Create<PaginatedResult<LicenseInfoListVm>>();
    public List<LicenseInfoByEntityListVm> ByEntityList => _fixture.CreateMany<LicenseInfoByEntityListVm>(2).ToList();
    public List<LicenseInfoTypeListVm> ByTypeList => _fixture.CreateMany<LicenseInfoTypeListVm>(2).ToList();

    public CreateLicenseInfoCommand CreateCommand => _fixture.Create<CreateLicenseInfoCommand>();
    public UpdateLicenseInfoCommand UpdateCommand => _fixture.Create<UpdateLicenseInfoCommand>();
    public GetLicenseInfoPaginatedListQuery PaginatedQuery => _fixture.Create<GetLicenseInfoPaginatedListQuery>();
    public BaseResponse BaseResponse => _fixture.Create<BaseResponse>();

    public string Id => Guid.NewGuid().ToString();
    public string Entity => _fixture.Create<string>();
    public string Type => _fixture.Create<string>();
    public string? EntityType => _fixture.Create<string>();
}
