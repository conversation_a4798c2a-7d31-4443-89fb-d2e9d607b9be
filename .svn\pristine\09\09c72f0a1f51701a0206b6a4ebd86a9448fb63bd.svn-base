using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Paginated;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.CyberResiliency.Controllers;

[Collection("ComponentGroupTests")]
public class ComponentGroupControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<ILogger<ComponentGroupController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly ComponentGroupController _controller;

    public ComponentGroupControllerShould()
    {
        _controller = new ComponentGroupController(
            _mockLogger.Object,
            _mockDataProvider.Object,
            _mockPublisher.Object,
            _mockMapper.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.List();

        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberComponentGroupPaginatedEvent>(), default), Times.Once);
        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public async Task List_ThrowsException_And_LogsError()
    {
        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberComponentGroupPaginatedEvent>(), default))
            .ThrowsAsync(new Exception("Test exception"));

        await Assert.ThrowsAsync<Exception>(() => _controller.List());

        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberComponentGroupPaginatedEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_WithEmptyId_CreatesNewComponentGroup_And_ReturnsJsonResult()
    {
        var viewModel = new CyberComponentGroupViewModel
        {
            GroupName = "Test Group",
            ComponentProperties = "Test Properties",
            SiteId = "site-123",
            SiteName = "Test Site"
        };

        var command = new CreateCyberComponentGroupCommand
        {
            GroupName = "Test Group",
            ComponentProperties = "Test Properties",
            SiteId = "site-123",
            SiteName = "Test Site"
        };

        var response = new BaseResponse { Success = true, Message = "Created successfully" };

        _mockMapper.Setup(x => x.Map<CreateCyberComponentGroupCommand>(viewModel)).Returns(command);
        _mockDataProvider.Setup(x => x.CyberComponentGroup.CreateAsync(command)).ReturnsAsync(response);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = response });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberComponentGroup.CreateAsync(command), Times.Once);
        _mockMapper.Verify(x => x.Map<CreateCyberComponentGroupCommand>(viewModel), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_WithId_UpdatesComponentGroup_And_ReturnsJsonResult()
    {
        var viewModel = new CyberComponentGroupViewModel
        {
            Id = "group-123",
            GroupName = "Updated Group",
            ComponentProperties = "Updated Properties",
            SiteId = "site-123",
            SiteName = "Test Site"
        };

        var command = new UpdateCyberComponentGroupCommand
        {
            Id = "group-123",
            GroupName = "Updated Group",
            ComponentProperties = "Updated Properties",
            SiteId = "site-123",
            SiteName = "Test Site"
        };

        var response = new BaseResponse { Success = true, Message = "Updated successfully" };

        _mockMapper.Setup(x => x.Map<UpdateCyberComponentGroupCommand>(viewModel)).Returns(command);
        _mockDataProvider.Setup(x => x.CyberComponentGroup.UpdateAsync(command)).ReturnsAsync(response);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "group-123" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = response });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberComponentGroup.UpdateAsync(command), Times.Once);
        _mockMapper.Verify(x => x.Map<UpdateCyberComponentGroupCommand>(viewModel), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_WithValidationException_ReturnsJsonException()
    {
        var viewModel = new CyberComponentGroupViewModel
        {
            GroupName = "Test Group"
        };

        var failures = new List<ValidationFailure>
        {
            new ValidationFailure("GroupName", "Group name is required.")
        };
        var validationResult = new ValidationResult(failures);
        var validationException = new ValidationException(validationResult);

        _mockMapper.Setup(x => x.Map<CreateCyberComponentGroupCommand>(viewModel))
            .Throws(validationException);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Validation error on CyberComponentGroup page")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_WithGeneralException_ReturnsJsonException()
    {
        var viewModel = new CyberComponentGroupViewModel
        {
            GroupName = "Test Group"
        };

        var exception = new Exception("General error");

        _mockMapper.Setup(x => x.Map<CreateCyberComponentGroupCommand>(viewModel))
            .Throws(exception);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberComponentGroup page while processing the request for create or update.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task GetPagination_ReturnsJsonResult_WithPaginatedData()
    {
        var query = new GetCyberComponentGroupPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var paginatedResult = new PaginatedResult<CyberComponentGroupListVm>
        {
            Data = new List<CyberComponentGroupListVm>
            {
                new() { Id = "1", GroupName = "Group 1", SiteId = "site-1", SiteName = "Site 1" },
                new() { Id = "2", GroupName = "Group 2", SiteId = "site-2", SiteName = "Site 2" }
            },
            TotalCount = 2,
            CurrentPage = 1,
            PageSize = 10
        };

        _mockDataProvider.Setup(x => x.CyberComponentGroup.GetPaginatedCyberComponentGroups(query))
            .ReturnsAsync(paginatedResult);

        var result = await _controller.GetPagination(query);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(paginatedResult);
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberComponentGroup.GetPaginatedCyberComponentGroups(query), Times.Once);
    }

    [Fact]
    public async Task GetPagination_ThrowsException_ReturnsJsonException()
    {
        var query = new GetCyberComponentGroupPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberComponentGroup.GetPaginatedCyberComponentGroups(query))
            .ThrowsAsync(exception);

        var result = await _controller.GetPagination(query);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberComponentGroup page while processing the pagination request.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_ReturnsJsonResult_WithComponentData()
    {
        var siteId = "site-123";
        var componentData = new List<CyberComponentBySiteIdVm>
        {
            new() { Id = "comp-1", Name = "Component 1", SiteId = siteId, SiteName = "Test Site" },
            new() { Id = "comp-2", Name = "Component 2", SiteId = siteId, SiteName = "Test Site" }
        };

        _mockDataProvider.Setup(x => x.CyberComponent.GetCyberComponentBySiteId(siteId))
            .ReturnsAsync(componentData);

        var result = await _controller.GetCyberComponentBySiteId(siteId);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = componentData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberComponent.GetCyberComponentBySiteId(siteId), Times.Once);
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_ThrowsException_ReturnsJsonException()
    {
        var siteId = "site-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberComponent.GetCyberComponentBySiteId(siteId))
            .ThrowsAsync(exception);

        var result = await _controller.GetCyberComponentBySiteId(siteId);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on Cyber Component Group page while retrieving cyber component List.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task GetServerBySiteId_ReturnsJsonResult_WithServerData()
    {
        var siteId = "site-123";
        var serverData = new List<ServerListVm>
        {
            new() { Id = "server-1", Name = "Server 1", SiteId = siteId, SiteName = "Test Site" },
            new() { Id = "server-2", Name = "Server 2", SiteId = siteId, SiteName = "Test Site" }
        };

        _mockDataProvider.Setup(x => x.Server.GetServerBySiteId(siteId))
            .ReturnsAsync(serverData);

        var result = await _controller.GetServerBySiteId(siteId);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = serverData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.Server.GetServerBySiteId(siteId), Times.Once);
    }

    [Fact]
    public async Task GetServerBySiteId_ThrowsException_ReturnsJsonException()
    {
        var siteId = "site-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.Server.GetServerBySiteId(siteId))
            .ThrowsAsync(exception);

        var result = await _controller.GetServerBySiteId(siteId);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on Cyber Component Group page while retrieving Server List.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task Delete_ReturnsJsonResult_WithSuccessResponse()
    {
        var componentGroupId = "group-123";
        var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

        _mockDataProvider.Setup(x => x.CyberComponentGroup.DeleteAsync(componentGroupId))
            .ReturnsAsync(response);

        var result = await _controller.Delete(componentGroupId);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = response });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberComponentGroup.DeleteAsync(componentGroupId), Times.Once);
    }

    [Fact]
    public async Task Delete_ThrowsException_ReturnsJsonException()
    {
        var componentGroupId = "group-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberComponentGroup.DeleteAsync(componentGroupId))
            .ThrowsAsync(exception);

        var result = await _controller.Delete(componentGroupId);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred while deleting record on cyber component group.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task IsComponentGroupNameExist_ReturnsTrue_WhenNameExists()
    {
        var name = "Test Group";
        var id = "group-123";

        _mockDataProvider.Setup(x => x.CyberComponentGroup.IsCyberComponentGroupNameExist(name, id))
            .ReturnsAsync(true);

        var result = await _controller.IsComponentGroupNameExist(name, id);

        Assert.True(result);
        _mockDataProvider.Verify(x => x.CyberComponentGroup.IsCyberComponentGroupNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsComponentGroupNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        var name = "Test Group";
        var id = "group-123";

        _mockDataProvider.Setup(x => x.CyberComponentGroup.IsCyberComponentGroupNameExist(name, id))
            .ReturnsAsync(false);

        var result = await _controller.IsComponentGroupNameExist(name, id);

        Assert.False(result);
        _mockDataProvider.Verify(x => x.CyberComponentGroup.IsCyberComponentGroupNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsComponentGroupNameExist_ThrowsException_ReturnsFalse()
    {
        var name = "Test Group";
        var id = "group-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberComponentGroup.IsCyberComponentGroupNameExist(name, id))
            .ThrowsAsync(exception);

        var result = await _controller.IsComponentGroupNameExist(name, id);

        Assert.False(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"An error occurred on Cyber Component Group while checking if Component group name exists for : {name}.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }
}
