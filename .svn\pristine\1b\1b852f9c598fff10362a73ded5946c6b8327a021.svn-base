﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetList;
using ContinuityPatrol.Services.Db.Impl.Orchestration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Orchestration;

public class WorkflowPermissionServiceTests : BaseServiceTestSetup<WorkflowPermissionService>, IClassFixture<WorkflowPermissionServiceFixture>
{
    private readonly WorkflowPermissionServiceFixture _fixture;

    public WorkflowPermissionServiceTests(WorkflowPermissionServiceFixture fixture)
    {
        InitializeService(accessor => new WorkflowPermissionService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateWorkflowPermissionResponse
        {
            Message = "Created",
            WorkflowPermissionId = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Success()
    {
        var response = new DeleteWorkflowPermissionResponse
        {
            Message = "Deleted"
        };
        _fixture.DeleteCommand.Id = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<DeleteWorkflowPermissionCommand>(c => c.Id == _fixture.DeleteCommand.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(_fixture.DeleteCommand.Id);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateWorkflowPermissionResponse
        {
            Message = "Updated",
            WorkflowPermissionId = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.UpdateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task GetWorkflowPermissions_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowPermissionListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PermissionList);

        var result = await ServiceUnderTest.GetWorkflowPermissions();

        Assert.Equal(_fixture.PermissionList.Count, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Data()
    {
        _fixture.DetailQuery.Id = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowPermissionDetailQuery>(q => q.Id == _fixture.DetailQuery.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailResponse);

        var result = await ServiceUnderTest.GetByReferenceId(_fixture.DetailQuery.Id);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetPaginatedWorkflowPermissions_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResponse);

        var result = await ServiceUnderTest.GetPaginatedWorkflowPermissions(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResponse.TotalCount, result.TotalCount);
    }
}
