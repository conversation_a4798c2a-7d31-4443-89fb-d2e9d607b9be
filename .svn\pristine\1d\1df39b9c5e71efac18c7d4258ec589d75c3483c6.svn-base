using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class IncidentLogsFixture : IDisposable
{
    public List<IncidentLogsListVm> IncidentLogsListVm { get; }
    public PaginatedResult<IncidentLogsListVm> PaginatedIncidentLogsListVm { get; }
    public IncidentLogsDetailVm IncidentLogsDetailVm { get; }
    public CreateIncidentLogsCommand CreateIncidentLogsCommand { get; }
    public UpdateIncidentLogsCommand UpdateIncidentLogsCommand { get; }
    public GetIncidentLogsPaginatedQuery GetIncidentLogsPaginatedQuery { get; }

    public IncidentLogsFixture()
    {
        var fixture = new Fixture();

        IncidentLogsListVm = fixture.Create<List<IncidentLogsListVm>>();
        PaginatedIncidentLogsListVm = fixture.Create<PaginatedResult<IncidentLogsListVm>>();
        IncidentLogsDetailVm = fixture.Create<IncidentLogsDetailVm>();
        CreateIncidentLogsCommand = fixture.Create<CreateIncidentLogsCommand>();
        UpdateIncidentLogsCommand = fixture.Create<UpdateIncidentLogsCommand>();
        GetIncidentLogsPaginatedQuery = fixture.Create<GetIncidentLogsPaginatedQuery>();
    }

    public void Dispose()
    {

    }
}
