using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetList;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetStartTimeEndTimeByUserId;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class UserActivitiesControllerTests : IClassFixture<UserActivityFixture>
{
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly UserActivitiesController _controller;

    public UserActivitiesControllerTests(UserActivityFixture userActivityFixture)
    {
        _userActivityFixture = userActivityFixture;
        
        var testBuilder = new ControllerTestBuilder<UserActivitiesController>();
        _controller = testBuilder.CreateController(
            _ => new UserActivitiesController(),
            out _mediatorMock);
    }

    #region GetUserActivities Tests

    [Fact]
    public async Task GetUserActivities_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserActivityListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_userActivityFixture.UserActivityListVm);

        // Act
        var result = await _controller.GetUserActivities();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var userActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Equal(5, userActivities.Count);
        Assert.Contains(userActivities, ua => ua.LoginName == "admin");
        Assert.Contains(userActivities, ua => ua.LoginName == "manager");
        Assert.Contains(userActivities, ua => ua.LoginName == "operator");
        Assert.Contains(userActivities, ua => ua.LoginName == "viewer");
        Assert.All(userActivities, ua => Assert.NotNull(ua.Entity));
        Assert.All(userActivities, ua => Assert.NotNull(ua.ActivityType));
        Assert.All(userActivities, ua => Assert.NotNull(ua.HostAddress));
    }

    [Fact]
    public async Task GetUserActivities_ReturnsEmptyList_WhenNoActivitiesExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserActivityListQuery>(), default))
            .ReturnsAsync(new List<UserActivityListVm>());

        // Act
        var result = await _controller.GetUserActivities();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<UserActivityListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetUserActivities_ReturnsActivitiesWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserActivityListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_userActivityFixture.UserActivityListVm);

        // Act
        var result = await _controller.GetUserActivities();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var userActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        
        // Verify different activity types
        Assert.Contains(userActivities, ua => ua.ActivityType == "Create");
        Assert.Contains(userActivities, ua => ua.ActivityType == "Update");
        Assert.Contains(userActivities, ua => ua.ActivityType == "Delete");
        Assert.Contains(userActivities, ua => ua.ActivityType == "View");
        Assert.Contains(userActivities, ua => ua.ActivityType == "Login");
        
        // Verify different entities
        Assert.Contains(userActivities, ua => ua.Entity == "User");
        Assert.Contains(userActivities, ua => ua.Entity == "Company");
        Assert.Contains(userActivities, ua => ua.Entity == "Site");
        Assert.Contains(userActivities, ua => ua.Entity == "Dashboard");
        Assert.Contains(userActivities, ua => ua.Entity == "Authentication");
        
        // Verify all activities have required fields
        Assert.All(userActivities, ua => Assert.NotNull(ua.RequestUrl));
        Assert.All(userActivities, ua => Assert.NotNull(ua.ActivityDetails));
        Assert.All(userActivities, ua => Assert.NotNull(ua.CreatedDate));
    }

    #endregion

    #region CreateUserActivity Tests

    [Fact]
    public async Task CreateUserActivity_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userActivityFixture.CreateUserActivityCommand;
        var expectedResponse = _userActivityFixture.CreateUserActivityResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateUserActivity(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateUserActivityResponse>(createdResult.Value);
        Assert.Equal("User activity created successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    
    [Fact]
    public async Task CreateUserActivity_WithValidActivityData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateUserActivityCommand
        {
            LoginName = "testuser",
            Entity = "InfraObject",
            Action = "Create Infrastructure Object",
            ActivityType = "Create",
            CompanyId = "COMPANY_001",
            RequestUrl = "/api/v6/infraobjects",
            HostAddress = "*************",
            ActivityDetails = "Created new infrastructure object for production environment",
            UserId = "USER_TEST"
        };

        var expectedResponse = new CreateUserActivityResponse
        {
            Id = Guid.NewGuid().ToString(),
            Success = true,
            Message = "User activity created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateUserActivity(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateUserActivityResponse>(createdResult.Value);
        Assert.Equal("User activity created successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    #endregion

    #region GetLoginName Tests

    [Fact]
    public async Task GetLoginName_WithValidLoginName_ReturnsOkResult()
    {
        // Arrange
        var loginName = "admin";
        var expectedActivities = _userActivityFixture.UserActivityLoginNameVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserActivityLoginNameQuery>(q => q.LoginName == loginName), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedActivities);

        // Act
        var result = await _controller.GetLoginName(loginName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityLoginNameVm>>(okResult.Value);
        Assert.Equal(2, returnedActivities.Count);
        Assert.All(returnedActivities, ua => Assert.Equal("admin", ua.LoginName));
        Assert.Contains(returnedActivities, ua => ua.Action == "Create User");
        Assert.Contains(returnedActivities, ua => ua.Action == "Delete Site");
    }

   
    [Fact]
    public async Task GetLoginName_WithEmptyLoginName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetLoginName(""));
    }

    [Fact]
    public async Task GetLoginName_WithWhitespaceLoginName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetLoginName("   "));
    }

    [Fact]
    public async Task GetLoginName_WithNonExistentLoginName_ReturnsEmptyList()
    {
        // Arrange
        var loginName = "nonexistentuser";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserActivityLoginNameQuery>(q => q.LoginName == loginName), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserActivityLoginNameVm>());

        // Act
        var result = await _controller.GetLoginName(loginName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityLoginNameVm>>(okResult.Value);
        Assert.Empty(returnedActivities);
    }

    [Fact]
    public async Task GetLoginName_WithValidUser_ReturnsUserSpecificActivities()
    {
        // Arrange
        var loginName = "manager";
        var managerActivities = new List<UserActivityLoginNameVm>
        {
            new UserActivityLoginNameVm
            {
                Id = "UA_002",
                Action = "Update Company",
                LoginName = "manager",
                Entity = "Company",
                ActivityType = "Update",
                RequestUrl = "/api/v6/companies",
                HostAddress = "*************",
                ActivityDetails = "Updated company settings for ACME Corp",
                CreatedDate = DateTime.UtcNow.AddHours(-4).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserActivityLoginNameQuery>(q => q.LoginName == loginName), It.IsAny<CancellationToken>()))
            .ReturnsAsync(managerActivities);

        // Act
        var result = await _controller.GetLoginName(loginName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityLoginNameVm>>(okResult.Value);
        Assert.Single(returnedActivities);
        Assert.Equal("manager", returnedActivities.First().LoginName);
        Assert.Equal("Update Company", returnedActivities.First().Action);
        Assert.Equal("Company", returnedActivities.First().Entity);
    }

    #endregion

    #region GetStartTimeEndTimeByUser Tests

    [Fact]
    public async Task GetStartTimeEndTimeByUser_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var loginName = "admin";
        var createDate = "2023-01-01";
        var lastModifiedDate = "2023-01-31";
        var expectedActivities = _userActivityFixture.UserActivityListVm.Take(2).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetStartTimeEndTimeByUserIdQuery>(
                q => q.LoginName == loginName && q.StartDate == createDate && q.EndDate == lastModifiedDate), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedActivities);

        // Act
        var result = await _controller.GetStartTimeEndTimeByUser(loginName, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Equal(2, returnedActivities.Count);
        Assert.All(returnedActivities, ua => Assert.NotNull(ua.CreatedDate));
        Assert.All(returnedActivities, ua => Assert.NotNull(ua.LoginName));
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_WithNullLoginName_ReturnsAllActivities()
    {
        // Arrange
        string? loginName = null;
        var createDate = "2023-01-01";
        var lastModifiedDate = "2023-01-31";
        var expectedActivities = _userActivityFixture.UserActivityListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetStartTimeEndTimeByUserIdQuery>(
                q => q.LoginName == loginName && q.StartDate == createDate && q.EndDate == lastModifiedDate), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedActivities);

        // Act
        var result = await _controller.GetStartTimeEndTimeByUser(loginName, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Equal(5, returnedActivities.Count);
        
        // Verify different users are included when loginName is null
        var distinctUsers = returnedActivities.Select(ua => ua.LoginName).Distinct().ToList();
        Assert.True(distinctUsers.Count > 1);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_WithEmptyLoginName_ReturnsAllActivities()
    {
        // Arrange
        var loginName = "";
        var createDate = "2023-01-01";
        var lastModifiedDate = "2023-01-31";
        var expectedActivities = _userActivityFixture.UserActivityListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetStartTimeEndTimeByUserIdQuery>(
                q => q.LoginName == loginName && q.StartDate == createDate && q.EndDate == lastModifiedDate), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedActivities);

        // Act
        var result = await _controller.GetStartTimeEndTimeByUser(loginName, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Equal(5, returnedActivities.Count);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_WithDateRange_ReturnsFilteredActivities()
    {
        // Arrange
        var loginName = "operator";
        var createDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var lastModifiedDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
        var recentActivities = new List<UserActivityListVm>
        {
            new UserActivityListVm
            {
                Id = "UA_004",
                Action = "View Dashboard",
                LoginName = "operator",
                Entity = "Dashboard",
                ActivityType = "View",
                RequestUrl = "/dashboard",
                HostAddress = "*************",
                ActivityDetails = "Accessed main dashboard",
                CreatedDate = DateTime.UtcNow.AddMinutes(-30).ToString("yyyy-MM-dd HH:mm:ss"),
                LastModifiedDate = DateTime.UtcNow.AddMinutes(-30)
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetStartTimeEndTimeByUserIdQuery>(
                q => q.LoginName == loginName && q.StartDate == createDate && q.EndDate == lastModifiedDate), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(recentActivities);

        // Act
        var result = await _controller.GetStartTimeEndTimeByUser(loginName, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Single(returnedActivities);
        Assert.Equal("operator", returnedActivities.First().LoginName);
        Assert.Equal("View Dashboard", returnedActivities.First().Action);
    }

    [Fact]
    public async Task GetStartTimeEndTimeByUser_WithNoActivitiesInRange_ReturnsEmptyList()
    {
        // Arrange
        var loginName = "admin";
        var createDate = "2020-01-01";
        var lastModifiedDate = "2020-01-31";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetStartTimeEndTimeByUserIdQuery>(
                q => q.LoginName == loginName && q.StartDate == createDate && q.EndDate == lastModifiedDate), 
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserActivityListVm>());

        // Act
        var result = await _controller.GetStartTimeEndTimeByUser(loginName, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedActivities = Assert.IsAssignableFrom<List<UserActivityListVm>>(okResult.Value);
        Assert.Empty(returnedActivities);
    }

    #endregion
}
