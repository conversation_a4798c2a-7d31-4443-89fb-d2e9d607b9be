using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class ImpactActivityService : IImpactActivityService
{
    private readonly IBaseClient _client;

    public ImpactActivityService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<List<ImpactActivityListVm>> GetImpactActivityList()
    {
        var request = new RestRequest("api/v6/impactactivities");

        return await _client.GetFromCache<List<ImpactActivityListVm>>(request, "GetImpactActivityList");
    }

    public async Task<BaseResponse> CreateAsync(CreateImpactActivityCommand createImpactActivityCommand)
    {
        var request = new RestRequest("api/v6/impactactivities", Method.Post);

        request.AddJsonBody(createImpactActivityCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateImpactActivityCommand updateImpactActivityCommand)
    {
        var request = new RestRequest("api/v6/impactactivities", Method.Put);

        request.AddJsonBody(updateImpactActivityCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/impactactivities/{id}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<ImpactActivityDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/impactactivities/{id}");

        return await _client.Get<ImpactActivityDetailVm>(request);
    }
     #region NameExist
  //  public async Task<bool> IsImpactActivityNameExist(string name, string? id)
  //  {
  //     var request = new RestRequest($"api/v6/impactactivitys/name-exist?impactactivityName={name}&id={id}");
  //
  //     return await Get<bool>(request);
  //  }
    #endregion

    #region Paginated
  //  public async Task<PaginatedResult<ImpactActivityListVm>> GetPaginatedImpactActivitys(GetImpactActivityPaginatedListQuery query)
  //  {
  //      var request = new RestRequest("api/v6/impactactivitys/paginated-list");
  //
  //      return await Get<PaginatedResult<ImpactActivityListVm>>(request);
  //  }
   #endregion
}
