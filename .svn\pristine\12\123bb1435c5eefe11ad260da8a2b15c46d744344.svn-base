﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetByInfraObjectAndActionId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries;

public class GetWorkflowActionResultByInfraObjectAndActionIdQueryHandlerTests
{
    private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler _handler;

    public GetWorkflowActionResultByInfraObjectAndActionIdQueryHandlerTests()
    {
        _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler(
            _mockWorkflowActionResultRepository.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnWorkflowActionResult_WhenEntityExistsAndIsActive()
    {
        var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
        {
            InfraobjectId = Guid.NewGuid().ToString(),
            ActionId = Guid.NewGuid().ToString()
        };

        var workflowActionResult = new Domain.Entities.WorkflowActionResult
        {
            ReferenceId = Guid.NewGuid().ToString(),
            WorkflowActionName = "TestAction",
            IsActive = true
        };

        var expectedViewModel = new WorkflowActionResultByInfraObjectAndActionIdVm
        {
            Id = workflowActionResult.ReferenceId,
            WorkflowActionName = workflowActionResult.WorkflowActionName
        };

        _mockWorkflowActionResultRepository
            .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
            .ReturnsAsync(workflowActionResult);

        _mockMapper
            .Setup(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(workflowActionResult))
            .Returns(expectedViewModel);

        var result = await _handler.Handle(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expectedViewModel.Id, result.Id);
        Assert.Equal(expectedViewModel.WorkflowActionName, result.WorkflowActionName);

        _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(workflowActionResult), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
    {
        var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
        {
            InfraobjectId = Guid.NewGuid().ToString(),
            ActionId = Guid.NewGuid().ToString()
        };

        _mockWorkflowActionResultRepository
            .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
            .ReturnsAsync((Domain.Entities.WorkflowActionResult)null!);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

        _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsInactive()
    {
        var request = new GetWorkflowActionResultByInfraObjectAndActionIdQuery
        {
            InfraobjectId = Guid.NewGuid().ToString(),
            ActionId = Guid.NewGuid().ToString()
        };

        var inactiveWorkflowActionResult = new Domain.Entities.WorkflowActionResult
        {
            ReferenceId = Guid.NewGuid().ToString(),
            WorkflowActionName = "TestAction",
            IsActive = false
        };

        _mockWorkflowActionResultRepository
            .Setup(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId))
            .ReturnsAsync(inactiveWorkflowActionResult);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

        _mockWorkflowActionResultRepository.Verify(repo => repo.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Never);
    }
    [Fact]
    public void All_Properties_Should_Be_Set_Correctly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var model = new WorkflowActionResultByInfraObjectAndActionIdVm
        {
            Id = "id-123",
            WorkflowActionName = "ActionA",
            CompanyId = "company-001",
            WorkflowOperationId = "op-001",
            WorkflowOperationName = "Operation A",
            WorkflowOperationGroupId = "group-001",
            WorkflowOperationGroupName = "Group A",
            InfraObjectId = "infra-001",
            InfraObjectName = "Infra A",
            ActionId = "action-001",
            StepId = "step-001",
            StartRto = 5,
            ConditionActionId = 10,
            StartTime = now,
            EndTime = now.AddMinutes(5),
            Status = "Success",
            Message = "Step executed successfully",
            SkipStep = true,
            IsReload = 1,
            IsRetry = false,
            IsParallel = true,
            Direction = "Forward",
            Version = "v1.0",
            NodeId = "node-001",
            NodeName = "Node A"
        };

        // Assert
        Assert.Equal("id-123", model.Id);
        Assert.Equal("ActionA", model.WorkflowActionName);
        Assert.Equal("company-001", model.CompanyId);
        Assert.Equal("op-001", model.WorkflowOperationId);
        Assert.Equal("Operation A", model.WorkflowOperationName);
        Assert.Equal("group-001", model.WorkflowOperationGroupId);
        Assert.Equal("Group A", model.WorkflowOperationGroupName);
        Assert.Equal("infra-001", model.InfraObjectId);
        Assert.Equal("Infra A", model.InfraObjectName);
        Assert.Equal("action-001", model.ActionId);
        Assert.Equal("step-001", model.StepId);
        Assert.Equal(5, model.StartRto);
        Assert.Equal(10, model.ConditionActionId);
        Assert.Equal(now, model.StartTime);
        Assert.Equal(now.AddMinutes(5), model.EndTime);
        Assert.Equal("Success", model.Status);
        Assert.Equal("Step executed successfully", model.Message);
        Assert.True(model.SkipStep);
        Assert.Equal(1, model.IsReload);
        Assert.False(model.IsRetry);
        Assert.True(model.IsParallel);
        Assert.Equal("Forward", model.Direction);
        Assert.Equal("v1.0", model.Version);
        Assert.Equal("node-001", model.NodeId);
        Assert.Equal("Node A", model.NodeName);
    }

}