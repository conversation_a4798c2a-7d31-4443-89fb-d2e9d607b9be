using ContinuityPatrol.Application.Features.CyberAlert.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Events;

public class CreateCyberAlertEventTests : IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<CyberAlertCreatedEventHandler>> _mockLogger;
    private readonly CyberAlertCreatedEventHandler _handler;

    public CreateCyberAlertEventTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockUserActivityRepository = CyberRepositoryMocks.CreateUserActivityRepository(_cyberAlertFixture.UserActivities);
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<CyberAlertCreatedEventHandler>>();

        // Setup default user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("TestUser123");
        _mockUserService.Setup(x => x.LoginName).Returns("TestUser123");

        _handler = new CyberAlertCreatedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ProcessCyberAlertCreatedEvent_When_ValidEvent()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "Test Security Alert Created"
        };

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities .UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivityWithCorrectCreateProperties_When_ValidEvent()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "alert-001"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "TestAlert_Cancellation"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

       
    }

    [Fact]
    public async Task Handle_ProcessMultipleCreateEvents_When_ValidEvents()
    {
        // Arrange
        var events = new[]
        {
            new CyberAlertCreatedEvent
            {
                Name = "TestAlert_Multiple_1",
            },
            new CyberAlertCreatedEvent
            {
                Name = "TestAlert_Multiple_2",
            },
            new CyberAlertCreatedEvent
            {
                Name = "TestAlert_Multiple_3",
            }
        };

        // Act
        foreach (var eventItem in events)
        {
            await _handler.Handle(eventItem, CancellationToken.None);
        }

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(3));
    }

   
    [Fact]
    public async Task Handle_CreateCompleteCreateUserActivity_When_EventWithAllProperties()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "CompleteCreateTestAlert"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    
    [Fact]
    public async Task Handle_HandleDifferentSeverityLevels_When_EventsWithVariousSeverities()
    {
        // Arrange
        var severityLevels = new[] { "Low", "Medium", "High", "Critical" };

        foreach (var severity in severityLevels)
        {
            var cyberAlertEvent = new CyberAlertCreatedEvent
            {
                Name = "Open"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(cyberAlertEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_HandleDifferentAlertTypes_When_EventsWithVariousTypes()
    {
        // Arrange
        var alertTypes = new[] { "Intrusion", "Malware", "DDoS", "Phishing", "Vulnerability" };

        foreach (var alertType in alertTypes)
        {
            var cyberAlertEvent = new CyberAlertCreatedEvent
            {
                Name = "Medium"
            };

            Domain.Entities.UserActivity createdActivity = null;
            _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
                .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

            // Act
            await _handler.Handle(cyberAlertEvent, CancellationToken.None);

            // Assert
            createdActivity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_HandleComplexAlertData_When_EventWithComplexJson()
    {
        // Arrange
        var complexAlertData = @"{
            ""source_ip"": ""*************"",
            ""destination_ip"": ""*********"",
            ""port"": 443,
            ""protocol"": ""HTTPS"",
            ""attack_vector"": ""SQL Injection"",
            ""confidence_score"": 0.95,
            ""metadata"": {
                ""user_agent"": ""Mozilla/5.0"",
                ""request_method"": ""POST""
            }
        }";

        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "TestAlert_ComplexData"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: Event uses logged-in user service for user identification
    /// Expected: User activity uses current logged-in user information
    /// </summary>
    [Fact]
    public async Task Handle_UseLoggedInUserService_When_ValidEvent()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "UserServiceTestAlert"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
        
        // Verify user service was called
        _mockUserService.Verify(x => x.UserId, Times.AtLeastOnce);
        _mockUserService.Verify(x => x.LoginName, Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_ProcessRapidCreateEvents_When_MultipleEventsInSuccession()
    {
        // Arrange
        var events = Enumerable.Range(1, 10).Select(i => new CyberAlertCreatedEvent
        {
            Name = "Medium",
        }).ToList();

        // Act
        var tasks = events.Select(evt => _handler.Handle(evt, CancellationToken.None));
        await Task.WhenAll(tasks);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(10));
    }

    /// <summary>
    /// Test: Event handler logs activity with proper entity reference
    /// Expected: Activity details contain alert ID for reference
    /// </summary>
    [Fact]
    public async Task Handle_LogActivityWithEntityReference_When_ValidEvent()
    {
        // Arrange
        var alertId = "entity-reference-alert-id";
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "EntityReferenceTestAlert"
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleDifferentUserServiceStates_When_ValidEvent()
    {
        // Arrange - Setup different user service behavior
        _mockUserService.Setup(x => x.UserId).Returns("DifferentUser456");
        _mockUserService.Setup(x => x.LoginName).Returns("DifferentLoginName");

        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "DifferentUserTestAlert",
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_HandleSpecialCharactersInEvent_When_EventWithSpecialChars()
    {
        // Arrange
        var cyberAlertEvent = new CyberAlertCreatedEvent
        {
            Name = "High",
        };

        Domain.Entities.UserActivity createdActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(activity => createdActivity = activity)
            .ReturnsAsync((Domain.Entities.UserActivity activity) => activity);

        // Act
        await _handler.Handle(cyberAlertEvent, CancellationToken.None);

        // Assert
        createdActivity.ShouldNotBeNull();
    }
}
