﻿using AutoMapper;
using ContinuityPatrol.Application.Features.DashboardView.Event.OperationalAnalyticsView;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Extension;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers;

[Collection("AnalyticsTests")]
public class AnalyticsControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<ILogger<AnalyticsController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly AnalyticsController _controller;

    public AnalyticsControllerShould()
    {
        _controller = new AnalyticsController(
            _mockDataProvider.Object,
            _mockPublisher.Object,
            _mockLogger.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task Index_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.Index();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<OperationalAnalyticsEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithValidData_ReturnsJsonResult()
    {
        var drillAnalyticsData = new { DrillCount = 5, SuccessfulDrills = 4, FailedDrills = 1 };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(drillAnalyticsData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = drillAnalyticsData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithValidData_ReturnsJsonResult()
    {
        var componentFailureData = new { TotalComponents = 10, FailedComponents = 2, HealthyComponents = 8 };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(componentFailureData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = componentFailureData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithValidData_ReturnsJsonResult()
    {
        var availabilityData = new { AvailabilityPercentage = 99.5, UptimeHours = 720, DowntimeHours = 3.6 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(availabilityData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = availabilityData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithValidData_ReturnsJsonResult()
    {
        var workflowData = new { TotalWorkflows = 15, ActiveWorkflows = 12, InactiveWorkflows = 3 };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(workflowData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = workflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithValidData_ReturnsJsonResult()
    {
        var slaData = new { TotalSLAs = 20, BreachedSLAs = 3, ComplianceSLAs = 17 };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(slaData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = slaData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ThrowsAsync(exception);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithValidData_ReturnsJsonResult()
    {
        var healthSummaryData = new { OverallHealth = "Good", CriticalIssues = 1, WarningIssues = 3, HealthyServices = 25 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(healthSummaryData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = healthSummaryData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync((object)null);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync((object)null);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync((object)null);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync((object)null);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync((object)null);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithNullData_ReturnsJsonResult()
    {
        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync((object)null);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = (object)null });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithDatabaseException_ReturnsJsonException()
    {
        var exception = new InvalidOperationException("Database connection failed");

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithTimeoutException_ReturnsJsonException()
    {
        var exception = new TimeoutException("Request timeout");

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithArgumentException_ReturnsJsonException()
    {
        var exception = new ArgumentException("Invalid parameter");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithUnauthorizedAccessException_ReturnsJsonException()
    {
        var exception = new UnauthorizedAccessException("Access denied");

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNotSupportedException_ReturnsJsonException()
    {
        var exception = new NotSupportedException("Operation not supported");

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ThrowsAsync(exception);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithNullReferenceException_ReturnsJsonException()
    {
        var exception = new NullReferenceException("Object reference not set");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLog(LogLevel.Error, "An error occurred on server type page", Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithComplexData_ReturnsJsonResult()
    {
        var complexDrillData = new
        {
            DrillCount = 25,
            SuccessfulDrills = 20,
            FailedDrills = 5,
            DrillTypes = new[] { "Fire", "Earthquake", "Cyber", "Power Outage" },
            LastDrillDate = DateTime.Now.AddDays(-7),
            NextScheduledDrill = DateTime.Now.AddDays(30),
            AverageResponseTime = 4.5,
            ComplianceScore = 85.7
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(complexDrillData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = complexDrillData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithDetailedMetrics_ReturnsJsonResult()
    {
        var detailedFailureData = new
        {
            TotalComponents = 150,
            FailedComponents = 8,
            HealthyComponents = 142,
            CriticalFailures = 2,
            WarningComponents = 15,
            ComponentCategories = new[] { "Database", "Web Server", "Application Server", "Load Balancer" },
            FailureRate = 5.33,
            MTTR = 2.5, // Mean Time To Recovery in hours
            MTBF = 720.0 // Mean Time Between Failures in hours
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(detailedFailureData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = detailedFailureData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithExtendedMetrics_ReturnsJsonResult()
    {
        var extendedAvailabilityData = new
        {
            AvailabilityPercentage = 99.95,
            UptimeHours = 8759.6,
            DowntimeHours = 0.4,
            PlannedDowntime = 0.2,
            UnplannedDowntime = 0.2,
            SLATarget = 99.9,
            SLACompliance = true,
            AvailabilityTrend = "Improving",
            LastIncidentDate = DateTime.Now.AddDays(-45)
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(extendedAvailabilityData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = extendedAvailabilityData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithComprehensiveData_ReturnsJsonResult()
    {
        var comprehensiveWorkflowData = new
        {
            TotalWorkflows = 45,
            ActiveWorkflows = 38,
            InactiveWorkflows = 7,
            AutomatedWorkflows = 32,
            ManualWorkflows = 13,
            WorkflowCategories = new[] { "Backup", "Recovery", "Monitoring", "Maintenance", "Security" },
            AverageExecutionTime = 12.5,
            SuccessRate = 96.8,
            FailedExecutions = 3
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(comprehensiveWorkflowData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = comprehensiveWorkflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithDetailedBreachData_ReturnsJsonResult()
    {
        var detailedSlaData = new
        {
            TotalSLAs = 35,
            BreachedSLAs = 4,
            ComplianceSLAs = 31,
            CriticalBreaches = 1,
            MinorBreaches = 3,
            SLATypes = new[] { "Availability", "Performance", "Recovery Time", "Response Time" },
            OverallComplianceRate = 88.6,
            WorstPerformingSLA = "Database Recovery Time",
            BestPerformingSLA = "Web Application Availability"
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(detailedSlaData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = detailedSlaData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithComprehensiveHealthData_ReturnsJsonResult()
    {
        var comprehensiveHealthData = new
        {
            OverallHealth = "Excellent",
            CriticalIssues = 0,
            WarningIssues = 2,
            HealthyServices = 48,
            HealthScore = 95.5,
            ServiceCategories = new[] { "Database", "Web", "Application", "Network", "Storage" },
            TrendDirection = "Stable",
            LastHealthCheck = DateTime.Now.AddMinutes(-5),
            NextScheduledCheck = DateTime.Now.AddMinutes(55)
        };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(comprehensiveHealthData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = comprehensiveHealthData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task Index_VerifiesEventPublishing_AndLogging()
    {
        var result = await _controller.Index();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<OperationalAnalyticsEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithEmptyData_ReturnsJsonResult()
    {
        var emptyData = new { };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(emptyData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = emptyData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithZeroValues_ReturnsJsonResult()
    {
        var zeroData = new { TotalComponents = 0, FailedComponents = 0, HealthyComponents = 0 };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(zeroData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = zeroData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithPerfectUptime_ReturnsJsonResult()
    {
        var perfectUptimeData = new { AvailabilityPercentage = 100.0, UptimeHours = 8760, DowntimeHours = 0 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(perfectUptimeData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = perfectUptimeData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithAllInactiveWorkflows_ReturnsJsonResult()
    {
        var allInactiveData = new { TotalWorkflows = 10, ActiveWorkflows = 0, InactiveWorkflows = 10 };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(allInactiveData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = allInactiveData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithNoBreaches_ReturnsJsonResult()
    {
        var noBreachData = new { TotalSLAs = 50, BreachedSLAs = 0, ComplianceSLAs = 50 };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(noBreachData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = noBreachData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithCriticalIssues_ReturnsJsonResult()
    {
        var criticalHealthData = new { OverallHealth = "Critical", CriticalIssues = 5, WarningIssues = 10, HealthyServices = 15 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(criticalHealthData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = criticalHealthData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }
}