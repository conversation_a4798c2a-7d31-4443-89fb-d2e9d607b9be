﻿using AutoMapper;
using ContinuityPatrol.Application.Features.DashboardView.Event.OperationalAnalyticsView;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers;

[Collection("AnalyticsTests")]
public class AnalyticsControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<ILogger<AnalyticsController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly AnalyticsController _controller;

    public AnalyticsControllerShould()
    {
        _controller = new AnalyticsController(
            _mockDataProvider.Object,
            _mockPublisher.Object,
            _mockLogger.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task Index_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.Index();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);

        _mockPublisher.Verify(x => x.Publish(It.IsAny<OperationalAnalyticsEvent>(), default), Times.Once);
        _mockLogger.VerifyLoggerWasCalled(LogLevel.Debug, Times.Never);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithValidData_ReturnsJsonResult()
    {
        var drillAnalyticsData = new { DrillCount = 5, SuccessfulDrills = 4, FailedDrills = 1 };

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ReturnsAsync(drillAnalyticsData);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = drillAnalyticsData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetDrillAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetDrillAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetDrillAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetDrillAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithValidData_ReturnsJsonResult()
    {
        var componentFailureData = new { TotalComponents = 10, FailedComponents = 2, HealthyComponents = 8 };

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ReturnsAsync(componentFailureData);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = componentFailureData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetComponentFailureAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetComponentFailureAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetComponentFailureAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithValidData_ReturnsJsonResult()
    {
        var availabilityData = new { AvailabilityPercentage = 99.5, UptimeHours = 720, DowntimeHours = 3.6 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ReturnsAsync(availabilityData);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = availabilityData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalAvailabilityAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalAvailabilityAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalAvailabilityAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithValidData_ReturnsJsonResult()
    {
        var workflowData = new { TotalWorkflows = 15, ActiveWorkflows = 12, InactiveWorkflows = 3 };

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ReturnsAsync(workflowData);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = workflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetWorkflowAnalytics(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetWorkflowAnalytics())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowAnalytics();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithValidData_ReturnsJsonResult()
    {
        var slaData = new { TotalSLAs = 20, BreachedSLAs = 3, ComplianceSLAs = 17 };

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ReturnsAsync(slaData);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = slaData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetSlaBreach(), Times.Once);
    }

    [Fact]
    public async Task GetSlaBreach_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetSlaBreach())
            .ThrowsAsync(exception);

        var result = await _controller.GetSlaBreach();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithValidData_ReturnsJsonResult()
    {
        var healthSummaryData = new { OverallHealth = "Good", CriticalIssues = 1, WarningIssues = 3, HealthyServices = 25 };

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ReturnsAsync(healthSummaryData);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { success = true, data = healthSummaryData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.DashboardView.GetOperationalServiceHealthSummary(), Times.Once);
    }

    [Fact]
    public async Task GetOperationalHealthSummary_WithException_ReturnsJsonException()
    {
        var exception = new Exception("Test exception");

        _mockDataProvider.Setup(x => x.DashboardView.GetOperationalServiceHealthSummary())
            .ThrowsAsync(exception);

        var result = await _controller.GetOperationalHealthSummary();

        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        _mockLogger.VerifyLoggerWasCalled(LogLevel.Error, Times.Once);
    }
}