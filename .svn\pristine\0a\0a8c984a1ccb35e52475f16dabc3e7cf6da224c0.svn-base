﻿using ContinuityPatrol.Application.Features.User.Events.ForgotPassword;
using ContinuityPatrol.Shared.Tests.Extension;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Events;

public class ForgotPasswordUpdatedEventTests
{
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILogger<ForgotPasswordUpdatedEventHandler>> _mockLogger;
    private readonly ForgotPasswordUpdatedEventHandler _handler;

    public ForgotPasswordUpdatedEventTests()
    {
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockLogger = new Mock<ILogger<ForgotPasswordUpdatedEventHandler>>();

        _handler = new ForgotPasswordUpdatedEventHandler(
            _mockUserService.Object,
            _mockUserActivityRepository.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldLogPasswordResetSuccessfully()
    {
        var updatedEvent = new ForgotPasswordUpdatedEvent
        {
            UserName = "testuser"
        };

        _mockUserService.Setup(service => service.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.LoginName).Returns("testuser");
        _mockUserService.Setup(service => service.RequestedUrl).Returns("http://localhost");
        _mockUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.IpAddress).Returns("***********");

        await _handler.Handle(updatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);

        _mockLogger.VerifyLog(LogLevel.Information, "Password reset successfully", Times.Once());
    }

    [Fact]
    public async Task Handle_ShouldAddUserActivityWithCorrectDetails()
    {
        var updatedEvent = new ForgotPasswordUpdatedEvent
        {
            UserName = "testuser"
        };

        _mockUserService.Setup(service => service.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.LoginName).Returns("testuser");
        _mockUserService.Setup(service => service.RequestedUrl).Returns("http://localhost");
        _mockUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.IpAddress).Returns("***********");

        await _handler.Handle(updatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldNotThrowException_WhenUserServiceDataIsNull()
    {
        var updatedEvent = new ForgotPasswordUpdatedEvent
        {
            UserName = "testuser"
        };

        _mockUserService.Setup(service => service.UserId).Returns((string?)null!);
        _mockUserService.Setup(service => service.LoginName).Returns((string)null!);
        _mockUserService.Setup(service => service.RequestedUrl).Returns((string)null!);
        _mockUserService.Setup(service => service.CompanyId).Returns((string?)null!);
        _mockUserService.Setup(service => service.IpAddress).Returns((string)null!);

        await _handler.Handle(updatedEvent, CancellationToken.None);
    }

    [Fact]
    public async Task Handle_ShouldHandleEmptyUserNameInEventGracefully()
    {
        var updatedEvent = new ForgotPasswordUpdatedEvent
        {
            UserName = string.Empty
        };

        _mockUserService.Setup(service => service.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.LoginName).Returns("testuser");
        _mockUserService.Setup(service => service.RequestedUrl).Returns("http://localhost");
        _mockUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(service => service.IpAddress).Returns("***********");

        await _handler.Handle(updatedEvent, CancellationToken.None);

        _mockLogger.VerifyLog(LogLevel.Information, "Password reset successfully", Times.Once());
    }
}