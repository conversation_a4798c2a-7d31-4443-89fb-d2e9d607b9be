﻿using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationGroupId;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Queries;
public class GetBulkImportActionResultByGroupIdQueryHandlerTests
{
    private readonly Mock<IBulkImportActionResultRepository> _mockRepo;
    private readonly IMapper _mapper;
    private readonly GetBulkImportActionResultByGroupIdQueryHandler _handler;

    public GetBulkImportActionResultByGroupIdQueryHandlerTests()
    {
        _mockRepo = new Mock<IBulkImportActionResultRepository>();

        var config = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Domain.Entities.BulkImportActionResult, BulkImportActionResultListVm>();
        });
        _mapper = config.CreateMapper();

        _handler = new GetBulkImportActionResultByGroupIdQueryHandler(_mapper, _mockRepo.Object);
    }

    [Fact]
    public async Task Handle_ReturnsMappedList_WhenDataExists()
    {
        // Arrange
        var groupId = "group123";
        var dataList = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { Id = 1, NodeName = "Node A", Status = "Completed" },
            new Domain.Entities.BulkImportActionResult { Id = 2, NodeName = "Node B", Status = "Failed" }
        };

        _mockRepo.Setup(repo => repo.GetBulkImportActionResultOperationGroupId(groupId))
                 .ReturnsAsync(dataList);

        var request = new GetBulkImportActionResultByGroupIdQuery { BulkImportOperationGroupId = groupId };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal("Node A", result[0].NodeName);
        Assert.Equal("Node B", result[1].NodeName);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
    {
        // Arrange
        var groupId = "group-empty";
        _mockRepo.Setup(repo => repo.GetBulkImportActionResultOperationGroupId(groupId))
                 .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        var request = new GetBulkImportActionResultByGroupIdQuery { BulkImportOperationGroupId = groupId };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
}
