﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Events.Import;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Events;

public class ImportFormTypeCategoryEventTests : IClassFixture<FormTypeCategoryFixture>, IClassFixture<UserActivityFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILogger<FormTypeCategoryImportedEventHandler>> _mockLogger;
    private readonly FormTypeCategoryImportedEventHandler _handler;

    public ImportFormTypeCategoryEventTests(FormTypeCategoryFixture formTypeCategoryFixture, UserActivityFixture userActivityFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns("123");
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("456");

        _mockLogger = new Mock<ILogger<FormTypeCategoryImportedEventHandler>>();

        _mockUserActivityRepository = UserActivityRepositoryMocks.CreateUserActivityRepository(_userActivityFixture.UserActivities);

        _handler = new FormTypeCategoryImportedEventHandler(mockLoggedInUserService.Object, _mockLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public void Constructor_Should_Create_Handler_Successfully()
    {
        // Act & Assert
        _handler.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_Complete_Successfully()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert - No exception should be thrown
        Assert.True(true);
    }

    [Fact]
    public async Task Handle_Should_Call_Repository_AddAsync()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_Entity()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Entity.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_Action()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_ActivityType()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityType.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_ActivityDetails()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_Map_UserService_Properties_Correctly()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
   
    }

    [Fact]
    public async Task Handle_Should_Complete_With_CancellationToken()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var notification = new FormTypeCategoryImportedEvent();
        var cancellationToken = new CancellationToken();

        // Act & Assert
        await _handler.Handle(notification, cancellationToken);
        Assert.True(true);
    }
}