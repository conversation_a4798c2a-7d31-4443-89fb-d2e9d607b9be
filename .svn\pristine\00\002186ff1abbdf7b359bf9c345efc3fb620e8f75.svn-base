﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Verify;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Validators;

public class UpdateWorkflowVerifyCommandValidatorTests
{
    private readonly Mock<IWorkflowOperationGroupRepository> _workflowOperationGroupRepositoryMock = new();
    private readonly UpdateWorkflowVerifyCommandValidator _validator;

    public UpdateWorkflowVerifyCommandValidatorTests()
    {
        _validator = new UpdateWorkflowVerifyCommandValidator(_workflowOperationGroupRepositoryMock.Object);
    }
    [Fact]
    public async Task Should_Have_Error_When_Id_Is_Empty()
    {
        var command = new UpdateWorkflowVerifyCommand
        {
            Id = ""
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(x => x.Id)
              .WithErrorMessage("Id is required.");
    }

    [Fact]
    public async Task Should_Have_Error_When_Id_Is_Invalid_Guid()
    {
        var command = new UpdateWorkflowVerifyCommand
        {
            Id = "invalid-guid"
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(x => x.Id)
              .WithErrorMessage("Id is invalid.");
    }

    [Fact]
    public async Task Should_Have_Error_When_Workflow_Not_Executed()
    {
        var command = new UpdateWorkflowVerifyCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        _workflowOperationGroupRepositoryMock
            .Setup(x => x.IsWorkflowIdExist(command.Id))
            .ReturnsAsync(false);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(x => x)
              .WithErrorMessage("Drill operation was not performed in this Workflow.");
    }

    [Fact]
    public async Task Should_Not_Have_Error_When_Command_Is_Valid()
    {
        var command = new UpdateWorkflowVerifyCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        _workflowOperationGroupRepositoryMock
            .Setup(x => x.IsWorkflowIdExist(command.Id))
            .ReturnsAsync(true);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(null)]
    [InlineData(" ")]
    [InlineData("\t")]
    public async Task Should_Have_Error_When_Id_Is_NullOrWhiteSpace(string id)
    {
        var command = new UpdateWorkflowVerifyCommand { Id = id };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(x => x.Id)
            .WithErrorMessage("Id is required.");
    }
    [Fact]
    public async Task Should_Have_Error_When_Valid_Id_But_Workflow_Not_Executed()
    {
        var id = Guid.NewGuid().ToString();
        var command = new UpdateWorkflowVerifyCommand { Id = id };

        _workflowOperationGroupRepositoryMock
            .Setup(x => x.IsWorkflowIdExist(id))
            .ReturnsAsync(false);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(x => x)
            .WithErrorMessage("Drill operation was not performed in this Workflow.");
    }
    [Fact]
    public async Task Should_Not_Have_Error_For_Valid_Id_And_Executed_Workflow()
    {
        var id = Guid.NewGuid().ToString();
        var command = new UpdateWorkflowVerifyCommand { Id = id };

        _workflowOperationGroupRepositoryMock
            .Setup(x => x.IsWorkflowIdExist(id))
            .ReturnsAsync(true);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldNotHaveAnyValidationErrors();
    }
    [Fact]
    public async Task Should_Validate_Long_But_Valid_Guid()
    {
        var id = Guid.NewGuid().ToString();
        var command = new UpdateWorkflowVerifyCommand { Id = id };

        _workflowOperationGroupRepositoryMock
            .Setup(x => x.IsWorkflowIdExist(id))
            .ReturnsAsync(true);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldNotHaveAnyValidationErrors();
    }

}