using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Create;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Events.Paginated;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.CyberResiliency.Controllers;

[Collection("JobManagementTests")]
public class JobManagementControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<ILogger<JobManagementController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly JobManagementController _controller;

    public JobManagementControllerShould()
    {
        _controller = new JobManagementController(
            _mockPublisher.Object,
            _mockLogger.Object,
            _mockMapper.Object,
            _mockDataProvider.Object
        );

        _controller.ControllerContext = new ControllerContextMocks().Default();

        WebHelper.UserSession = new UserSession
        {
            CompanyId = "test-company-123",
            LoggedUserId = "test-user-123",
            LoginName = "DemoUser"
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_PublishesEvent_And_ReturnsView()
    {
        var result = await _controller.List();

        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberJobManagementPaginatedEvent>(), default), Times.Once);
        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public async Task List_ThrowsException_And_LogsError()
    {
        _mockPublisher.Setup(x => x.Publish(It.IsAny<CyberJobManagementPaginatedEvent>(), default))
            .ThrowsAsync(new Exception("Test exception"));

        await Assert.ThrowsAsync<Exception>(() => _controller.List());

        _mockPublisher.Verify(x => x.Publish(It.IsAny<CyberJobManagementPaginatedEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginated_ReturnsJsonResult_WithPaginatedData()
    {
        var query = new GetCyberJobManagementPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var paginatedResult = new PaginatedResult<CyberJobManagementListVm>
        {
            Data = new List<CyberJobManagementListVm>
            {
                new() { Id = "1", Name = "Job 1", Status = "Pending", State = "Active" },
                new() { Id = "2", Name = "Job 2", Status = "Running", State = "Active" }
            },
            TotalCount = 2,
            CurrentPage = 1,
            PageSize = 10
        };

        _mockDataProvider.Setup(x => x.CyberJobManagement.GetPaginatedCyberJobManagements(query))
            .ReturnsAsync(paginatedResult);

        var result = await _controller.GetPaginated(query);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(paginatedResult);
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberJobManagement.GetPaginatedCyberJobManagements(query), Times.Once);
    }

    [Fact]
    public async Task GetPaginated_ThrowsException_ReturnsJsonException()
    {
        var query = new GetCyberJobManagementPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberJobManagement.GetPaginatedCyberJobManagements(query))
            .ThrowsAsync(exception);

        var result = await _controller.GetPaginated(query);

        Assert.IsType<JsonResult>(result);
    }

    [Fact]
    public async Task CreateOrUpdate_WithEmptyId_CreatesNewJob_And_RedirectsToList()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Name = "Test Job",
            AirgapId = "airgap-123",
            WorkflowId = "workflow-123"
        };

        var command = new CreateCyberJobManagementCommand
        {
            Name = "Test Job",
            AirgapId = "airgap-123",
            WorkflowId = "workflow-123",
            Status = "Pending"
        };

        var response = new BaseResponse { Success = true, Message = "Created successfully" };

        _mockMapper.Setup(x => x.Map<CreateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>())).Returns(command);
        _mockDataProvider.Setup(x => x.CyberJobManagement.CreateAsync(command)).ReturnsAsync(response);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_WithId_UpdatesJob_And_RedirectsToList()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Id = "job-123",
            Name = "Updated Job",
            AirgapId = "airgap-123",
            WorkflowId = "workflow-123"
        };

        var command = new UpdateCyberJobManagementCommand
        {
            Id = "job-123",
            Name = "Updated Job",
            AirgapId = "airgap-123",
            WorkflowId = "workflow-123",
            Status = "Pending"
        };

        var response = new BaseResponse { Success = true, Message = "Updated successfully" };

        _mockMapper.Setup(x => x.Map<UpdateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>())).Returns(command);
        _mockDataProvider.Setup(x => x.CyberJobManagement.UpdateAsync(command)).ReturnsAsync(response);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "job-123" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_WithValidationException_RedirectsToList()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Name = "Test Job"
        };

        var failures = new List<ValidationFailure>
        {
            new ValidationFailure("Name", "Validation failed for Name")
        };

        var validationResult = new ValidationResult(failures);
        var validationException = new ValidationException(validationResult);

        _mockMapper.Setup(x => x.Map<CreateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>()))
            .Throws(validationException);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Validation error on CyberJobManagement page")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_WithGeneralException_RedirectsToList()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Name = "Test Job"
        };

        var exception = new Exception("General error");

        _mockMapper.Setup(x => x.Map<CreateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>()))
            .Throws(exception);

        var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "id", "" }
        });
        _controller.ControllerContext.HttpContext.Request.Form = formCollection;

        var result = await _controller.CreateOrUpdate(viewModel);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task IsJobManagementExist_ReturnsTrue_WhenNameExists()
    {
        var name = "Test Job";
        var id = "job-123";

        _mockDataProvider.Setup(x => x.CyberJobManagement.IsCyberJobManagementNameExist(name, id))
            .ReturnsAsync(true);

        var result = await _controller.IsJobManagementExist(name, id);

        Assert.True(result);
        _mockDataProvider.Verify(x => x.CyberJobManagement.IsCyberJobManagementNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsJobManagementExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        var name = "Test Job";
        var id = "job-123";

        _mockDataProvider.Setup(x => x.CyberJobManagement.IsCyberJobManagementNameExist(name, id))
            .ReturnsAsync(false);

        var result = await _controller.IsJobManagementExist(name, id);

        Assert.False(result);
        _mockDataProvider.Verify(x => x.CyberJobManagement.IsCyberJobManagementNameExist(name, id), Times.Once);
    }

    [Fact]
    public async Task IsJobManagementExist_ThrowsException_ReturnsFalse()
    {
        var name = "Test Job";
        var id = "job-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberJobManagement.IsCyberJobManagementNameExist(name, id))
            .ThrowsAsync(exception);

        var result = await _controller.IsJobManagementExist(name, id);

        Assert.False(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"An error occurred on CyberJobManagement while checking if cyber job name exists for : {name}.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task GetWorkflowNames_ReturnsJsonResult_WithWorkflowData()
    {
        var workflowData = new List<WorkflowNameVm>
        {
            new() { Id = "1", Name = "Workflow 1" },
            new() { Id = "2", Name = "Workflow 2" }
        };

        _mockDataProvider.Setup(x => x.Workflow.GetWorkflowNames())
            .ReturnsAsync(workflowData);

        var result = await _controller.GetWorkflowNames();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = workflowData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.Workflow.GetWorkflowNames(), Times.Once);
    }

    [Fact]
    public async Task GetWorkflowNames_ThrowsException_ReturnsJsonException()
    {
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.Workflow.GetWorkflowNames())
            .ThrowsAsync(exception);

        var result = await _controller.GetWorkflowNames();

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberJobManagement page while retrieving workflow names.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task GetReplicationNames_ReturnsJsonResult_WithReplicationData()
    {
        var replicationData = new List<ReplicationNameVm>
        {
            new() { Id = "1", Name = "Replication 1" },
            new() { Id = "2", Name = "Replication 2" }
        };

        _mockDataProvider.Setup(x => x.Replication.GetReplicationNames())
            .ReturnsAsync(replicationData);

        var result = await _controller.GetReplicationNames();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = replicationData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.Replication.GetReplicationNames(), Times.Once);
    }

    [Fact]
    public async Task GetReplicationNames_ThrowsException_ReturnsJsonException()
    {
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.Replication.GetReplicationNames())
            .ThrowsAsync(exception);

        var result = await _controller.GetReplicationNames();

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberJobManagement page while retrieving replication names.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task Delete_ReturnsRedirectToList_WithSuccessMessage()
    {
        var jobId = "job-123";
        var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

        _mockDataProvider.Setup(x => x.CyberJobManagement.DeleteAsync(jobId))
            .ReturnsAsync(response);

        var result = await _controller.Delete(jobId);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task Delete_ThrowsException_RedirectsToList()
    {
        var jobId = "job-123";
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberJobManagement.DeleteAsync(jobId))
            .ThrowsAsync(exception);

        var result = await _controller.Delete(jobId);

        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task GetAirGapList_ReturnsJsonResult_WithAirGapData()
    {
        var airGapData = new List<CyberAirGapListVm>
        {
            new() { Id = "1", Name = "AirGap 1", SourceSiteId = "site-1", SourceSiteName = "Site 1" },
            new() { Id = "2", Name = "AirGap 2", SourceSiteId = "site-2", SourceSiteName = "Site 2" }
        };

        _mockDataProvider.Setup(x => x.CyberAirGap.GetCyberAirGapList())
            .ReturnsAsync(airGapData);

        var result = await _controller.GetAirGapList();

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = airGapData });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberAirGap.GetCyberAirGapList(), Times.Once);
    }

    [Fact]
    public async Task GetAirGapList_ThrowsException_ReturnsJsonException()
    {
        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberAirGap.GetCyberAirGapList())
            .ThrowsAsync(exception);

        var result = await _controller.GetAirGapList();

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberJobManagement page while retrieving air gap List.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task UpdateJobState_ReturnsJsonResult_WithSuccessResponse()
    {
        var command = new UpdateCyberJobManagementStateCommand
        {
            updateCyberJobStates = new List<UpdateCyberJobState>
            {
                new() { Id = "job-1", State = "Active" },
                new() { Id = "job-2", State = "Inactive" }
            }
        };

        var response = new BaseResponse { Success = true, Message = "State updated successfully" };

        _mockDataProvider.Setup(x => x.CyberJobManagement.UpdateState(command))
            .ReturnsAsync(response);

        var result = await _controller.UpdateJobState(command);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = response });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberJobManagement.UpdateState(command), Times.Once);
    }

    [Fact]
    public async Task UpdateJobState_ThrowsException_ReturnsJsonException()
    {
        var command = new UpdateCyberJobManagementStateCommand
        {
            updateCyberJobStates = new List<UpdateCyberJobState>
            {
                new() { Id = "job-1", State = "Active" }
            }
        };

        var exception = new Exception("Database error");

        _mockDataProvider.Setup(x => x.CyberJobManagement.UpdateState(command))
            .ThrowsAsync(exception);

        var result = await _controller.UpdateJobState(command);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberJobManagement page while updating job state.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }

    [Fact]
    public async Task ResetCyberJob_ReturnsJsonResult_WithSuccessResponse()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Id = "job-123",
            Name = "Test Job",
            AirgapId = "airgap-123"
        };

        var command = new UpdateCyberJobManagementCommand
        {
            Id = "job-123",
            Name = "Test Job",
            AirgapId = "airgap-123",
            Status = "Pending"
        };

        var response = new BaseResponse { Success = true, Message = "Job reset successfully" };

        _mockMapper.Setup(x => x.Map<UpdateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>())).Returns(command);
        _mockDataProvider.Setup(x => x.CyberJobManagement.UpdateAsync(command)).ReturnsAsync(response);

        var result = await _controller.ResetCyberJob(viewModel);

        var jsonResult = Assert.IsType<JsonResult>(result);
        var jsonData = JsonConvert.SerializeObject(jsonResult.Value);
        var expectedData = JsonConvert.SerializeObject(new { Success = true, data = response });
        Assert.Equal(expectedData, jsonData);

        _mockDataProvider.Verify(x => x.CyberJobManagement.UpdateAsync(It.IsAny<UpdateCyberJobManagementCommand>()), Times.Once);
        _mockMapper.Verify(x => x.Map<UpdateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>()), Times.Once);
    }

    [Fact]
    public async Task ResetCyberJob_ThrowsException_ReturnsJsonException()
    {
        var viewModel = new CyberJobManagementViewModel
        {
            Id = "job-123",
            Name = "Test Job"
        };

        var exception = new Exception("Database error");

        _mockMapper.Setup(x => x.Map<UpdateCyberJobManagementCommand>(It.IsAny<CyberJobManagementViewModel>()))
            .Throws(exception);

        var result = await _controller.ResetCyberJob(viewModel);

        Assert.IsType<JsonResult>(result);
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("An error occurred on CyberJobManagement page while reset job status.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }
}
