using AutoFixture;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Delete;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Update;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetList;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetNames;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetTeamMasterIdByTeamResource;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class TeamResourceFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<TeamResource> TeamResources { get; set; }
    public List<TeamResource> InvalidTeamResources { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<TeamResourceListVm> TeamResourceListVm { get; }
    public TeamResourceDetailVm TeamResourceDetailVm { get; }
    public List<TeamResourceNameVm> TeamResourceNameVm { get; }
    public List<TeamMasterIdByTeamResourceVm> TeamMasterIdByTeamResourceVm { get; }
    public PaginatedResult<TeamResourceListVm> TeamResourcePaginatedListVm { get; }

    // Commands
    public CreateTeamResourceCommand CreateTeamResourceCommand { get; set; }
    public UpdateTeamResourceCommand UpdateTeamResourceCommand { get; set; }
    public DeleteTeamResourceCommand DeleteTeamResourceCommand { get; set; }

    // Queries
    public GetTeamResourceDetailQuery GetTeamResourceDetailQuery { get; set; }
    public GetTeamResourceListQuery GetTeamResourceListQuery { get; set; }
    public GetTeamResourceNameQuery GetTeamResourceNameQuery { get; set; }
    public GetTeamResourceNameUniqueQuery GetTeamResourceNameUniqueQuery { get; set; }
    public GetTeamResourcePaginatedListQuery GetTeamResourcePaginatedListQuery { get; set; }
    public GetTeamMasterIdByTeamResourceQuery GetTeamMasterIdByTeamResourceQuery { get; set; }

    // Responses
    public CreateTeamResourceResponse CreateTeamResourceResponse { get; set; }
    public UpdateTeamResourceResponse UpdateTeamResourceResponse { get; set; }
    public DeleteTeamResourceResponse DeleteTeamResourceResponse { get; set; }

    // Unique Check Results
    public bool IsTeamResourceNameUniqueResult { get; set; }

    public TeamResourceFixture()
    {
        try
        {
            // Create test data using AutoFixture
            TeamResources = AutoTeamResourceFixture.Create<List<TeamResource>>();
            InvalidTeamResources = AutoTeamResourceFixture.Create<List<TeamResource>>();
            UserActivities = AutoTeamResourceFixture.Create<List<UserActivity>>();

            // Set invalid team resources to inactive
            foreach (var invalidTeamResource in InvalidTeamResources)
            {
                invalidTeamResource.IsActive = false;
            }

            // Commands
            CreateTeamResourceCommand = AutoTeamResourceFixture.Create<CreateTeamResourceCommand>();
            UpdateTeamResourceCommand = AutoTeamResourceFixture.Create<UpdateTeamResourceCommand>();
            DeleteTeamResourceCommand = AutoTeamResourceFixture.Create<DeleteTeamResourceCommand>();

            // Set command IDs to match existing entities
            if (TeamResources.Any())
            {
                UpdateTeamResourceCommand.Id = TeamResources.First().ReferenceId;
                DeleteTeamResourceCommand.Id = TeamResources.First().ReferenceId;
            }

            // Queries
            GetTeamResourceDetailQuery = AutoTeamResourceFixture.Create<GetTeamResourceDetailQuery>();
            GetTeamResourceListQuery = AutoTeamResourceFixture.Create<GetTeamResourceListQuery>();
            GetTeamResourceNameQuery = AutoTeamResourceFixture.Create<GetTeamResourceNameQuery>();
            GetTeamResourceNameUniqueQuery = AutoTeamResourceFixture.Create<GetTeamResourceNameUniqueQuery>();
            GetTeamResourcePaginatedListQuery = AutoTeamResourceFixture.Create<GetTeamResourcePaginatedListQuery>();
            GetTeamMasterIdByTeamResourceQuery = AutoTeamResourceFixture.Create<GetTeamMasterIdByTeamResourceQuery>();

            // Set query IDs to match existing entities
            if (TeamResources.Any())
            {
                GetTeamResourceDetailQuery.Id = TeamResources.First().ReferenceId;
                GetTeamMasterIdByTeamResourceQuery.TeamMasterId = TeamResources.First().TeamMasterId;
            }

            // Responses
            CreateTeamResourceResponse = AutoTeamResourceFixture.Create<CreateTeamResourceResponse>();
            UpdateTeamResourceResponse = AutoTeamResourceFixture.Create<UpdateTeamResourceResponse>();
            DeleteTeamResourceResponse = AutoTeamResourceFixture.Create<DeleteTeamResourceResponse>();

            // Unique check result
            IsTeamResourceNameUniqueResult = false; // Team resource name exists
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            TeamResources = new List<TeamResource>();
            InvalidTeamResources = new List<TeamResource>();
            UserActivities = new List<UserActivity>();
            CreateTeamResourceCommand = new CreateTeamResourceCommand();
            UpdateTeamResourceCommand = new UpdateTeamResourceCommand();
            DeleteTeamResourceCommand = new DeleteTeamResourceCommand();
            GetTeamResourceDetailQuery = new GetTeamResourceDetailQuery();
            GetTeamResourceListQuery = new GetTeamResourceListQuery();
            GetTeamResourceNameQuery = new GetTeamResourceNameQuery();
            GetTeamResourceNameUniqueQuery = new GetTeamResourceNameUniqueQuery();
            GetTeamResourcePaginatedListQuery = new GetTeamResourcePaginatedListQuery();
            GetTeamMasterIdByTeamResourceQuery = new GetTeamMasterIdByTeamResourceQuery();
            CreateTeamResourceResponse = new CreateTeamResourceResponse();
            UpdateTeamResourceResponse = new UpdateTeamResourceResponse();
            DeleteTeamResourceResponse = new DeleteTeamResourceResponse();
            IsTeamResourceNameUniqueResult = false;
        }

        // Configure View Models
        TeamResourceListVm = new List<TeamResourceListVm>
        {
            new TeamResourceListVm
            {
                Id = "TR_001",
                TeamMasterId = "TM_001",
                TeamMasterName = "Development Team",
                ResourceName = "John Developer",
                ResourceId = "DEV_001",
                Email = "<EMAIL>",
                Phone = "******-0101"
            },
            new TeamResourceListVm
            {
                Id = "TR_002",
                TeamMasterId = "TM_001",
                TeamMasterName = "Development Team",
                ResourceName = "Jane QA Engineer",
                ResourceId = "QA_001",
                Email = "<EMAIL>",
                Phone = "******-0102"
            },
            new TeamResourceListVm
            {
                Id = "TR_003",
                TeamMasterId = "TM_002",
                TeamMasterName = "Operations Team",
                ResourceName = "Bob DevOps",
                ResourceId = "DEVOPS_001",
                Email = "<EMAIL>",
                Phone = "******-0103"
            },
            new TeamResourceListVm
            {
                Id = "TR_004",
                TeamMasterId = "TM_002",
                TeamMasterName = "Operations Team",
                ResourceName = "Alice SysAdmin",
                ResourceId = "SYSADMIN_001",
                Email = "<EMAIL>",
                Phone = "******-0104"
            },
            new TeamResourceListVm
            {
                Id = "TR_005",
                TeamMasterId = "TM_003",
                TeamMasterName = "Security Team",
                ResourceName = "Charlie Security",
                ResourceId = "SEC_001",
                Email = "<EMAIL>",
                Phone = "******-0105"
            }
        };

        TeamResourceDetailVm = new TeamResourceDetailVm
        {
            Id = "TR_001",
            TeamMasterId = "TM_001",
            TeamMasterName = "Development Team",
            ResourceName = "John Developer",
            ResourceId = "DEV_001",
            Email = "<EMAIL>",
            Phone = "******-0101"
        };

        TeamResourceNameVm = new List<TeamResourceNameVm>
        {
            new TeamResourceNameVm { Id = "TR_001", ResourceName = "John Developer" },
            new TeamResourceNameVm { Id = "TR_002", ResourceName = "Jane QA Engineer" },
            new TeamResourceNameVm { Id = "TR_003", ResourceName = "Bob DevOps" },
            new TeamResourceNameVm { Id = "TR_004", ResourceName = "Alice SysAdmin" },
            new TeamResourceNameVm { Id = "TR_005", ResourceName = "Charlie Security" }
        };

        TeamMasterIdByTeamResourceVm = new List<TeamMasterIdByTeamResourceVm>
        {
            new TeamMasterIdByTeamResourceVm
            {
                Id = "TR_001",
                TeamMasterId = "TM_001",
                TeamMasterName = "Development Team",
                ResourceName = "John Developer",
                ResourceId = "DEV_001",
                Email = "<EMAIL>",
                Phone = "******-0101"
            },
            new TeamMasterIdByTeamResourceVm
            {
                Id = "TR_002",
                TeamMasterId = "TM_001",
                TeamMasterName = "Development Team",
                ResourceName = "Jane QA Engineer",
                ResourceId = "QA_001",
                Email = "<EMAIL>",
                Phone = "******-0102"
            }
        };

        TeamResourcePaginatedListVm = new PaginatedResult<TeamResourceListVm>
        {
            Data = TeamResourceListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        // Configure AutoMapper for TeamResource mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<TeamResourceProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoTeamResourceFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize TeamResource entity
            fixture.Customize<TeamResource>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.TeamMasterId, "TM_001")
                .With(b => b.TeamMasterName, "Test Team")
                .With(b => b.ResourceName, "Test Resource")
                .With(b => b.ResourceId, "RES_001")
                .With(b => b.Email, "<EMAIL>")
                .With(b => b.Phone, "******-0100"));

            // Customize CreateTeamResourceCommand
            fixture.Customize<CreateTeamResourceCommand>(c => c
                .With(b => b.TeamMasterId, "TM_NEW")
                .With(b => b.TeamMasterName, "New Test Team")
                .With(b => b.ResourceName, "New Test Resource")
                .With(b => b.ResourceId, "RES_NEW")
                .With(b => b.Email, "<EMAIL>")
                .With(b => b.Phone, "******-0200"));

            // Customize UpdateTeamResourceCommand
            fixture.Customize<UpdateTeamResourceCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.TeamMasterId, "TM_001")
                .With(b => b.TeamMasterName, "Updated Test Team")
                .With(b => b.ResourceName, "Updated Test Resource")
                .With(b => b.ResourceId, "RES_UPD")
                .With(b => b.Email, "<EMAIL>")
                .With(b => b.Phone, "******-0300"));

            // Customize DeleteTeamResourceCommand
            fixture.Customize<DeleteTeamResourceCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetTeamResourceDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetTeamResourceNameUniqueQuery>(c => c
                .With(b => b.ResourceName, "Test Resource")
                .With(b => b.ResourceId, "0"));

            fixture.Customize<GetTeamResourcePaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "ResourceName")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetTeamMasterIdByTeamResourceQuery>(c => c
                .With(b => b.TeamMasterId, "TM_001"));

            // Customize Responses
            fixture.Customize<CreateTeamResourceResponse>(c => c
                .With(b => b.Message, "TeamResource Created successfully."));

            fixture.Customize<UpdateTeamResourceResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "TeamResource 'Test Resource' has been updated successfully"));

            fixture.Customize<DeleteTeamResourceResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "TeamResource 'Test Resource' has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "TeamResource")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
