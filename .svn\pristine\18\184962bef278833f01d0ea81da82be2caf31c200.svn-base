﻿using ContinuityPatrol.Application.Features.ReplicationJob.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationJob.Events
{
    public class PaginatedReplicationJobEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<ILogger<ReplicationJobPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly ReplicationJobPaginatedEventHandler _handler;

        public PaginatedReplicationJobEventTests()
        {
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<ReplicationJobPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();

            _handler = new ReplicationJobPaginatedEventHandler(
                _mockLoggedInUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldAddUserActivityAndLogMessage_WhenEventIsHandled()
        {
            var paginatedEvent = new ReplicationJobPaginatedEvent();

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("TestUserId");
            _mockLoggedInUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockLoggedInUserService.Setup(service => service.RequestedUrl).Returns("http://localhost/api/replicationjob");
            _mockLoggedInUserService.Setup(service => service.CompanyId).Returns("TestCompanyId");
            _mockLoggedInUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "TestUserId" &&
                activity.LoginName == "TestUser" &&
                activity.RequestUrl == "http://localhost/api/replicationjob" &&
                activity.CompanyId == "TestCompanyId" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Entity == Modules.ReplicationJob.ToString() &&
                activity.Action == $"{ActivityType.View} {Modules.ReplicationJob}" &&
                activity.ActivityType == ActivityType.View.ToString() &&
                activity.ActivityDetails == "Replication Job viewed"
            )), Times.Once);

           
        }

    }
}
