﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetOracleMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class OracleMonitorStatusControllerTests : IClassFixture<OracleMonitorStatusFixture>
{
    private readonly OracleMonitorStatusFixture _fixture;
    private readonly OracleMonitorStatusController _controller;
    private readonly Mock<IMediator> _mediatorMock;

    public OracleMonitorStatusControllerTests(OracleMonitorStatusFixture fixture)
    {
        _fixture = fixture;

        var testBuilder = new ControllerTestBuilder<OracleMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new OracleMonitorStatusController(),
            out _mediatorMock
        );
    }

    [Fact]
    public async Task CreateOracleMonitorStatus_ReturnsCreated()
    {
        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.CreateResponse);

        var result = await _controller.CreateOracleMonitorStatus(_fixture.CreateCommand);

    }

    [Fact]
    public async Task UpdateOracleMonitorStatus_ReturnsUpdated()
    {
        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.UpdateResponse);

        var result = await _controller.UpdateOracleMonitorStatus(_fixture.UpdateCommand);

        
    }

    [Fact]
    public async Task GetAllOracleMonitorStatus_ReturnsList()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOracleMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ListVm);

        var result = await _controller.GetAllOracleMonitorStatus();

        var ok = Assert.IsType<OkObjectResult>(result.Result);
        var list = Assert.IsType<List<OracleMonitorStatusListVm>>(ok.Value);
        Assert.Equal(_fixture.ListVm.Count, list.Count);
    }

    [Fact]
    public async Task GetOracleMonitorStatusById_ReturnsDetail()
    {
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetOracleMonitorStatusDetailQuery>(q => q.Id == _fixture.DetailVm.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _controller.GetOracleLMonitorStatusById(id);

       
    }

    [Fact]
    public async Task GetPaginatedOracleMonitorStatus_ReturnsPaginatedList()
    {
        var query = new GetOracleMonitorStatusPaginatedListQuery { PageNumber = 1, PageSize = 10 };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new PaginatedResult<OracleMonitorStatusListVm> {});

        var result = await _controller.GetPaginatedOracleMonitorStatus(query);

       
    }

    [Fact]
    public async Task GetOracleMonitorStatusByType_ReturnsDetailByType()
    {
        var type = _fixture.DetailByTypeVm.Type;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetOracleMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OracleMonitorStatusDetailByTypeVm> {});

        var result = await _controller.GetOracleMonitorStatusByType(type);

        
    }

    [Fact]
    public async Task GetOracleMonitorStatusByInfraObjectId_ReturnsString()
    {
        var infraId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetOracleMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.InfraObjectIdResponse);

        var result = await _controller.GetOracleMonitorStatusByInfraObjectId(infraId);

        var ok = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Equal(_fixture.InfraObjectIdResponse, ok.Value);
    }
}
