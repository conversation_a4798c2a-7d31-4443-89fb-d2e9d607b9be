using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestBusinessServiceEvaluationSpecification : Specification<BusinessServiceEvaluation>
{
    public TestBusinessServiceEvaluationSpecification(string searchTerm)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.BusinessServiceName.Contains(searchTerm);
        }
    }
}

public class BusinessServiceEvaluationRepositoryTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceEvaluationRepository _repository;
    private readonly BusinessServiceEvaluationRepository _repositoryNotParent;

    public BusinessServiceEvaluationRepositoryTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceEvaluationRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new BusinessServiceEvaluationRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region Basic CRUD Tests

    [Fact]
    public async Task AddAsync_ShouldAddBusinessServiceEvaluation_Successfully()
    {
        // Arrange
        var businessServiceEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties();

        // Act
        var result = await _repository.AddAsync(businessServiceEvaluation);
        await _dbContext.SaveChangesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceEvaluation.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceEvaluation.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(businessServiceEvaluation.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(businessServiceEvaluation.Description, result.Description);
        Assert.Equal(businessServiceEvaluation.Grade, result.Grade);
        Assert.Equal(businessServiceEvaluation.GradeValue, result.GradeValue);
    }

    [Fact]
    public async Task GetBusinessServiceEvaluationByReferenceIdAsync_ShouldReturnBusinessServiceEvaluation_WhenExists()
    {
        // Arrange
        var businessServiceEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties();
        await _repository.AddAsync(businessServiceEvaluation);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetBusinessServiceEvaluationByReferenceIdAsync(businessServiceEvaluation.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceEvaluation.ReferenceId, result.ReferenceId);
        Assert.Equal(businessServiceEvaluation.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(businessServiceEvaluation.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(businessServiceEvaluation.Description, result.Description);
        Assert.Equal(businessServiceEvaluation.Grade, result.Grade);
        Assert.Equal(businessServiceEvaluation.GradeValue, result.GradeValue);
    }

    [Fact]
    public async Task GetBusinessServiceEvaluationByReferenceIdAsync_ShouldThrowNotFoundException_WhenNotExists()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(async () =>
            await _repository.GetBusinessServiceEvaluationByReferenceIdAsync(nonExistentId));
    }

    [Fact]
    public async Task GetBusinessServiceEvaluationByReferenceIdAsync_ShouldThrowArgumentException_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(async () =>
            await _repository.GetBusinessServiceEvaluationByReferenceIdAsync("INVALID_GUID"));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateBusinessServiceEvaluation_Successfully()
    {
        // Arrange
        var businessServiceEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties();
        await _repository.AddAsync(businessServiceEvaluation);
        await _dbContext.SaveChangesAsync();

        // Modify properties
        businessServiceEvaluation.BusinessServiceName = "Updated Service Name";
        businessServiceEvaluation.Description = "Updated Description";
        businessServiceEvaluation.Grade = "A+";
        businessServiceEvaluation.GradeValue = "95";

        // Act
        _dbContext.BusinessServiceEvaluations.Update(businessServiceEvaluation);
         _dbContext.SaveChanges();

        // Assert
        var result = await _repository.GetBusinessServiceEvaluationByReferenceIdAsync(businessServiceEvaluation.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
        Assert.Equal("Updated Description", result.Description);
        Assert.Equal("A+", result.Grade);
        Assert.Equal("95", result.GradeValue);
    }

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_Successfully()
    {
        // Arrange
        var businessServiceEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties();
        await _repository.AddAsync(businessServiceEvaluation);
        await _dbContext.SaveChangesAsync();

        // Act
        businessServiceEvaluation.IsActive = false;
       _dbContext.BusinessServiceEvaluations.Update(businessServiceEvaluation);
         _dbContext.SaveChanges();

        // Assert
        var result = await _repository.GetByReferenceIdAsync( businessServiceEvaluation.ReferenceId);
        Assert.NotNull(result);
        Assert.False(result.IsActive);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEvaluations_WhenIsAllInfra()
    {
        // Arrange
        var businessServiceEvaluations = new List<BusinessServiceEvaluation>
        {
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(isActive: false) // Inactive
        };

        foreach (var evaluation in businessServiceEvaluations)
        {
            await _repository.AddAsync(evaluation);
        }
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 2); // Only active evaluations
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAssignedBusinessServices_WhenNotAllInfra()
    {
        // Arrange
        var assignedEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
            businessServiceId: "c9b3cd51-f688-4667-be33-46f82b7086fa", // Should be included based on mock setup
            isActive: true);

        var nonAssignedEvaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
            businessServiceId: "NON_ASSIGNED_BS_002", // Should be excluded
            isActive: true);

        await _repositoryNotParent.AddAsync(assignedEvaluation);
        await _repositoryNotParent.AddAsync(nonAssignedEvaluation);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Should only return assigned business services based on mock configuration
        Assert.Contains(result, x => x.BusinessServiceId == "c9b3cd51-f688-4667-be33-46f82b7086fa");
    }

    #endregion

    #region AssignedBusinessServices Tests

    [Fact]
    public void AssignedBusinessServices_ShouldReturnEmpty_WhenBusinessServicesIsNull()
    {
        // Act
        var result = _repository.AssignedBusinessServices(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
    [Fact]
    public void AssignedBusinessServices_ShouldReturnEmpty_WhenAssignedBusinessServicesIsNull()
    {
        // Act

        var bsevalution=new List<BusinessServiceEvaluation>().AsQueryable();
        var _mocUserService = new Mock<ILoggedInUserService>() ;
        _mocUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>()
        }));

        _mocUserService.Setup(x => x.IsAuthenticated).Returns(true);

        var repository = new BusinessServiceEvaluationRepository(_dbContext, _mocUserService.Object);
        var result = repository.AssignedBusinessServices(bsevalution);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedBusinessServices_ShouldReturnFilteredServices_WhenAssignedBusinessServicesExist()
    {
        // Arrange
        var businessServiceEvaluations = new List<BusinessServiceEvaluation>
        {
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceId: "c9b3cd51-f688-4667-be33-46f82b7086fa",
                businessServiceName: "Assigned Service"),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceId: "NON_ASSIGNED_BS_002",
                businessServiceName: "Non-Assigned Service")
        }.AsQueryable();

        // Act
        var result = _repositoryNotParent.AssignedBusinessServices(businessServiceEvaluations);

        // Assert
        Assert.NotNull(result);
        // Should only return assigned business services based on mock configuration
        Assert.Contains(result, x => x.BusinessServiceId == "c9b3cd51-f688-4667-be33-46f82b7086fa");
    }

    #endregion

    #region Specification Tests

    [Fact]
    public async Task GetAsync_WithSpecification_ShouldReturnMatchingBusinessServiceEvaluations()
    {
        // Arrange
        var evaluations = new List<BusinessServiceEvaluation>
        {
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceName: "Enterprise Service", isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceName: "Basic Service", isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceName: "Enterprise Advanced", isActive: false)
        };

        foreach (var evaluation in evaluations)
        {
            await _repository.AddAsync(evaluation);
        }
        await _dbContext.SaveChangesAsync();

        var specification = new TestBusinessServiceEvaluationSpecification("Enterprise");

        // Act
        var result = await _repository.PaginatedListAllAsync(1,10,specification, "BusinessServiceName","Asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Data.Count >= 1);
        Assert.All(result.Data, evaluation =>
        {
            Assert.True(evaluation.IsActive);
            Assert.Contains("Enterprise", evaluation.BusinessServiceName);
        });
    }
    [Fact]
    public async Task GetBusinessServiceEvaluationByReferenceIdAsync_ShouldReturnMatchingBusinessServiceEvaluations()
    {
        // Arrange
        var evaluations = new List<BusinessServiceEvaluation>
        {
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(businessServiceId: "c9b3cd51-f688-4667-be33-46f82b7086fa",
                businessServiceName: "Enterprise Service", isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(businessServiceId: "c9b3cd51-f688-4667-be33-46f82b7086fa",
                businessServiceName: "Basic Service", isActive: true),
            _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceName: "Enterprise Advanced", isActive: false)
        };

        foreach (var evaluation in evaluations)
        {
            await _repository.AddAsync(evaluation);
        }
        await _dbContext.SaveChangesAsync();

        // Act

        var result = await _repositoryNotParent.GetBusinessServiceEvaluationByReferenceIdAsync(evaluations[0].ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("Enterprise", evaluations[0].BusinessServiceName);
     
    }

    #endregion

    #region Edge Cases and Business Logic Tests

    [Fact]
    public async Task AddAsync_ShouldHandleMultipleEvaluationsWithSameBusinessService()
    {
        // Arrange
        var evaluations = new List<BusinessServiceEvaluation>();
        for (int i = 0; i < 3; i++)
        {
            evaluations.Add(_businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithProperties(
                businessServiceId: BusinessServiceEvaluationFixture.BusinessServiceId,
                grade: $"Grade_{i}",
                gradeValue: (80 + i * 5).ToString()));
        }

        // Act
        foreach (var evaluation in evaluations)
        {
            await _repository.AddAsync(evaluation);
        }
        await _dbContext.SaveChangesAsync();

        // Assert
        var result = await _repository.ListAllAsync();
        var sameBusinessServiceEvaluations = result.Where(x => x.BusinessServiceId == BusinessServiceEvaluationFixture.BusinessServiceId).ToList();
        Assert.True(sameBusinessServiceEvaluations.Count >= 3);
    }

    [Fact]
    public async Task Repository_ShouldHandleGradeAndGradeValueCorrectly()
    {
        // Arrange
        var evaluation = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationWithSpecificGrade("A+", "95");

        // Act
        await _repository.AddAsync(evaluation);
        await _dbContext.SaveChangesAsync();

        // Assert
        var result = await _repository.GetBusinessServiceEvaluationByReferenceIdAsync(evaluation.ReferenceId);
        Assert.NotNull(result);
        Assert.Equal("A+", result.Grade);
        Assert.Equal("95", result.GradeValue);
    }

    #endregion
}
