﻿using ContinuityPatrol.Application.Features.LogViewer.Events.Create;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.LogViewer.Events
{
    public class CreateLogViewerEventHandlerTests
    {
        private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
        private readonly Mock<ILogger<LogViewerCreatedEventHandler>> _loggerMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly LogViewerCreatedEventHandler _handler;

        public CreateLogViewerEventHandlerTests()
        {
            _userActivityRepoMock = new Mock<IUserActivityRepository>();
            _loggerMock = new Mock<ILogger<LogViewerCreatedEventHandler>>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _userServiceMock.Setup(x => x.UserId).Returns("User123");
            _userServiceMock.Setup(x => x.LoginName).Returns("TestUser");
            _userServiceMock.Setup(x => x.CompanyId).Returns("Comp01");
            _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("/logviewer/create");

            _handler = new LogViewerCreatedEventHandler(_userServiceMock.Object, _loggerMock.Object, _userActivityRepoMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogAndAddUserActivity()
        {
            // Arrange
            var logViewerCreatedEvent = new LogViewerCreatedEvent
            {
                Name = "LogViewer-Test"
            };

            Domain.Entities.UserActivity capturedActivity = null;

            _userActivityRepoMock
                .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(logViewerCreatedEvent, CancellationToken.None);

            // Assert
            Assert.NotNull(capturedActivity);
            Assert.Equal("User123", capturedActivity.UserId);
            Assert.Equal("TestUser", capturedActivity.LoginName);
            Assert.Equal("Comp01", capturedActivity.CompanyId);
            Assert.Equal("LogViewer", capturedActivity.Entity);
            Assert.Equal("Create LogViewer", capturedActivity.Action);
            Assert.Equal(ActivityType.Create.ToString(), capturedActivity.ActivityType);
            Assert.Contains("LogViewer 'LogViewer-Test' created successfully", capturedActivity.ActivityDetails);

            _userActivityRepoMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);

            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("LogViewer 'LogViewer-Test' created successfully")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }


        [Fact]
        public async Task Handle_ShouldHandle_NullName_Gracefully()
        {
            // Arrange
            var logViewerCreatedEvent = new LogViewerCreatedEvent { Name = null };

            // Act
            await _handler.Handle(logViewerCreatedEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.ActivityDetails.Contains("LogViewer '' created successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrow_WhenNameIsEmpty()
        {
            // Arrange
            var logViewerCreatedEvent = new LogViewerCreatedEvent { Name = string.Empty };

            // Act
            var exception = await Record.ExceptionAsync(() => _handler.Handle(logViewerCreatedEvent, CancellationToken.None));

            // Assert
            Assert.Null(exception);
            _userActivityRepoMock.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.ActivityDetails == "LogViewer '' created successfully.")), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldUseDefaultValues_WhenUserInfoIsNullOrEmpty()
        {
            // Arrange
            _userServiceMock.Setup(x => x.UserId).Returns((string)null);
            _userServiceMock.Setup(x => x.LoginName).Returns((string)null);
            _userServiceMock.Setup(x => x.CompanyId).Returns((string)null);
            _userServiceMock.Setup(x => x.IpAddress).Returns((string)null);
            _userServiceMock.Setup(x => x.RequestedUrl).Returns((string)null);

            var logViewerCreatedEvent = new LogViewerCreatedEvent { Name = "FallbackLog" };

            // Act
            await _handler.Handle(logViewerCreatedEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == null &&
                ua.LoginName == null &&
                ua.CompanyId == null &&
                ua.RequestUrl == null &&
                ua.HostAddress == null &&
                ua.Entity == "LogViewer" &&
                ua.Action == "Create LogViewer"
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_LogInformationMessage_WithExactText()
        {
            // Arrange
            var createdEvent = new LogViewerCreatedEvent { Name = "LogX" };

            // Act
            await _handler.Handle(createdEvent, CancellationToken.None);

            // Assert: verify logging occurred
            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((obj, type) =>
                        obj.ToString().Contains("LogViewer 'LogX' created successfully.")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
}
