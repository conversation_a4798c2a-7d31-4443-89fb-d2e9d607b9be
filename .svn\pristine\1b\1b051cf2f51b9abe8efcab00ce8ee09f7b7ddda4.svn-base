﻿
    const sanitize = val => val?.replace(/\s+/g, '');
    const showError = (id, msg) => $(`#${id}-error`).text(msg).addClass('field-validation-error');
    const clearError = id => $(`#${id}-error`).text('').removeClass('field-validation-error');
    const CurrentPassword = async val => {
        if (!val) return showError('OldPassword', 'Enter current password'), false;
        clearError('OldPassword'); return true;
    };

    const NewPassword = async val => {
        if (!val) return showError('NewPassword', 'Enter new password'), false;
        clearError('NewPassword'); return true;
    };

    const ConfirmPassword = async val => {
        if (!val) return showError('ConfirmPassword', 'Enter confirm password'), false;
        clearError('ConfirmPassword'); return true;
    };
    $('#SaveFunction').on('click', async function () {
        const currentPassword = $('#CurrentPassword').val();
        const newPassword = $('#Password').val();
        const confirmPassword = $('#ConfirmPassword').val();
        const profileId = $('#selectWorkflowprofileName').val();

        $('#loginId').val(profileId);
        const isCurrentValid = await CurrentPassword(currentPassword);
        const isNewValid = await NewPassword(newPassword);
        const isConfirmValid = await ConfirmPassword(confirmPassword);

        if (profileId && isCurrentValid && isNewValid && isConfirmValid) {
            try {
                if (currentPassword.length < 30)
                    $('#CurrentPassword').val(await EncryptPassword(currentPassword));

                if (newPassword.length < 30)
                    $('#Password').val(await EncryptPassword(newPassword));

                if (confirmPassword.length < 30)
                    $('#ConfirmPassword').val(await EncryptPassword(confirmPassword));

                $(this).prop('disabled', true);
                $('#CreateForm').trigger('submit');
                $('#ProfileChangepasswordModal').modal('show');
            } catch (e) {
            }
        }
    });

    $(document).on('input keyup', '#CurrentPassword, #Password, #ConfirmPassword', function () {
        const id = this.id, val = sanitize(this.value || '');
        if (id === 'CurrentPassword') {
            inputpassword(id, val);
            val ? clearError('OldPassword') : showError('OldPassword', 'Enter current password');
        } else if (id === 'Password') {
            inputpassword(id, val);
            $('#ConfirmPassword').val('');            
            clearError('ConfirmPassword');
        } else if (id === 'ConfirmPassword') {
            inputConfirmpassword(id, val);
        }
    });

    $(document).on('blur', '#Password, #ConfirmPassword,#CurrentPassword', function () {
        if (this.value) blurpassword(this.id, sanitize(this.value));
    });

    $(document).on('focus', '#Password, #CurrentPassword, #ConfirmPassword', function () {
        this.id === 'ConfirmPassword' ? focusconfirmpassword(this.id) : focuspassword(this.id);
    });

    $('.toggle-password').on('click', async function () {
        const input = $(this).prev();
        const icon = $(this).find('i');
        if (input.attr('type') === 'password') {
            showPassword(input, icon);
            const val = input.val();
            if (val && val.length > 30)
                input.val(await onfocusPassword(val));
        } else {
            hidePassword(input, icon);
        }
    });
