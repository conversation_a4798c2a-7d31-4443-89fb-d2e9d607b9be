﻿using ContinuityPatrol.Application.Features.EscalationMatrix.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.EscalationMatrix.Events.Paginated;

public class EscalationMatrixPaginatedEventHandlerTests
{
    private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
    private readonly Mock<ILoggedInUserService> _userServiceMock;
    private readonly Mock<ILogger<EscalationMatrixPaginatedEventHandler>> _loggerMock;
    private readonly List<Domain.Entities.UserActivity> _userActivities;
    private readonly EscalationMatrixPaginatedEventHandler _handler;

    public EscalationMatrixPaginatedEventHandlerTests()
    {
        _userActivities = new List<Domain.Entities.UserActivity>();
        _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
        _userServiceMock = new Mock<ILoggedInUserService>();
        _userActivityRepositoryMock = EscalationMatrixRepositoryMocks.PaginatedEscalationMatrixEventRepository(_userActivities);
        _loggerMock = new Mock<ILogger<EscalationMatrixPaginatedEventHandler>>();

        _userServiceMock.Setup(x => x.UserId).Returns("user-123");
        _userServiceMock.Setup(x => x.LoginName).Returns("testuser");
        _userServiceMock.Setup(x => x.CompanyId).Returns("company-456");
        _userServiceMock.Setup(x => x.RequestedUrl).Returns("http://localhost/view");
        _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new EscalationMatrixPaginatedEventHandler(
            _userServiceMock.Object,
            _loggerMock.Object,
            _userActivityRepositoryMock.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldLogAndAddUserActivity_WhenPaginatedViewTriggered()
    {
        // Arrange
        var @event = new EscalationMatrixPaginatedEvent();

        // Act
        await _handler.Handle(@event, CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(repo =>
            repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "user-123" &&
                ua.LoginName == "testuser" &&
                ua.CompanyId == "company-456" &&
                ua.RequestUrl == "http://localhost/view" &&
                ua.HostAddress == "127.0.0.1" &&
                ua.Action == "View EscalationMatrix" &&
                ua.ActivityType == ActivityType.View.ToString() &&
                ua.ActivityDetails == "Escalation Matrix viewed"
            )), Times.Once);

        _loggerMock.Verify(logger =>
            logger.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Escalation Matrix viewed")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldSucceed_WhenUserId_IsEmpty()
    {
        // Arrange
        _userServiceMock.Setup(x => x.UserId).Returns(string.Empty);

        // Act
        await _handler.Handle(new EscalationMatrixPaginatedEvent(), CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldNotThrow_WhenLoggerFails()
    {
        // Arrange
        _loggerMock.Setup(logger =>
            logger.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            )).Throws(new Exception("Logger failed"));

        // Act
        var exception = await Record.ExceptionAsync(() =>
            _handler.Handle(new EscalationMatrixPaginatedEvent(), CancellationToken.None));

        // Assert
        Assert.Null(exception); // No exception should bubble up
        _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
