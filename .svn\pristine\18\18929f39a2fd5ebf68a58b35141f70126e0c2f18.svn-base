﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.PageSolutionMapping.Commands
{
    public class UpdatePageSolutionMappingTests
    {
        private readonly Mock<IPageSolutionMappingRepository> _mockPageSolutionMappingRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IMapper> _mockMapper;
        private readonly UpdatePageSolutionMappingCommandHandler _handler;

        public UpdatePageSolutionMappingTests()
        {
            _mockPageSolutionMappingRepository = new Mock<IPageSolutionMappingRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockMapper = new Mock<IMapper>();
            _handler = new UpdatePageSolutionMappingCommandHandler(_mockMapper.Object, _mockPageSolutionMappingRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenValidRequestIsProvided()
        {
            var request = new UpdatePageSolutionMappingCommand
            {
                Id = "valid-reference-id",
                Name = "UpdatedPageSolution"
            };

            var existingEntity = new Domain.Entities.PageSolutionMapping
            {
                ReferenceId = request.Id,
                Name = "OriginalPageSolution"
            };

            _mockPageSolutionMappingRepository
                .Setup(r => r.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync(existingEntity);

            _mockMapper
                .Setup(m => m.Map(request, existingEntity, typeof(UpdatePageSolutionMappingCommand), typeof(Domain.Entities.PageSolutionMapping)))
                .Callback(() =>
                {
                    existingEntity.Name = request.Name;
                });

            _mockPageSolutionMappingRepository
                .Setup(r => r.UpdateAsync(existingEntity))
                .ReturnsAsync(existingEntity);

            var expectedResponse = new UpdatePageSolutionMappingResponse
            {
                Message = Message.Update(nameof(PageSolutionMapping), existingEntity.Name),
                Id = existingEntity.ReferenceId
            };
            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedResponse.Id, result.Id);

            _mockPageSolutionMappingRepository.Verify(r => r.GetByReferenceIdAsync(request.Id), Times.Once);
            _mockMapper.Verify(m => m.Map(request, existingEntity, typeof(UpdatePageSolutionMappingCommand), typeof(Domain.Entities.PageSolutionMapping)), Times.Once);
            _mockPageSolutionMappingRepository.Verify(r => r.UpdateAsync(existingEntity), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageSolutionMappingUpdatedEvent>(), CancellationToken.None), Times.Once);
        }
        [Fact]
        public void Should_Assign_And_Assert_UpdatePageSolutionMappingCommand()
        {
            // Arrange
            var command = new UpdatePageSolutionMappingCommand
            {
                Id = "UPD001",
                Name = "UpdatedMap",
                PageBuilderId = "PB002",
                PageBuilderName = "UpdatedPage",
                MonitorType = "NoSQL",
                Type = 3,
                TypeName = "MongoDB",
                SubTypeId = "SUB002",
                SubType = "DocumentDB",
                ReplicationTypeId = "REP002",
                ReplicationTypeName = "Incremental",
                ReplicationCategoryTypeId = "RCT002",
                ReplicationCategoryType = "Optional"
            };

            // Assert
            Assert.Equal("UPD001", command.Id);
            Assert.Equal("UpdatedMap", command.Name);
            Assert.Equal("PB002", command.PageBuilderId);
            Assert.Equal("UpdatedPage", command.PageBuilderName);
            Assert.Equal("NoSQL", command.MonitorType);
            Assert.Equal(3, command.Type);
            Assert.Equal("MongoDB", command.TypeName);
            Assert.Equal("SUB002", command.SubTypeId);
            Assert.Equal("DocumentDB", command.SubType);
            Assert.Equal("REP002", command.ReplicationTypeId);
            Assert.Equal("Incremental", command.ReplicationTypeName);
            Assert.Equal("RCT002", command.ReplicationCategoryTypeId);
            Assert.Equal("Optional", command.ReplicationCategoryType);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
        {
            var request = new UpdatePageSolutionMappingCommand
            {
                Id = "invalid-reference-id",
                Name = "UpdatedPageSolution"
            };

            _mockPageSolutionMappingRepository
                .Setup(r => r.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync((Domain.Entities.PageSolutionMapping)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));

            _mockPageSolutionMappingRepository.Verify(r => r.GetByReferenceIdAsync(request.Id), Times.Once);
            _mockMapper.Verify(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<System.Type>(), It.IsAny<System.Type>()), Times.Never);
            _mockPageSolutionMappingRepository.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.PageSolutionMapping>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageSolutionMappingUpdatedEvent>(), CancellationToken.None), Times.Never);
        }
    }
}
