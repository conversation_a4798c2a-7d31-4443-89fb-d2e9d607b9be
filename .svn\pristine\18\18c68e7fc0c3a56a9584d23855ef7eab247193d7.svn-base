﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes
{
    public class AutoFiaImpactCategoryDataAttribute : AutoDataAttribute
    {
        public AutoFiaImpactCategoryDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFiaImpactCategoryCommand>(p => p.Name, 10));
            fixture.Customize<CreateFiaImpactCategoryCommand>(c => c.With(b => b.Name, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFiaImpactCategoryCommand>(p => p.Name, 10));
            fixture.Customize<UpdateFiaImpactCategoryCommand>(c => c.With(b => b.Id, 0.ToString));

            return fixture;
        })
        {

        }
    }
}
