using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Update;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraMasterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class InfraMasterControllerTests : IClassFixture<InfraMasterFixture>
{
    private readonly InfraMasterFixture _infraMasterFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly InfraMastersController _controller;

    public InfraMasterControllerTests(InfraMasterFixture infraMasterFixture)
    {
        _infraMasterFixture = infraMasterFixture;

        var testBuilder = new ControllerTestBuilder<InfraMastersController>();
        _controller = testBuilder.CreateController(
            _ => new InfraMastersController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetInfraMasters_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllInfraMasterCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraMasterListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_infraMasterFixture.InfraMasterListVm);

        // Act
        var result = await _controller.GetInfraMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraMasters = Assert.IsAssignableFrom<List<InfraMasterListVm>>(okResult.Value);
        Assert.Equal(3, infraMasters.Count);
    }

    [Fact]
    public async Task GetInfraMasterById_ReturnsInfraMaster_WhenIdIsValid()
    {
        // Arrange
        var infraMasterId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraMasterDetailQuery>(q => q.Id == infraMasterId), default))
            .ReturnsAsync(_infraMasterFixture.InfraMasterDetailVm);

        // Act
        var result = await _controller.GetInfraMasterById(infraMasterId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetInfraMasterById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetInfraMasterById("invalid-guid"));
    }

    [Fact]
    public async Task CreateInfraMaster_Returns201Created()
    {
        // Arrange
        var command = _infraMasterFixture.CreateInfraMasterCommand;
        var expectedMessage = $"InfraMaster '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraMasterResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateInfraMaster(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateInfraMasterResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateInfraMaster_ReturnsOk()
    {
        // Arrange
        var command = _infraMasterFixture.UpdateInfraMasterCommand;
        var expectedMessage = $"InfraMaster '{command.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraMasterResponse
            {
                Message = expectedMessage,
                Success = true
            });

        // Act
        var result = await _controller.UpdateInfraMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateInfraMasterResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteInfraMaster_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "InfraMaster 'TestInfra' has been deleted successfully!.";
        var infraMasterId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraMasterCommand>(c => c.Id == infraMasterId), default))
            .ReturnsAsync(new DeleteInfraMasterResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteInfraMaster(infraMasterId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteInfraMasterResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteInfraMaster_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteInfraMaster("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedInfraMasters_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetInfraMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _infraMasterFixture.InfraMasterListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraMasterPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<InfraMasterListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedInfraMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<InfraMasterListVm>>(okResult.Value);
        Assert.True(paginatedResult.Succeeded);
        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task GetInfraMasters_CallsCorrectQuery()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllInfraMasterCacheKey + companyId);

        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetInfraMasterListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<InfraMasterListVm>());

        // Act
        await _controller.GetInfraMasters();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateInfraMaster_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _infraMasterFixture.CreateInfraMasterCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateInfraMasterResponse
            {
                Message = "Created successfully",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateInfraMaster(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateInfraMaster_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _infraMasterFixture.UpdateInfraMasterCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateInfraMasterResponse
            {
                Message = "Updated successfully",
                Success = true
            });

        // Act
        await _controller.UpdateInfraMaster(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteInfraMaster_ClearsCacheAfterDeletion()
    {
        // Arrange
        var infraMasterId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteInfraMasterCommand>(c => c.Id == infraMasterId), default))
            .ReturnsAsync(new DeleteInfraMasterResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteInfraMaster(infraMasterId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task IsInfraMasterNameExist_ReturnsExpectedResult_WhenNameExists()
    {
        // Arrange
        var infraMasterName = "Existing Infrastructure";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraMasterNameUniqueQuery>(q =>
                q.Name == infraMasterName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsInfraMasterNameExist(infraMasterName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsInfraMasterNameExist_ReturnsExpectedResult_WhenNameDoesNotExist()
    {
        // Arrange
        var infraMasterName = "New Infrastructure";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraMasterNameUniqueQuery>(q =>
                q.Name == infraMasterName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsInfraMasterNameExist(infraMasterName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task IsInfraMasterNameExist_Throws_WhenNameIsEmpty()
    {
        // Arrange
        string infraMasterName = "";
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsInfraMasterNameExist(infraMasterName, id));
    }

    [Fact]
    public async Task GetPaginatedInfraMasters_WithSearchString_CallsCorrectQuery()
    {
        // Arrange
        var query = new GetInfraMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Network"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraMasterPaginatedListQuery>(q =>
                q.SearchString == "Network"), default))
            .ReturnsAsync(PaginatedResult<InfraMasterListVm>.Success(
                data: new List<InfraMasterListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        await _controller.GetPaginatedInfraMasters(query);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<GetInfraMasterPaginatedListQuery>(q =>
            q.SearchString == "Network"), default), Times.Once);
    }

    [Fact]
    public async Task GetPaginatedInfraMasters_WithSorting_CallsCorrectQuery()
    {
        // Arrange
        var query = new GetInfraMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SortColumn = "Name",
            SortOrder = "desc"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetInfraMasterPaginatedListQuery>(q =>
                q.SortColumn == "Name" && q.SortOrder == "desc"), default))
            .ReturnsAsync(PaginatedResult<InfraMasterListVm>.Success(
                data: new List<InfraMasterListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        await _controller.GetPaginatedInfraMasters(query);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<GetInfraMasterPaginatedListQuery>(q =>
            q.SortColumn == "Name" && q.SortOrder == "desc"), default), Times.Once);
    }
}