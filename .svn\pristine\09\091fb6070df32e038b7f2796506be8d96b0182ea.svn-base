﻿using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;

public class WorkflowDrCalenderSendEmailCommand:IRequest<WorkflowDrCalenderSendEmailResponse>
{
    public string ProfileId { get; set; }
    public string WorkflowOperationId { get; set; } 
    public string DrCalenderId { get; set; }
    public string ActivityDetail { get; set; }  
    public List<string> WorkflowIds { get; set; }
    public List<UserInfoDetailVm> ResponsibleUser { get; set; }
}
