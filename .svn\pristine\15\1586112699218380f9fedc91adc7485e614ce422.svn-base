﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Queries;

public class GetBusinessServiceNameQueryHandlerTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;
    private Mock<ISiteRepository> _mockSiteRepository;
    private readonly GetBusinessServiceNameQueryHandler _handler;

    public GetBusinessServiceNameQueryHandlerTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;
        foreach (var businessService in _businessServiceFixture.BusinessServices)
        {
            businessService.SiteProperties = @"{
            ""siteId"": ""test-site-001"",
            ""siteName"": ""Test Data Center"",
            ""environment"": ""Testing""
        }";
        }
        _mockSiteRepository = new Mock<ISiteRepository>();

        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetBusinessServiceNamesRepository(_businessServiceFixture.BusinessServices);

        _handler = new GetBusinessServiceNameQueryHandler(_mockBusinessServiceRepository.Object, _businessServiceFixture.Mapper, _mockSiteRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_BusinessServiceNamesCount()
    {
        var result = await _handler.Handle(new BusinessServiceNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceNameVm>>();
        result.Count.ShouldBe(_businessServiceFixture.BusinessServices.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetBusinessServiceEmptyRepository();

        var handler = new GetBusinessServiceNameQueryHandler(_mockBusinessServiceRepository.Object,_businessServiceFixture.Mapper,_mockSiteRepository.Object);

        var result = await handler.Handle(new BusinessServiceNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetBusinessServiceNamesMethod_OneTime()
    {
        await _handler.Handle(new BusinessServiceNameQuery(), CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.GetBusinessServiceNames(), Times.Once);
    }
}
