﻿using ContinuityPatrol.Application.Features.DataSet.Event.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Events;

public class DataSetPaginatedEventHandlerTests : IClassFixture<DataSetFixture>, IClassFixture<UserActivityFixture>
{
    private readonly DataSetFixture _dataSetFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly DataSetPaginatedEventHandler _handler;

    public DataSetPaginatedEventHandlerTests(DataSetFixture dataSetFixture, UserActivityFixture userActivityFixture)
    {
        _dataSetFixture = dataSetFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Admin");

        var mockDataSetEventLogger = new Mock<ILogger<DataSetPaginatedEventHandler>>();

        _mockUserActivityRepository = DataSetRepositoryMocks.CreateDataSetEventRepository(_userActivityFixture.UserActivities);

        _handler = new DataSetPaginatedEventHandler(mockLoggedInUserService.Object, mockDataSetEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_ViewDataSetEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Admin";

        var result = _handler.Handle(_dataSetFixture.DataSetPaginatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_dataSetFixture.DataSetPaginatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
