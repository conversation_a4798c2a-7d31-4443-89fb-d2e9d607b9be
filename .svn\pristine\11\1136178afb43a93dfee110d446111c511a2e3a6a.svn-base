﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetNameUnique;
using ContinuityPatrol.Services.Db.Impl.Orchestration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Orchestration;

public class WorkflowDrCalenderServiceTests : BaseServiceTestSetup<WorkflowDrCalenderService>, IClassFixture<WorkflowDrCalenderServiceFixture>
{
    private readonly WorkflowDrCalenderServiceFixture _fixture;

    public WorkflowDrCalenderServiceTests(WorkflowDrCalenderServiceFixture fixture)
    {
        InitializeService(accessor => new WorkflowDrCalenderService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetWorkflowDrCalenderList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowDrCalenderListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.WorkflowDrCalenderListVm);

        var result = await ServiceUnderTest.GetWorkflowDrCalenderList();

        Assert.Equal(_fixture.WorkflowDrCalenderListVm.Count, result.Count);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateWorkflowDrCalenderResponse
        {
            Message = "Created",
            Id = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.CreateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateWorkflowDrCalenderResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };
        MediatorMock.Setup(m => m.Send(_fixture.UpdateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Return_Success()
    {
        // Arrange
        var workflowDrCalenderId = Guid.NewGuid().ToString();

        var expectedResponse = new DeleteWorkflowDrCalenderResponse
        {
            Success = true,
            Message = "Deleted"
        };

        MediatorMock.Setup(m => m.Send(
            It.Is<DeleteWorkflowDrCalenderCommand>(cmd => cmd.Id == workflowDrCalenderId),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await ServiceUnderTest.DeleteAsync(workflowDrCalenderId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetPaginatedWorkflowDrCalenders_Should_Return_Result()
    {
        MediatorMock.Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await ServiceUnderTest.GetPaginatedWorkflowDrCalenders(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult.Data.Count, result.Data.Count);
    }

    [Fact]
    public async Task IsWorkflowDrCalenderNameExist_Should_Return_True()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowDrCalenderNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsWorkflowDrCalenderNameExist("SomeName", Guid.NewGuid().ToString());

        Assert.True(result);
    }

    [Fact]
    public async Task SendEmail_Should_Return_Success()
    {
        var response = new WorkflowDrCalenderSendEmailResponse
        {
            Message = "Email Sent"
        };
        MediatorMock.Setup(m => m.Send(_fixture.SendEmailCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.SendEmail(_fixture.SendEmailCommand);

        Assert.True(result.Success);
        Assert.Equal("Email Sent", result.Message);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_WorkflowDrCalenderDetailVm()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedVm = _fixture.WorkflowDrCalenderDetailVm;

        MediatorMock.Setup(m => m.Send(
            It.Is<GetWorkflowDrCalenderDetailQuery>(q => q.Id == id),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await ServiceUnderTest.GetByReferenceId(id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedVm.Id, result.Id);
        Assert.Equal(expectedVm.UserName, result.UserName); // assuming Name exists
    }
}
