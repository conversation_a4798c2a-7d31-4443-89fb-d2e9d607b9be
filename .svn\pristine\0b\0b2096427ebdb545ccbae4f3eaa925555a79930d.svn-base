﻿using ContinuityPatrol.Application.Features.Workflow.Events.PaginatedView;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Events;

public class WorkflowPaginatedEventTests
{
    private readonly Mock<ILogger<WorkflowPaginatedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly WorkflowPaginatedEventHandler _handler;

    public WorkflowPaginatedEventTests()
    {
        _mockLogger = new Mock<ILogger<WorkflowPaginatedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockUserService = new Mock<ILoggedInUserService>();

        _handler = new WorkflowPaginatedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object
        );
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_When_WorkflowPaginatedEvent_Is_Handled()
    {
        var eventData = new WorkflowPaginatedEvent();

        _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
        _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
        _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.IpAddress).Returns("***********");

        await _handler.Handle(eventData, CancellationToken.None);

        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
            activity.UserId == _mockUserService.Object.UserId &&
            activity.LoginName == _mockUserService.Object.LoginName &&
            activity.RequestUrl == _mockUserService.Object.RequestedUrl &&
            activity.CompanyId == _mockUserService.Object.CompanyId &&
            activity.HostAddress == _mockUserService.Object.IpAddress &&
            activity.Action == "View Workflow" &&
            activity.Entity == "Workflow" &&
            activity.ActivityType == "View" &&
            activity.ActivityDetails == "Workflow List viewed"
        )), Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Workflow List viewed")),
                It.IsAny<Exception>(),
                ((Func<It.IsAnyType, Exception, string>)It.IsAny<object>())!),
            Times.Once
        );

    }

    [Fact]
    public async Task Handle_Should_Log_Information_When_Workflow_List_Is_Viewed()
    {
        var eventData = new WorkflowPaginatedEvent();

        _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
        _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
        _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.IpAddress).Returns("***********");

        await _handler.Handle(eventData, CancellationToken.None);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Workflow List viewed")),
                It.IsAny<Exception>(),
                ((Func<It.IsAnyType, Exception, string>)It.IsAny<object>())!),
            Times.Once
        );

    }

    [Fact]
    public async Task Handle_Should_Call_AddAsync_Once_For_Valid_WorkflowPaginatedEvent()
    {
        var eventData = new WorkflowPaginatedEvent();

        _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
        _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
        _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());
        _mockUserService.Setup(s => s.IpAddress).Returns("***********");

        await _handler.Handle(eventData, CancellationToken.None);

        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
    [Fact]
    public void WorkflowName_Should_Be_Set_And_Retrieved_Correctly()
    {
        // Arrange
        var workflowEvent = new WorkflowPaginatedEvent
        {
            WorkflowName = "Sample Workflow"
        };

        // Act & Assert
        Assert.Equal("Sample Workflow", workflowEvent.WorkflowName);
    }
}