using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class BusinessServiceAvailabilityFixture
{
    public List<BusinessServiceAvailabilityListVm> BusinessServiceAvailabilityListVm { get; }
    public BusinessServiceDetailVm BusinessServiceDetailVm { get; }
    public CreateBusinessServiceAvailabilityCommand CreateBusinessServiceAvailabilityCommand { get; }
    public UpdateBusinessServiceAvailabilityCommand UpdateBusinessServiceAvailabilityCommand { get; }

    public BusinessServiceAvailabilityFixture()
    {
        var fixture = new Fixture();

        BusinessServiceAvailabilityListVm = fixture.Create<List<BusinessServiceAvailabilityListVm>>();
        BusinessServiceDetailVm = fixture.Create<BusinessServiceDetailVm>();
        CreateBusinessServiceAvailabilityCommand = fixture.Create<CreateBusinessServiceAvailabilityCommand>();
        UpdateBusinessServiceAvailabilityCommand = fixture.Create<UpdateBusinessServiceAvailabilityCommand>();
    }

    public void Dispose()
    {

    }
}
