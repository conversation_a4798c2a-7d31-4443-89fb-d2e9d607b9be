﻿using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Commands;


public class UpdateJobTests : IClassFixture<JobFixture>
{
    private readonly Mock<IJobRepository> _mockJobRepository;
    private readonly JobFixture _jobFixture;

    public UpdateJobTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;
        _mockJobRepository = JobRepositoryMocks.UpdateJobRepository(_jobFixture.Jobs);
    }

    private UpdateJobCommandHandler CreateHandlerWithAllDependencies(
        Mock<IJobScheduler> mockClient = null,
        Mock<IPublisher> mockPublisher = null,
        Mock<ILoadBalancerRepository> mockNodeRepo = null,
        Mock<ILogger<UpdateJobCommandHandler>> mockLogger = null
    )
    {
        return new UpdateJobCommandHandler(
            _jobFixture.Mapper,
            _mockJobRepository.Object,
            mockLogger?.Object ?? new Mock<ILogger<UpdateJobCommandHandler>>().Object,
            mockPublisher?.Object ?? new Mock<IPublisher>().Object,
            mockNodeRepo?.Object ?? new Mock<ILoadBalancerRepository>().Object,
            mockClient?.Object ?? new Mock<IJobScheduler>().Object
        );
    }
    [Fact]
    public void UpdateJobCommand_AssignPropertiesAndToString_ShouldSetCorrectlyAndReturnExpectedString()
    {
        // Arrange & Act
        var command = new UpdateJobCommand
        {
            Id = "job-001",
            Name = "UpdatedJob",
            InfraObjectProperties = "{\"env\":\"prod\"}",
            TemplateId = "template-123",
            TemplateName = "TemplateX",
            SolutionType = "Monitoring",
            SolutionTypeId = "sol-789",
            Type = "OnDemand",
            NodeId = "node-456",
            NodeName = "NodeB",
            Status = "Running",
            CronExpression = "0 12 * * *",
            ScheduleTime = "12:00",
            ScheduleType = 2,
            IsSchedule = 1,
            State = "Enabled",
            GroupPolicyId = "gp-987",
            GroupPolicyName = "PolicyA",
            ExecutionPolicy = "Parallel",
            LastExecutionTime = "2025-07-23T12:00:00",
            ExceptionMessage = "Error occurred"
        };

        // Assert
        command.Id.Should().Be("job-001");
        command.Name.Should().Be("UpdatedJob");
        command.InfraObjectProperties.Should().Be("{\"env\":\"prod\"}");
        command.TemplateId.Should().Be("template-123");
        command.TemplateName.Should().Be("TemplateX");
        command.SolutionType.Should().Be("Monitoring");
        command.SolutionTypeId.Should().Be("sol-789");
        command.Type.Should().Be("OnDemand");
        command.NodeId.Should().Be("node-456");
        command.NodeName.Should().Be("NodeB");
        command.Status.Should().Be("Running");
        command.CronExpression.Should().Be("0 12 * * *");
        command.ScheduleTime.Should().Be("12:00");
        command.ScheduleType.Should().Be(2);
        command.IsSchedule.Should().Be(1);
        command.State.Should().Be("Enabled");
        command.GroupPolicyId.Should().Be("gp-987");
        command.GroupPolicyName.Should().Be("PolicyA");
        command.ExecutionPolicy.Should().Be("Parallel");
        command.LastExecutionTime.Should().Be("2025-07-23T12:00:00");
        command.ExceptionMessage.Should().Be("Error occurred");
        command.ToString().Should().Be("Name: UpdatedJob; Id:job-001;");
    }

    [Fact]
    public async Task Handle_ValidJob_UpdateReferenceIdAsyncMethodToJobsRepo()
    {
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var handler = CreateHandlerWithAllDependencies();

        var result = await handler.Handle(command, CancellationToken.None);

        var job = await _mockJobRepository.Object.GetByReferenceIdAsync(result.JobId);

        Assert.Equal(command.Name, job.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidJobResponse()
    {
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var handler = CreateHandlerWithAllDependencies();

        var result = await handler.Handle(command, CancellationToken.None);

        result.ShouldBeOfType<UpdateJobResponse>();
        result.JobId.ShouldBe(command.Id);
        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidJobId()
    {
        var command = _jobFixture.UpdateJobCommand;
        command.Id = int.MaxValue.ToString(); // Non-existent job

        var handler = CreateHandlerWithAllDependencies();

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var handler = CreateHandlerWithAllDependencies();

        await handler.Handle(command, CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
        _mockJobRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Job>()), Times.Once);
    }
    [Fact]
    public async Task Handle_Should_Log_When_ScheduleJob_Throws_Exception()
    {
        // Arrange
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var mockClient = new Mock<IJobScheduler>();
        var mockPublisher = new Mock<IPublisher>();
        var mockNodeRepo = new Mock<ILoadBalancerRepository>();
        var mockLogger = new Mock<ILogger<UpdateJobCommandHandler>>();

        var validNode = new Domain.Entities.LoadBalancer
        {
            IPAddress = "127.0.0.1",
            Port = 80,
            ConnectionType = "http",
            TypeCategory = ServiceType.MonitorService.ToString()
        };

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync(validNode);

        mockClient.Setup(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                  .ThrowsAsync(new Exception("Test exception"));

        var handler = CreateHandlerWithAllDependencies(mockClient, mockPublisher, mockNodeRepo, mockLogger);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.JobId.ShouldBe(command.Id);

        mockLogger.Verify(x => x.Log(
            LogLevel.Information,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("throws an exception")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
    [Fact]
    public async Task Handle_Should_Skip_Scheduling_When_NodeConfig_Is_Null()
    {
        // Arrange
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var mockNodeRepo = new Mock<ILoadBalancerRepository>();
        var mockClient = new Mock<IJobScheduler>();

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        var handler = CreateHandlerWithAllDependencies(mockClient, null, mockNodeRepo);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        mockClient.Verify(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_Should_ScheduleJob_When_NodeConfig_Is_Valid()
    {
        // Arrange
        var command = _jobFixture.UpdateJobCommand;
        command.Id = _jobFixture.Jobs[0].ReferenceId;

        var mockNodeRepo = new Mock<ILoadBalancerRepository>();
        var mockClient = new Mock<IJobScheduler>();

        var validNode = new Domain.Entities.LoadBalancer
        {
            IPAddress = "127.0.0.1",
            Port = 8080,
            ConnectionType = "http",
            TypeCategory = ServiceType.MonitorService.ToString()
        };

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        mockNodeRepo.Setup(x =>
            x.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync(validNode);

        mockClient.Setup(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                  .Returns(Task.CompletedTask);

        var handler = CreateHandlerWithAllDependencies(mockClient, null, mockNodeRepo);

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.JobId.ShouldBe(command.Id);
        mockClient.Verify(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
    }

}